# System Architecture

## Overview

The AWS AI Security & Compliance Scanner is built using a serverless, event-driven architecture that leverages AWS cloud-native services for maximum scalability, security, and cost-effectiveness.

## High-Level Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────────┐
│   Web Frontend  │    │   Mobile App     │    │   API Clients       │
│   (React/Next)  │    │   (Future)       │    │   (CLI/SDK)         │
└─────────┬───────┘    └─────────┬────────┘    └──────────┬──────────┘
          │                      │                        │
          └──────────────────────┼────────────────────────┘
                                 │
                    ┌────────────▼────────────┐
                    │     CloudFront CDN      │
                    │   (Global Distribution) │
                    └────────────┬────────────┘
                                 │
                    ┌────────────▼────────────┐
                    │      API Gateway        │
                    │   (REST/WebSocket)      │
                    └────────────┬────────────┘
                                 │
          ┌──────────────────────┼──────────────────────┐
          │                      │                      │
┌─────────▼─────────┐  ┌─────────▼─────────┐  ┌─────────▼─────────┐
│   Auth Lambda     │  │  Upload Lambda    │  │  Scan Lambda      │
│   (Cognito)       │  │  (File Handler)   │  │  (Orchestrator)   │
└─────────┬─────────┘  └─────────┬─────────┘  └─────────┬─────────┘
          │                      │                      │
          │            ┌─────────▼─────────┐            │
          │            │       S3          │            │
          │            │  (File Storage)   │            │
          │            └─────────┬─────────┘            │
          │                      │                      │
          │                      │            ┌─────────▼─────────┐
          │                      │            │  Step Functions   │
          │                      │            │   (Workflow)      │
          │                      │            └─────────┬─────────┘
          │                      │                      │
          │                      │         ┌────────────┼────────────┐
          │                      │         │            │            │
          │                      │ ┌───────▼───────┐ ┌──▼──┐ ┌──────▼──────┐
          │                      │ │   AI/ML       │ │ PII │ │  Security   │
          │                      │ │   Analysis    │ │ Det │ │  Scanner    │
          │                      │ │   Lambda      │ │ ect │ │  Lambda     │
          │                      │ └───────┬───────┘ └──┬──┘ └──────┬──────┘
          │                      │         │            │           │
          │                      │         └────────────┼───────────┘
          │                      │                      │
          │                      │            ┌─────────▼─────────┐
          │                      │            │   Report Gen      │
          │                      │            │   Lambda          │
          │                      │            └─────────┬─────────┘
          │                      │                      │
┌─────────▼─────────┐            │            ┌─────────▼─────────┐
│    DynamoDB       │            │            │    DynamoDB       │
│  (User Data)      │            │            │  (Scan Results)   │
└───────────────────┘            │            └───────────────────┘
                                 │
                       ┌─────────▼─────────┐
                       │   CloudWatch      │
                       │ (Logs/Metrics)    │
                       └───────────────────┘
```

## Core Components

### 1. Frontend Layer
- **Technology**: React 18 with Next.js 14
- **Styling**: TailwindCSS for responsive design
- **State Management**: React Context + React Query
- **Authentication**: AWS Amplify with Cognito integration
- **File Upload**: Direct S3 upload with presigned URLs

### 2. API Gateway
- **Type**: REST API with WebSocket support for real-time updates
- **Features**: 
  - Request validation and transformation
  - Rate limiting and throttling
  - CORS configuration
  - API key management
  - Request/response logging

### 3. Lambda Functions

#### Authentication Lambda
- **Runtime**: Node.js 18
- **Purpose**: Handle Cognito integration, JWT validation
- **Triggers**: API Gateway requests

#### File Upload Lambda
- **Runtime**: Node.js 18
- **Purpose**: Generate presigned URLs, validate file types
- **Integrations**: S3, DynamoDB

#### Scan Orchestrator Lambda
- **Runtime**: Python 3.9
- **Purpose**: Initiate Step Functions workflow
- **Triggers**: S3 events, API Gateway

#### AI Analysis Lambda
- **Runtime**: Python 3.9
- **Purpose**: Coordinate AI/ML services
- **Integrations**: Comprehend, Rekognition, SageMaker, Bedrock

#### Security Scanner Lambda
- **Runtime**: Python 3.9
- **Purpose**: Custom security pattern detection
- **Features**: Regex patterns, signature matching

#### PII Detection Lambda
- **Runtime**: Python 3.9
- **Purpose**: Identify personally identifiable information
- **Integrations**: Amazon Comprehend

#### Report Generator Lambda
- **Runtime**: Python 3.9
- **Purpose**: Generate PDF reports, summaries
- **Libraries**: ReportLab, Jinja2

### 4. Step Functions Workflow

```json
{
  "Comment": "AI Security Scanner Workflow",
  "StartAt": "ValidateFile",
  "States": {
    "ValidateFile": {
      "Type": "Task",
      "Resource": "arn:aws:lambda:region:account:function:ValidateFile",
      "Next": "ParallelAnalysis"
    },
    "ParallelAnalysis": {
      "Type": "Parallel",
      "Branches": [
        {
          "StartAt": "SecurityScan",
          "States": {
            "SecurityScan": {
              "Type": "Task",
              "Resource": "arn:aws:lambda:region:account:function:SecurityScanner",
              "End": true
            }
          }
        },
        {
          "StartAt": "PIIDetection",
          "States": {
            "PIIDetection": {
              "Type": "Task",
              "Resource": "arn:aws:lambda:region:account:function:PIIDetector",
              "End": true
            }
          }
        },
        {
          "StartAt": "AnomalyDetection",
          "States": {
            "AnomalyDetection": {
              "Type": "Task",
              "Resource": "arn:aws:lambda:region:account:function:AnomalyDetector",
              "End": true
            }
          }
        }
      ],
      "Next": "GenerateReport"
    },
    "GenerateReport": {
      "Type": "Task",
      "Resource": "arn:aws:lambda:region:account:function:ReportGenerator",
      "End": true
    }
  }
}
```

### 5. Storage Layer

#### Amazon S3
- **Buckets**:
  - `scanner-uploads-{env}`: Temporary file storage
  - `scanner-reports-{env}`: Generated reports
  - `scanner-models-{env}`: ML model artifacts
- **Features**:
  - Server-side encryption (SSE-KMS)
  - Lifecycle policies for auto-deletion
  - Versioning enabled
  - Access logging

#### DynamoDB Tables
- **Users Table**: User profiles and preferences
- **Scans Table**: Scan metadata and status
- **Results Table**: Detailed scan results
- **Reports Table**: Generated report metadata

### 6. AI/ML Services

#### Amazon Comprehend
- **Use Cases**: PII detection, sentiment analysis, entity recognition
- **Features**: Custom entity recognition, document classification

#### Amazon Rekognition
- **Use Cases**: Image analysis, text extraction from images
- **Features**: Content moderation, face detection

#### Amazon SageMaker
- **Use Cases**: Custom ML models for anomaly detection
- **Models**: Isolation Forest, Autoencoders for outlier detection

#### Amazon Bedrock
- **Use Cases**: Natural language report generation
- **Models**: Claude, Titan for text generation

### 7. Security & Monitoring

#### AWS Cognito
- **Features**: User pools, identity pools, MFA
- **Integration**: Social login, SAML federation

#### AWS IAM
- **Principle**: Least privilege access
- **Features**: Role-based access, resource-based policies

#### AWS CloudWatch
- **Monitoring**: Metrics, logs, alarms
- **Dashboards**: Real-time system health

#### AWS X-Ray
- **Tracing**: Distributed request tracing
- **Performance**: Latency analysis, bottleneck identification

## Data Flow

1. **File Upload**:
   - User uploads file via frontend
   - Frontend requests presigned URL from API
   - File uploaded directly to S3
   - S3 event triggers scan orchestrator

2. **Scanning Process**:
   - Step Functions workflow initiated
   - Parallel execution of analysis functions
   - Results stored in DynamoDB
   - Real-time updates via WebSocket

3. **Report Generation**:
   - Aggregate results from all scanners
   - Generate executive summary using Bedrock
   - Create PDF report with detailed findings
   - Store report in S3, metadata in DynamoDB

4. **Cleanup**:
   - Automatic file deletion based on retention policy
   - Archive old scan results
   - Cleanup temporary resources

## Scalability Considerations

- **Auto-scaling**: Lambda functions scale automatically
- **Concurrency**: Step Functions handle parallel processing
- **Caching**: CloudFront for static assets, DynamoDB DAX for data
- **Multi-region**: Cross-region replication for disaster recovery

## Security Measures

- **Encryption**: End-to-end encryption (TLS + KMS)
- **Access Control**: IAM roles with least privilege
- **Network Security**: VPC endpoints, security groups
- **Audit**: CloudTrail for API calls, CloudWatch for monitoring
- **Compliance**: SOC 2, GDPR, HIPAA ready architecture
