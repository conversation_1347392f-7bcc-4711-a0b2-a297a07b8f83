{"name": "aws-ai-security-scanner-infrastructure", "version": "1.0.0", "description": "AWS CDK infrastructure for AI Security Scanner", "main": "lib/index.js", "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk", "deploy": "cdk deploy --all --require-approval never", "deploy:dev": "cdk deploy --all --require-approval never --context environment=dev", "deploy:prod": "cdk deploy --all --require-approval never --context environment=prod", "destroy": "cdk destroy --all", "diff": "cdk diff", "synth": "cdk synth", "bootstrap": "cdk bootstrap", "clean": "rimraf lib cdk.out node_modules", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "format": "prettier --write \"**/*.{ts,js,json,md}\""}, "devDependencies": {"@types/jest": "^29.5.8", "@types/node": "^20.9.0", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "aws-cdk": "^2.105.0", "eslint": "^8.53.0", "jest": "^29.7.0", "prettier": "^3.1.0", "rimraf": "^5.0.5", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "dependencies": {"aws-cdk-lib": "^2.105.0", "constructs": "^10.3.0"}, "keywords": ["aws", "cdk", "infrastructure", "security", "compliance", "ai", "serverless"], "author": "AWS AI Security Scanner Team", "license": "MIT"}