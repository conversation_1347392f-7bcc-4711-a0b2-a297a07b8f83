"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/aws-amplify";
exports.ids = ["vendor-chunks/aws-amplify"];
exports.modules = {

/***/ "(ssr)/./node_modules/aws-amplify/dist/esm/initSingleton.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/aws-amplify/dist/esm/initSingleton.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultAmplify: () => (/* binding */ DefaultAmplify)\n/* harmony export */ });\n/* harmony import */ var _aws_amplify_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @aws-amplify/core */ \"(ssr)/./node_modules/@aws-amplify/core/dist/esm/storage/CookieStorage.mjs\");\n/* harmony import */ var _aws_amplify_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @aws-amplify/core */ \"(ssr)/./node_modules/@aws-amplify/core/dist/esm/storage/index.mjs\");\n/* harmony import */ var _aws_amplify_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @aws-amplify/core */ \"(ssr)/./node_modules/@aws-amplify/core/dist/esm/singleton/Amplify.mjs\");\n/* harmony import */ var _aws_amplify_core_internals_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-amplify/core/internals/utils */ \"(ssr)/./node_modules/@aws-amplify/core/dist/esm/utils/parseAmplifyConfig.mjs\");\n/* harmony import */ var _aws_amplify_auth_cognito__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @aws-amplify/auth/cognito */ \"(ssr)/./node_modules/@aws-amplify/auth/dist/esm/providers/cognito/credentialsProvider/credentialsProvider.mjs\");\n/* harmony import */ var _aws_amplify_auth_cognito__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @aws-amplify/auth/cognito */ \"(ssr)/./node_modules/@aws-amplify/auth/dist/esm/providers/cognito/credentialsProvider/IdentityIdStore.mjs\");\n/* harmony import */ var _aws_amplify_auth_cognito__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @aws-amplify/auth/cognito */ \"(ssr)/./node_modules/@aws-amplify/auth/dist/esm/providers/cognito/credentialsProvider/index.mjs\");\n/* harmony import */ var _aws_amplify_auth_cognito__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @aws-amplify/auth/cognito */ \"(ssr)/./node_modules/@aws-amplify/auth/dist/esm/providers/cognito/tokenProvider/tokenProvider.mjs\");\n\n\n\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst DefaultAmplify = {\n    /**\n     * Configures Amplify with the {@link resourceConfig} and {@link libraryOptions}.\n     *\n     * @param resourceConfig The {@link ResourcesConfig} object that is typically imported from the\n     * `amplifyconfiguration.json` file. It can also be an object literal created inline when calling `Amplify.configure`.\n     * @param libraryOptions The {@link LibraryOptions} additional options for the library.\n     *\n     * @example\n     * import config from './amplifyconfiguration.json';\n     *\n     * Amplify.configure(config);\n     */\n    configure(resourceConfig, libraryOptions) {\n        const resolvedResourceConfig = (0,_aws_amplify_core_internals_utils__WEBPACK_IMPORTED_MODULE_0__.parseAmplifyConfig)(resourceConfig);\n        const cookieBasedKeyValueStorage = new _aws_amplify_core__WEBPACK_IMPORTED_MODULE_1__.CookieStorage({ sameSite: 'lax' });\n        const resolvedKeyValueStorage = libraryOptions?.ssr\n            ? cookieBasedKeyValueStorage\n            : _aws_amplify_core__WEBPACK_IMPORTED_MODULE_2__.defaultStorage;\n        const resolvedCredentialsProvider = libraryOptions?.ssr\n            ? new _aws_amplify_auth_cognito__WEBPACK_IMPORTED_MODULE_3__.CognitoAWSCredentialsAndIdentityIdProvider(new _aws_amplify_auth_cognito__WEBPACK_IMPORTED_MODULE_4__.DefaultIdentityIdStore(cookieBasedKeyValueStorage))\n            : _aws_amplify_auth_cognito__WEBPACK_IMPORTED_MODULE_5__.cognitoCredentialsProvider;\n        // If no Auth config is provided, no special handling will be required, configure as is.\n        // Otherwise, we can assume an Auth config is provided from here on.\n        if (!resolvedResourceConfig.Auth) {\n            _aws_amplify_core__WEBPACK_IMPORTED_MODULE_6__.Amplify.configure(resolvedResourceConfig, libraryOptions);\n            return;\n        }\n        // If Auth options are provided, always just configure as is.\n        // Otherwise, we can assume no Auth libraryOptions were provided from here on.\n        if (libraryOptions?.Auth) {\n            _aws_amplify_core__WEBPACK_IMPORTED_MODULE_6__.Amplify.configure(resolvedResourceConfig, libraryOptions);\n            return;\n        }\n        // If no Auth libraryOptions were previously configured, then always add default providers.\n        if (!_aws_amplify_core__WEBPACK_IMPORTED_MODULE_6__.Amplify.libraryOptions.Auth) {\n            _aws_amplify_auth_cognito__WEBPACK_IMPORTED_MODULE_7__.cognitoUserPoolsTokenProvider.setAuthConfig(resolvedResourceConfig.Auth);\n            _aws_amplify_auth_cognito__WEBPACK_IMPORTED_MODULE_7__.cognitoUserPoolsTokenProvider.setKeyValueStorage(\n            // TODO: allow configure with a public interface\n            resolvedKeyValueStorage);\n            _aws_amplify_core__WEBPACK_IMPORTED_MODULE_6__.Amplify.configure(resolvedResourceConfig, {\n                ...libraryOptions,\n                Auth: {\n                    tokenProvider: _aws_amplify_auth_cognito__WEBPACK_IMPORTED_MODULE_7__.cognitoUserPoolsTokenProvider,\n                    credentialsProvider: resolvedCredentialsProvider,\n                },\n            });\n            return;\n        }\n        // At this point, Auth libraryOptions would have been previously configured and no overriding\n        // Auth options were given, so we should preserve the currently configured Auth libraryOptions.\n        if (libraryOptions) {\n            const authLibraryOptions = _aws_amplify_core__WEBPACK_IMPORTED_MODULE_6__.Amplify.libraryOptions.Auth;\n            // If ssr is provided through libraryOptions, we should respect the intentional reconfiguration.\n            if (libraryOptions.ssr !== undefined) {\n                _aws_amplify_auth_cognito__WEBPACK_IMPORTED_MODULE_7__.cognitoUserPoolsTokenProvider.setKeyValueStorage(\n                // TODO: allow configure with a public interface\n                resolvedKeyValueStorage);\n                authLibraryOptions.credentialsProvider = resolvedCredentialsProvider;\n            }\n            _aws_amplify_core__WEBPACK_IMPORTED_MODULE_6__.Amplify.configure(resolvedResourceConfig, {\n                Auth: authLibraryOptions,\n                ...libraryOptions,\n            });\n            return;\n        }\n        // Finally, if there were no libraryOptions given at all, we should simply not touch the currently\n        // configured libraryOptions.\n        _aws_amplify_core__WEBPACK_IMPORTED_MODULE_6__.Amplify.configure(resolvedResourceConfig);\n    },\n    /**\n     * Returns the {@link ResourcesConfig} object passed in as the `resourceConfig` parameter when calling\n     * `Amplify.configure`.\n     *\n     * @returns An {@link ResourcesConfig} object.\n     */\n    getConfig() {\n        return _aws_amplify_core__WEBPACK_IMPORTED_MODULE_6__.Amplify.getConfig();\n    },\n};\n\n\n//# sourceMappingURL=initSingleton.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/aws-amplify/dist/esm/initSingleton.mjs\n");

/***/ })

};
;