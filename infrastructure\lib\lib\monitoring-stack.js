"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.MonitoringStack = void 0;
const cdk = __importStar(require("aws-cdk-lib"));
const cloudwatch = __importStar(require("aws-cdk-lib/aws-cloudwatch"));
const cloudwatchActions = __importStar(require("aws-cdk-lib/aws-cloudwatch-actions"));
const sns = __importStar(require("aws-cdk-lib/aws-sns"));
const snsSubscriptions = __importStar(require("aws-cdk-lib/aws-sns-subscriptions"));
class MonitoringStack extends cdk.Stack {
    constructor(scope, id, props) {
        super(scope, id, props);
        const { environment, config, apiGateway, lambdaFunctions, stepFunction } = props;
        // SNS Topic for alerts
        const alertTopic = new sns.Topic(this, 'AlertTopic', {
            topicName: `ai-scanner-alerts-${environment}`,
            displayName: `AI Scanner Alerts - ${environment}`
        });
        // Add email subscription for alerts (configure email in environment variables)
        if (process.env.ALERT_EMAIL) {
            alertTopic.addSubscription(new snsSubscriptions.EmailSubscription(process.env.ALERT_EMAIL));
        }
        // CloudWatch Dashboard
        const dashboard = new cloudwatch.Dashboard(this, 'AiScannerDashboard', {
            dashboardName: `ai-scanner-${environment}`,
            defaultInterval: cdk.Duration.hours(1)
        });
        // API Gateway Metrics
        const apiRequestsMetric = new cloudwatch.Metric({
            namespace: 'AWS/ApiGateway',
            metricName: 'Count',
            dimensionsMap: {
                ApiName: apiGateway.restApiName
            },
            statistic: 'Sum'
        });
        const apiLatencyMetric = new cloudwatch.Metric({
            namespace: 'AWS/ApiGateway',
            metricName: 'Latency',
            dimensionsMap: {
                ApiName: apiGateway.restApiName
            },
            statistic: 'Average'
        });
        const apiErrorsMetric = new cloudwatch.Metric({
            namespace: 'AWS/ApiGateway',
            metricName: '4XXError',
            dimensionsMap: {
                ApiName: apiGateway.restApiName
            },
            statistic: 'Sum'
        });
        const apiServerErrorsMetric = new cloudwatch.Metric({
            namespace: 'AWS/ApiGateway',
            metricName: '5XXError',
            dimensionsMap: {
                ApiName: apiGateway.restApiName
            },
            statistic: 'Sum'
        });
        // Lambda Metrics
        const lambdaMetrics = {};
        Object.entries(lambdaFunctions).forEach(([name, func]) => {
            lambdaMetrics[name] = [
                new cloudwatch.Metric({
                    namespace: 'AWS/Lambda',
                    metricName: 'Invocations',
                    dimensionsMap: { FunctionName: func.functionName },
                    statistic: 'Sum'
                }),
                new cloudwatch.Metric({
                    namespace: 'AWS/Lambda',
                    metricName: 'Duration',
                    dimensionsMap: { FunctionName: func.functionName },
                    statistic: 'Average'
                }),
                new cloudwatch.Metric({
                    namespace: 'AWS/Lambda',
                    metricName: 'Errors',
                    dimensionsMap: { FunctionName: func.functionName },
                    statistic: 'Sum'
                }),
                new cloudwatch.Metric({
                    namespace: 'AWS/Lambda',
                    metricName: 'Throttles',
                    dimensionsMap: { FunctionName: func.functionName },
                    statistic: 'Sum'
                })
            ];
        });
        // Step Functions Metrics
        const stepFunctionExecutionsMetric = new cloudwatch.Metric({
            namespace: 'AWS/States',
            metricName: 'ExecutionsStarted',
            dimensionsMap: {
                StateMachineArn: stepFunction.stateMachineArn
            },
            statistic: 'Sum'
        });
        const stepFunctionSuccessMetric = new cloudwatch.Metric({
            namespace: 'AWS/States',
            metricName: 'ExecutionsSucceeded',
            dimensionsMap: {
                StateMachineArn: stepFunction.stateMachineArn
            },
            statistic: 'Sum'
        });
        const stepFunctionFailedMetric = new cloudwatch.Metric({
            namespace: 'AWS/States',
            metricName: 'ExecutionsFailed',
            dimensionsMap: {
                StateMachineArn: stepFunction.stateMachineArn
            },
            statistic: 'Sum'
        });
        // Dashboard Widgets
        dashboard.addWidgets(
        // API Gateway Overview
        new cloudwatch.GraphWidget({
            title: 'API Gateway - Requests',
            left: [apiRequestsMetric],
            width: 12,
            height: 6
        }), new cloudwatch.GraphWidget({
            title: 'API Gateway - Latency & Errors',
            left: [apiLatencyMetric],
            right: [apiErrorsMetric, apiServerErrorsMetric],
            width: 12,
            height: 6
        }));
        // Lambda Functions Overview
        const lambdaInvocationsWidget = new cloudwatch.GraphWidget({
            title: 'Lambda - Invocations',
            left: Object.values(lambdaMetrics).map(metrics => metrics[0]),
            width: 12,
            height: 6
        });
        const lambdaDurationWidget = new cloudwatch.GraphWidget({
            title: 'Lambda - Duration',
            left: Object.values(lambdaMetrics).map(metrics => metrics[1]),
            width: 12,
            height: 6
        });
        const lambdaErrorsWidget = new cloudwatch.GraphWidget({
            title: 'Lambda - Errors & Throttles',
            left: Object.values(lambdaMetrics).map(metrics => metrics[2]),
            right: Object.values(lambdaMetrics).map(metrics => metrics[3]),
            width: 24,
            height: 6
        });
        dashboard.addWidgets(lambdaInvocationsWidget, lambdaDurationWidget);
        dashboard.addWidgets(lambdaErrorsWidget);
        // Step Functions Overview
        dashboard.addWidgets(new cloudwatch.GraphWidget({
            title: 'Step Functions - Executions',
            left: [stepFunctionExecutionsMetric, stepFunctionSuccessMetric],
            right: [stepFunctionFailedMetric],
            width: 24,
            height: 6
        }));
        // Alarms
        this.createAlarms(environment, alertTopic, apiGateway, lambdaFunctions, stepFunction);
        // CloudFormation Outputs
        new cdk.CfnOutput(this, 'DashboardUrl', {
            value: `https://${this.region}.console.aws.amazon.com/cloudwatch/home?region=${this.region}#dashboards:name=${dashboard.dashboardName}`,
            description: 'CloudWatch Dashboard URL'
        });
        new cdk.CfnOutput(this, 'AlertTopicArn', {
            value: alertTopic.topicArn,
            description: 'SNS Alert Topic ARN'
        });
    }
    createAlarms(environment, alertTopic, apiGateway, lambdaFunctions, stepFunction) {
        // API Gateway Alarms
        new cloudwatch.Alarm(this, 'ApiHighErrorRate', {
            alarmName: `ai-scanner-api-high-error-rate-${environment}`,
            alarmDescription: 'API Gateway high error rate',
            metric: new cloudwatch.Metric({
                namespace: 'AWS/ApiGateway',
                metricName: '4XXError',
                dimensionsMap: { ApiName: apiGateway.restApiName },
                statistic: 'Sum'
            }),
            threshold: 10,
            evaluationPeriods: 2,
            comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_THRESHOLD
        }).addAlarmAction(new cloudwatchActions.SnsAction(alertTopic));
        new cloudwatch.Alarm(this, 'ApiHighLatency', {
            alarmName: `ai-scanner-api-high-latency-${environment}`,
            alarmDescription: 'API Gateway high latency',
            metric: new cloudwatch.Metric({
                namespace: 'AWS/ApiGateway',
                metricName: 'Latency',
                dimensionsMap: { ApiName: apiGateway.restApiName },
                statistic: 'Average'
            }),
            threshold: 5000, // 5 seconds
            evaluationPeriods: 3,
            comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_THRESHOLD
        }).addAlarmAction(new cloudwatchActions.SnsAction(alertTopic));
        // Lambda Function Alarms
        Object.entries(lambdaFunctions).forEach(([name, func]) => {
            new cloudwatch.Alarm(this, `Lambda${name}Errors`, {
                alarmName: `ai-scanner-lambda-${name}-errors-${environment}`,
                alarmDescription: `Lambda function ${name} errors`,
                metric: new cloudwatch.Metric({
                    namespace: 'AWS/Lambda',
                    metricName: 'Errors',
                    dimensionsMap: { FunctionName: func.functionName },
                    statistic: 'Sum'
                }),
                threshold: 5,
                evaluationPeriods: 2,
                comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_THRESHOLD
            }).addAlarmAction(new cloudwatchActions.SnsAction(alertTopic));
            new cloudwatch.Alarm(this, `Lambda${name}Throttles`, {
                alarmName: `ai-scanner-lambda-${name}-throttles-${environment}`,
                alarmDescription: `Lambda function ${name} throttles`,
                metric: new cloudwatch.Metric({
                    namespace: 'AWS/Lambda',
                    metricName: 'Throttles',
                    dimensionsMap: { FunctionName: func.functionName },
                    statistic: 'Sum'
                }),
                threshold: 1,
                evaluationPeriods: 1,
                comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_OR_EQUAL_TO_THRESHOLD
            }).addAlarmAction(new cloudwatchActions.SnsAction(alertTopic));
        });
        // Step Functions Alarms
        new cloudwatch.Alarm(this, 'StepFunctionFailures', {
            alarmName: `ai-scanner-stepfunction-failures-${environment}`,
            alarmDescription: 'Step Functions execution failures',
            metric: new cloudwatch.Metric({
                namespace: 'AWS/States',
                metricName: 'ExecutionsFailed',
                dimensionsMap: { StateMachineArn: stepFunction.stateMachineArn },
                statistic: 'Sum'
            }),
            threshold: 1,
            evaluationPeriods: 1,
            comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_OR_EQUAL_TO_THRESHOLD
        }).addAlarmAction(new cloudwatchActions.SnsAction(alertTopic));
    }
}
exports.MonitoringStack = MonitoringStack;
//# sourceMappingURL=data:application/json;base64,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