import * as cdk from 'aws-cdk-lib';
import * as cloudwatch from 'aws-cdk-lib/aws-cloudwatch';
import * as cloudwatchActions from 'aws-cdk-lib/aws-cloudwatch-actions';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import * as stepfunctions from 'aws-cdk-lib/aws-stepfunctions';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as snsSubscriptions from 'aws-cdk-lib/aws-sns-subscriptions';
import * as logs from 'aws-cdk-lib/aws-logs';
import { Construct } from 'constructs';

export interface MonitoringStackProps extends cdk.StackProps {
  environment: string;
  config: any;
  apiGateway: apigateway.RestApi;
  lambdaFunctions: { [key: string]: lambda.Function };
  stepFunction: stepfunctions.StateMachine;
}

export class MonitoringStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: MonitoringStackProps) {
    super(scope, id, props);

    const { environment, config, apiGateway, lambdaFunctions, stepFunction } = props;

    // SNS Topic for alerts
    const alertTopic = new sns.Topic(this, 'AlertTopic', {
      topicName: `ai-scanner-alerts-${environment}`,
      displayName: `AI Scanner Alerts - ${environment}`
    });

    // Add email subscription for alerts (configure email in environment variables)
    if (process.env.ALERT_EMAIL) {
      alertTopic.addSubscription(new snsSubscriptions.EmailSubscription(process.env.ALERT_EMAIL));
    }

    // CloudWatch Dashboard
    const dashboard = new cloudwatch.Dashboard(this, 'AiScannerDashboard', {
      dashboardName: `ai-scanner-${environment}`,
      defaultInterval: cdk.Duration.hours(1)
    });

    // API Gateway Metrics
    const apiRequestsMetric = new cloudwatch.Metric({
      namespace: 'AWS/ApiGateway',
      metricName: 'Count',
      dimensionsMap: {
        ApiName: apiGateway.restApiName
      },
      statistic: 'Sum'
    });

    const apiLatencyMetric = new cloudwatch.Metric({
      namespace: 'AWS/ApiGateway',
      metricName: 'Latency',
      dimensionsMap: {
        ApiName: apiGateway.restApiName
      },
      statistic: 'Average'
    });

    const apiErrorsMetric = new cloudwatch.Metric({
      namespace: 'AWS/ApiGateway',
      metricName: '4XXError',
      dimensionsMap: {
        ApiName: apiGateway.restApiName
      },
      statistic: 'Sum'
    });

    const apiServerErrorsMetric = new cloudwatch.Metric({
      namespace: 'AWS/ApiGateway',
      metricName: '5XXError',
      dimensionsMap: {
        ApiName: apiGateway.restApiName
      },
      statistic: 'Sum'
    });

    // Lambda Metrics
    const lambdaMetrics: { [key: string]: cloudwatch.Metric[] } = {};
    Object.entries(lambdaFunctions).forEach(([name, func]) => {
      lambdaMetrics[name] = [
        new cloudwatch.Metric({
          namespace: 'AWS/Lambda',
          metricName: 'Invocations',
          dimensionsMap: { FunctionName: func.functionName },
          statistic: 'Sum'
        }),
        new cloudwatch.Metric({
          namespace: 'AWS/Lambda',
          metricName: 'Duration',
          dimensionsMap: { FunctionName: func.functionName },
          statistic: 'Average'
        }),
        new cloudwatch.Metric({
          namespace: 'AWS/Lambda',
          metricName: 'Errors',
          dimensionsMap: { FunctionName: func.functionName },
          statistic: 'Sum'
        }),
        new cloudwatch.Metric({
          namespace: 'AWS/Lambda',
          metricName: 'Throttles',
          dimensionsMap: { FunctionName: func.functionName },
          statistic: 'Sum'
        })
      ];
    });

    // Step Functions Metrics
    const stepFunctionExecutionsMetric = new cloudwatch.Metric({
      namespace: 'AWS/States',
      metricName: 'ExecutionsStarted',
      dimensionsMap: {
        StateMachineArn: stepFunction.stateMachineArn
      },
      statistic: 'Sum'
    });

    const stepFunctionSuccessMetric = new cloudwatch.Metric({
      namespace: 'AWS/States',
      metricName: 'ExecutionsSucceeded',
      dimensionsMap: {
        StateMachineArn: stepFunction.stateMachineArn
      },
      statistic: 'Sum'
    });

    const stepFunctionFailedMetric = new cloudwatch.Metric({
      namespace: 'AWS/States',
      metricName: 'ExecutionsFailed',
      dimensionsMap: {
        StateMachineArn: stepFunction.stateMachineArn
      },
      statistic: 'Sum'
    });

    // Dashboard Widgets
    dashboard.addWidgets(
      // API Gateway Overview
      new cloudwatch.GraphWidget({
        title: 'API Gateway - Requests',
        left: [apiRequestsMetric],
        width: 12,
        height: 6
      }),
      new cloudwatch.GraphWidget({
        title: 'API Gateway - Latency & Errors',
        left: [apiLatencyMetric],
        right: [apiErrorsMetric, apiServerErrorsMetric],
        width: 12,
        height: 6
      })
    );

    // Lambda Functions Overview
    const lambdaInvocationsWidget = new cloudwatch.GraphWidget({
      title: 'Lambda - Invocations',
      left: Object.values(lambdaMetrics).map(metrics => metrics[0]),
      width: 12,
      height: 6
    });

    const lambdaDurationWidget = new cloudwatch.GraphWidget({
      title: 'Lambda - Duration',
      left: Object.values(lambdaMetrics).map(metrics => metrics[1]),
      width: 12,
      height: 6
    });

    const lambdaErrorsWidget = new cloudwatch.GraphWidget({
      title: 'Lambda - Errors & Throttles',
      left: Object.values(lambdaMetrics).map(metrics => metrics[2]),
      right: Object.values(lambdaMetrics).map(metrics => metrics[3]),
      width: 24,
      height: 6
    });

    dashboard.addWidgets(lambdaInvocationsWidget, lambdaDurationWidget);
    dashboard.addWidgets(lambdaErrorsWidget);

    // Step Functions Overview
    dashboard.addWidgets(
      new cloudwatch.GraphWidget({
        title: 'Step Functions - Executions',
        left: [stepFunctionExecutionsMetric, stepFunctionSuccessMetric],
        right: [stepFunctionFailedMetric],
        width: 24,
        height: 6
      })
    );

    // Alarms
    this.createAlarms(environment, alertTopic, apiGateway, lambdaFunctions, stepFunction);

    // CloudFormation Outputs
    new cdk.CfnOutput(this, 'DashboardUrl', {
      value: `https://${this.region}.console.aws.amazon.com/cloudwatch/home?region=${this.region}#dashboards:name=${dashboard.dashboardName}`,
      description: 'CloudWatch Dashboard URL'
    });

    new cdk.CfnOutput(this, 'AlertTopicArn', {
      value: alertTopic.topicArn,
      description: 'SNS Alert Topic ARN'
    });
  }

  private createAlarms(
    environment: string,
    alertTopic: sns.Topic,
    apiGateway: apigateway.RestApi,
    lambdaFunctions: { [key: string]: lambda.Function },
    stepFunction: stepfunctions.StateMachine
  ): void {
    // API Gateway Alarms
    new cloudwatch.Alarm(this, 'ApiHighErrorRate', {
      alarmName: `ai-scanner-api-high-error-rate-${environment}`,
      alarmDescription: 'API Gateway high error rate',
      metric: new cloudwatch.Metric({
        namespace: 'AWS/ApiGateway',
        metricName: '4XXError',
        dimensionsMap: { ApiName: apiGateway.restApiName },
        statistic: 'Sum'
      }),
      threshold: 10,
      evaluationPeriods: 2,
      comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_THRESHOLD
    }).addAlarmAction(new cloudwatchActions.SnsAction(alertTopic));

    new cloudwatch.Alarm(this, 'ApiHighLatency', {
      alarmName: `ai-scanner-api-high-latency-${environment}`,
      alarmDescription: 'API Gateway high latency',
      metric: new cloudwatch.Metric({
        namespace: 'AWS/ApiGateway',
        metricName: 'Latency',
        dimensionsMap: { ApiName: apiGateway.restApiName },
        statistic: 'Average'
      }),
      threshold: 5000, // 5 seconds
      evaluationPeriods: 3,
      comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_THRESHOLD
    }).addAlarmAction(new cloudwatchActions.SnsAction(alertTopic));

    // Lambda Function Alarms
    Object.entries(lambdaFunctions).forEach(([name, func]) => {
      new cloudwatch.Alarm(this, `Lambda${name}Errors`, {
        alarmName: `ai-scanner-lambda-${name}-errors-${environment}`,
        alarmDescription: `Lambda function ${name} errors`,
        metric: new cloudwatch.Metric({
          namespace: 'AWS/Lambda',
          metricName: 'Errors',
          dimensionsMap: { FunctionName: func.functionName },
          statistic: 'Sum'
        }),
        threshold: 5,
        evaluationPeriods: 2,
        comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_THRESHOLD
      }).addAlarmAction(new cloudwatchActions.SnsAction(alertTopic));

      new cloudwatch.Alarm(this, `Lambda${name}Throttles`, {
        alarmName: `ai-scanner-lambda-${name}-throttles-${environment}`,
        alarmDescription: `Lambda function ${name} throttles`,
        metric: new cloudwatch.Metric({
          namespace: 'AWS/Lambda',
          metricName: 'Throttles',
          dimensionsMap: { FunctionName: func.functionName },
          statistic: 'Sum'
        }),
        threshold: 1,
        evaluationPeriods: 1,
        comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_OR_EQUAL_TO_THRESHOLD
      }).addAlarmAction(new cloudwatchActions.SnsAction(alertTopic));
    });

    // Step Functions Alarms
    new cloudwatch.Alarm(this, 'StepFunctionFailures', {
      alarmName: `ai-scanner-stepfunction-failures-${environment}`,
      alarmDescription: 'Step Functions execution failures',
      metric: new cloudwatch.Metric({
        namespace: 'AWS/States',
        metricName: 'ExecutionsFailed',
        dimensionsMap: { StateMachineArn: stepFunction.stateMachineArn },
        statistic: 'Sum'
      }),
      threshold: 1,
      evaluationPeriods: 1,
      comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_OR_EQUAL_TO_THRESHOLD
    }).addAlarmAction(new cloudwatchActions.SnsAction(alertTopic));
  }
}
