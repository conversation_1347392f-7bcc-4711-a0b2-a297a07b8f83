// User types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  isEmailVerified: boolean;
  createdAt: string;
  lastLoginAt?: string;
  scanCount: number;
}

// Scan types
export type ScanType = 'security' | 'compliance' | 'anomaly' | 'full';
export type ScanStatus = 'pending_upload' | 'uploaded' | 'processing' | 'completed' | 'failed' | 'cancelled';
export type RiskLevel = 'MINIMAL' | 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';

export interface ScanRequest {
  fileName: string;
  fileSize: number;
  mimeType: string;
  scanType: ScanType;
  description?: string;
}

export interface Scan {
  scanId: string;
  userId: string;
  fileName: string;
  fileSize: number;
  mimeType: string;
  scanType: ScanType;
  status: ScanStatus;
  description?: string;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  progress: number;
  message: string;
  reportUrl?: string;
  results?: ScanResults;
}

// Scan results types
export interface ScanResults {
  security?: SecurityResults;
  pii?: PIIResults;
  anomaly?: AnomalyResults;
  report?: ReportResults;
}

export interface SecurityResults {
  scanType: 'security';
  status: 'completed' | 'failed';
  riskScore: number;
  riskLevel: RiskLevel;
  findings: SecurityFindings;
  summary: SecuritySummary;
  completedAt: string;
  error?: string;
}

export interface SecurityFindings {
  [category: string]: SecurityFinding[];
}

export interface SecurityFinding {
  pattern: string;
  match: string;
  start: number;
  end: number;
  line_number: number;
  context: string;
}

export interface SecuritySummary {
  totalFindings: number;
  categoriesFound: string[];
  hasCredentials: boolean;
  hasMalware: boolean;
}

export interface PIIResults {
  scanType: 'pii';
  status: 'completed' | 'failed';
  riskScore: number;
  riskLevel: RiskLevel;
  piiFindings: PIIFindings;
  complianceViolations: ComplianceViolations;
  summary: PIISummary;
  completedAt: string;
  error?: string;
}

export interface PIIFindings {
  [piiType: string]: PIIFinding[];
}

export interface PIIFinding {
  type: string;
  value: string;
  start: number;
  end: number;
  confidence: number;
  source: 'comprehend' | 'pattern';
  line_number: number;
  context: string;
}

export interface ComplianceViolations {
  [framework: string]: ComplianceViolation;
}

export interface ComplianceViolation {
  description: string;
  violation_level: RiskLevel;
  severity_score: number;
  violations: FrameworkViolation[];
  total_pii_instances: number;
}

export interface FrameworkViolation {
  pii_type: string;
  count: number;
  severity_weight: number;
  description: string;
}

export interface PIISummary {
  totalPiiInstances: number;
  piiTypesFound: string[];
  complianceFrameworksViolated: string[];
  highestViolationLevel: RiskLevel;
}

export interface AnomalyResults {
  scanType: 'anomaly';
  status: 'completed' | 'failed';
  riskScore: number;
  riskLevel: RiskLevel;
  textStatistics: TextStatistics;
  anomalies: AnomalyFindings;
  summary: AnomalySummary;
  completedAt: string;
  error?: string;
}

export interface TextStatistics {
  total_characters: number;
  total_lines: number;
  total_words: number;
  avg_line_length: number;
  avg_word_length: number;
  entropy: number;
  most_common_chars: [string, number][];
  most_common_words: [string, number][];
}

export interface AnomalyFindings {
  statistical: Anomaly[];
  sentiment: Anomaly[];
  pattern: Anomaly[];
  ml_detected: Anomaly[];
}

export interface Anomaly {
  type: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  description: string;
  value?: any;
  threshold?: any;
  [key: string]: any;
}

export interface AnomalySummary {
  totalAnomalies: number;
  anomalyTypes: string[];
  highestSeverity: string;
  hasStatisticalAnomalies: boolean;
  hasSentimentAnomalies: boolean;
  hasPatternAnomalies: boolean;
  hasMlAnomalies: boolean;
}

export interface ReportResults {
  scanType: 'report';
  status: 'completed' | 'failed';
  reportUrl: string;
  executiveSummary: string;
  overallRiskLevel: RiskLevel;
  recommendations: string[];
  completedAt: string;
  error?: string;
}

// API response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Upload types
export interface UploadResponse {
  scanId: string;
  uploadUrl: string;
  expiresIn: number;
  maxFileSize: number;
  allowedTypes: string[];
  message: string;
}

// Auth types
export interface AuthTokens {
  accessToken: string;
  idToken: string;
  refreshToken: string;
}

export interface SignInResponse {
  message: string;
  tokens: AuthTokens;
  expiresIn: number;
}

export interface SignUpResponse {
  message: string;
  userId: string;
  userSub: string;
}

// Error types
export interface AppError {
  code: string;
  message: string;
  details?: any;
}

// UI state types
export interface LoadingState {
  [key: string]: boolean;
}

export interface ErrorState {
  [key: string]: string | null;
}

// Form types
export interface SignInForm {
  email: string;
  password: string;
}

export interface SignUpForm {
  email: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
}

export interface ConfirmSignUpForm {
  email: string;
  confirmationCode: string;
}

export interface UploadForm {
  file: File | null;
  scanType: ScanType;
  description: string;
}

// Component props types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface ButtonProps extends BaseComponentProps {
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
}

export interface InputProps extends BaseComponentProps {
  type?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  error?: string;
  disabled?: boolean;
  required?: boolean;
}

export interface ModalProps extends BaseComponentProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

// Chart data types
export interface ChartData {
  name: string;
  value: number;
  color?: string;
}

export interface TimeSeriesData {
  timestamp: string;
  value: number;
  label?: string;
}

// Notification types
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  label: string;
  action: () => void;
}

// Theme types
export interface Theme {
  colors: {
    primary: string;
    secondary: string;
    success: string;
    warning: string;
    danger: string;
    gray: string;
  };
  fonts: {
    sans: string;
    mono: string;
  };
  spacing: {
    [key: string]: string;
  };
}
