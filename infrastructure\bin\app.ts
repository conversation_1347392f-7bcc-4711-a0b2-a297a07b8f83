#!/usr/bin/env node
import 'source-map-support/register';
import * as cdk from 'aws-cdk-lib';
import { AiSecurityScannerStack } from '../lib/ai-security-scanner-stack';
import { FrontendStack } from '../lib/frontend-stack';
import { MonitoringStack } from '../lib/monitoring-stack';

const app = new cdk.App();

// Get environment from context
const environment = app.node.tryGetContext('environment') || 'dev';
const account = process.env.CDK_DEFAULT_ACCOUNT;
const region = process.env.CDK_DEFAULT_REGION || 'us-east-1';

// Environment configuration
const envConfig = {
  dev: {
    domainName: undefined, // Use CloudFront default domain
    certificateArn: undefined,
    enableWaf: false,
    retentionDays: 7,
    logLevel: 'DEBUG'
  },
  prod: {
    domainName: 'ai-security-scanner.com',
    certificateArn: process.env.CERTIFICATE_ARN,
    enableWaf: true,
    retentionDays: 30,
    logLevel: 'INFO'
  }
};

const config = envConfig[environment as keyof typeof envConfig];

// Stack naming convention
const stackPrefix = `AiSecurityScanner-${environment}`;

// Main backend stack
const backendStack = new AiSecurityScannerStack(app, `${stackPrefix}-Backend`, {
  env: { account, region },
  environment,
  config,
  description: `AI Security Scanner Backend Stack - ${environment}`,
  tags: {
    Environment: environment,
    Project: 'AiSecurityScanner',
    Owner: 'DevOps',
    CostCenter: 'Engineering'
  }
});

// Frontend stack (depends on backend)
const frontendStack = new FrontendStack(app, `${stackPrefix}-Frontend`, {
  env: { account, region },
  environment,
  config,
  apiGatewayUrl: backendStack.apiGatewayUrl,
  userPoolId: backendStack.userPoolId,
  userPoolClientId: backendStack.userPoolClientId,
  identityPoolId: backendStack.identityPoolId,
  description: `AI Security Scanner Frontend Stack - ${environment}`,
  tags: {
    Environment: environment,
    Project: 'AiSecurityScanner',
    Owner: 'DevOps',
    CostCenter: 'Engineering'
  }
});

// Monitoring stack (depends on backend)
const monitoringStack = new MonitoringStack(app, `${stackPrefix}-Monitoring`, {
  env: { account, region },
  environment,
  config,
  apiGateway: backendStack.apiGateway,
  lambdaFunctions: backendStack.lambdaFunctions,
  stepFunction: backendStack.stepFunction,
  description: `AI Security Scanner Monitoring Stack - ${environment}`,
  tags: {
    Environment: environment,
    Project: 'AiSecurityScanner',
    Owner: 'DevOps',
    CostCenter: 'Engineering'
  }
});

// Add dependencies
frontendStack.addDependency(backendStack);
monitoringStack.addDependency(backendStack);

// Output important information
new cdk.CfnOutput(backendStack, 'DeploymentInfo', {
  value: JSON.stringify({
    environment,
    region,
    timestamp: new Date().toISOString(),
    stacks: [
      `${stackPrefix}-Backend`,
      `${stackPrefix}-Frontend`,
      `${stackPrefix}-Monitoring`
    ]
  }),
  description: 'Deployment information'
});

app.synth();
