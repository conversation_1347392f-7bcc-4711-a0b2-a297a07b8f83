"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@xstate";
exports.ids = ["vendor-chunks/@xstate"];
exports.modules = {

/***/ "(ssr)/./node_modules/@xstate/react/es/useActor.js":
/*!***************************************************!*\
  !*** ./node_modules/@xstate/react/es/useActor.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isActorWithState: () => (/* binding */ isActorWithState),\n/* harmony export */   useActor: () => (/* binding */ useActor)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var use_isomorphic_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-isomorphic-layout-effect */ \"(ssr)/./node_modules/use-isomorphic-layout-effect/dist/use-isomorphic-layout-effect.esm.js\");\n/* harmony import */ var _useConstant__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./useConstant */ \"(ssr)/./node_modules/@xstate/react/es/useConstant.js\");\n/* harmony import */ var use_sync_external_store_shim_with_selector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! use-sync-external-store/shim/with-selector */ \"(ssr)/./node_modules/use-sync-external-store/shim/with-selector.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/@xstate/react/es/utils.js\");\n\n\n\n\n\nfunction identity(a) {\n    return a;\n}\nfunction isActorWithState(actorRef) {\n    return 'state' in actorRef;\n}\nfunction isDeferredActor(actorRef) {\n    return 'deferred' in actorRef;\n}\nfunction defaultGetSnapshot(actorRef) {\n    return 'getSnapshot' in actorRef\n        ? (0,_utils__WEBPACK_IMPORTED_MODULE_3__.isService)(actorRef)\n            ? (0,_utils__WEBPACK_IMPORTED_MODULE_3__.getServiceSnapshot)(actorRef)\n            : actorRef.getSnapshot()\n        : isActorWithState(actorRef)\n            ? actorRef.state\n            : undefined;\n}\nfunction useActor(actorRef, getSnapshot) {\n    if (getSnapshot === void 0) { getSnapshot = defaultGetSnapshot; }\n    var actorRefRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(actorRef);\n    var deferredEventsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    var subscribe = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (handleStoreChange) {\n        var unsubscribe = actorRef.subscribe(handleStoreChange).unsubscribe;\n        return unsubscribe;\n    }, [actorRef]);\n    var boundGetSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () { return getSnapshot(actorRef); }, [actorRef, getSnapshot]);\n    var isEqual = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (prevState, nextState) {\n        if ((0,_utils__WEBPACK_IMPORTED_MODULE_3__.isService)(actorRef)) {\n            return (0,_utils__WEBPACK_IMPORTED_MODULE_3__.isInterpreterStateEqual)(actorRef, prevState, nextState);\n        }\n        return prevState === nextState;\n    }, [actorRef]);\n    var storeSnapshot = (0,use_sync_external_store_shim_with_selector__WEBPACK_IMPORTED_MODULE_2__.useSyncExternalStoreWithSelector)(subscribe, boundGetSnapshot, boundGetSnapshot, identity, isEqual);\n    var send = (0,_useConstant__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(function () { return function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        var event = args[0];\n        if ( true && args.length > 1) {\n            console.warn(\"Unexpected payload: \".concat(JSON.stringify(args[1]), \". Only a single event object can be sent to actor send() functions.\"));\n        }\n        var currentActorRef = actorRefRef.current;\n        // If the previous actor is a deferred actor,\n        // queue the events so that they can be replayed\n        // on the non-deferred actor.\n        if (isDeferredActor(currentActorRef) && currentActorRef.deferred) {\n            deferredEventsRef.current.push(event);\n        }\n        else {\n            currentActorRef.send(event);\n        }\n    }; });\n    (0,use_isomorphic_layout_effect__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(function () {\n        actorRefRef.current = actorRef;\n        // Dequeue deferred events from the previous deferred actorRef\n        while (deferredEventsRef.current.length > 0) {\n            var deferredEvent = deferredEventsRef.current.shift();\n            actorRef.send(deferredEvent);\n        }\n    }, [actorRef]);\n    return [storeSnapshot, send];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@xstate/react/es/useActor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@xstate/react/es/useConstant.js":
/*!******************************************************!*\
  !*** ./node_modules/@xstate/react/es/useConstant.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useConstant)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction useConstant(fn) {\n    var ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    if (!ref.current) {\n        ref.current = { v: fn() };\n    }\n    return ref.current.v;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHhzdGF0ZS9yZWFjdC9lcy91c2VDb25zdGFudC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBK0I7QUFDaEI7QUFDZixjQUFjLHlDQUFZO0FBQzFCO0FBQ0Esd0JBQXdCO0FBQ3hCO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXNlY3VyaXR5LXNjYW5uZXItZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQHhzdGF0ZS9yZWFjdC9lcy91c2VDb25zdGFudC5qcz8zODg2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHVzZUNvbnN0YW50KGZuKSB7XG4gICAgdmFyIHJlZiA9IFJlYWN0LnVzZVJlZigpO1xuICAgIGlmICghcmVmLmN1cnJlbnQpIHtcbiAgICAgICAgcmVmLmN1cnJlbnQgPSB7IHY6IGZuKCkgfTtcbiAgICB9XG4gICAgcmV0dXJuIHJlZi5jdXJyZW50LnY7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@xstate/react/es/useConstant.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@xstate/react/es/useInterpret.js":
/*!*******************************************************!*\
  !*** ./node_modules/@xstate/react/es/useInterpret.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useIdleInterpreter: () => (/* binding */ useIdleInterpreter),\n/* harmony export */   useInterpret: () => (/* binding */ useInterpret)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var use_isomorphic_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-isomorphic-layout-effect */ \"(ssr)/./node_modules/use-isomorphic-layout-effect/dist/use-isomorphic-layout-effect.esm.js\");\n/* harmony import */ var xstate__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! xstate */ \"(ssr)/./node_modules/xstate/es/interpreter.js\");\n/* harmony import */ var xstate__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! xstate */ \"(ssr)/./node_modules/xstate/es/utils.js\");\n/* harmony import */ var xstate__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! xstate */ \"(ssr)/./node_modules/xstate/es/State.js\");\n/* harmony import */ var _useConstant__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useConstant */ \"(ssr)/./node_modules/@xstate/react/es/useConstant.js\");\nvar __assign = (undefined && undefined.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __rest = (undefined && undefined.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\nvar __read = (undefined && undefined.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\n\n\n\n\nfunction useIdleInterpreter(getMachine, options) {\n    var machine = (0,_useConstant__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(function () {\n        return typeof getMachine === 'function' ? getMachine() : getMachine;\n    });\n    if ( true &&\n        typeof getMachine !== 'function') {\n        var _a = __read((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(machine), 1), initialMachine = _a[0];\n        if (getMachine !== initialMachine) {\n            console.warn('Machine given to `useMachine` has changed between renders. This is not supported and might lead to unexpected results.\\n' +\n                'Please make sure that you pass the same Machine as argument each time.');\n        }\n    }\n    var context = options.context, guards = options.guards, actions = options.actions, activities = options.activities, services = options.services, delays = options.delays, rehydratedState = options.state, interpreterOptions = __rest(options, [\"context\", \"guards\", \"actions\", \"activities\", \"services\", \"delays\", \"state\"]);\n    var service = (0,_useConstant__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(function () {\n        var machineConfig = {\n            context: context,\n            guards: guards,\n            actions: actions,\n            activities: activities,\n            services: services,\n            delays: delays\n        };\n        var machineWithConfig = machine.withConfig(machineConfig, function () { return (__assign(__assign({}, machine.context), context)); });\n        return (0,xstate__WEBPACK_IMPORTED_MODULE_3__.interpret)(machineWithConfig, interpreterOptions);\n    });\n    // Make sure options are kept updated when they change.\n    // This mutation assignment is safe because the service instance is only used\n    // in one place -- this hook's caller.\n    (0,use_isomorphic_layout_effect__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(function () {\n        Object.assign(service.machine.options.actions, actions);\n        Object.assign(service.machine.options.guards, guards);\n        Object.assign(service.machine.options.activities, activities);\n        Object.assign(service.machine.options.services, services);\n        Object.assign(service.machine.options.delays, delays);\n    }, [actions, guards, activities, services, delays]);\n    return service;\n}\nfunction useInterpret(getMachine) {\n    var _a = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        _a[_i - 1] = arguments[_i];\n    }\n    var _b = __read(_a, 2), _c = _b[0], options = _c === void 0 ? {} : _c, observerOrListener = _b[1];\n    var service = useIdleInterpreter(getMachine, options);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n        if (!observerOrListener) {\n            return;\n        }\n        var sub = service.subscribe((0,xstate__WEBPACK_IMPORTED_MODULE_4__.toObserver)(observerOrListener));\n        return function () {\n            sub.unsubscribe();\n        };\n    }, [observerOrListener]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n        var rehydratedState = options.state;\n        service.start(rehydratedState ? xstate__WEBPACK_IMPORTED_MODULE_5__.State.create(rehydratedState) : undefined);\n        return function () {\n            service.stop();\n            service.status = xstate__WEBPACK_IMPORTED_MODULE_3__.InterpreterStatus.NotStarted;\n        };\n    }, []);\n    return service;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@xstate/react/es/useInterpret.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@xstate/react/es/useSelector.js":
/*!******************************************************!*\
  !*** ./node_modules/@xstate/react/es/useSelector.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSelector: () => (/* binding */ useSelector)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var use_sync_external_store_shim_with_selector__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-sync-external-store/shim/with-selector */ \"(ssr)/./node_modules/use-sync-external-store/shim/with-selector.js\");\n/* harmony import */ var _useActor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useActor */ \"(ssr)/./node_modules/@xstate/react/es/useActor.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/@xstate/react/es/utils.js\");\n\n\n\n\nvar defaultCompare = function (a, b) { return a === b; };\nvar defaultGetSnapshot = function (a, initialStateCacheRef) {\n    if ((0,_utils__WEBPACK_IMPORTED_MODULE_2__.isService)(a)) {\n        // A status of 0 = interpreter not started\n        if (a.status === 0 && initialStateCacheRef.current) {\n            return initialStateCacheRef.current;\n        }\n        var snapshot = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.getServiceSnapshot)(a);\n        initialStateCacheRef.current = a.status === 0 ? snapshot : null;\n        return snapshot;\n    }\n    return (0,_useActor__WEBPACK_IMPORTED_MODULE_3__.isActorWithState)(a) ? a.state : undefined;\n};\nfunction useSelector(actor, selector, compare, getSnapshot) {\n    if (compare === void 0) { compare = defaultCompare; }\n    var initialStateCacheRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var subscribe = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (handleStoreChange) {\n        var unsubscribe = actor.subscribe(handleStoreChange).unsubscribe;\n        return unsubscribe;\n    }, [actor]);\n    var boundGetSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {\n        if (getSnapshot) {\n            return getSnapshot(actor);\n        }\n        return defaultGetSnapshot(actor, initialStateCacheRef);\n    }, [actor, getSnapshot]);\n    var selectedSnapshot = (0,use_sync_external_store_shim_with_selector__WEBPACK_IMPORTED_MODULE_1__.useSyncExternalStoreWithSelector)(subscribe, boundGetSnapshot, boundGetSnapshot, selector, compare);\n    return selectedSnapshot;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@xstate/react/es/useSelector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@xstate/react/es/utils.js":
/*!************************************************!*\
  !*** ./node_modules/@xstate/react/es/utils.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getServiceSnapshot: () => (/* binding */ getServiceSnapshot),\n/* harmony export */   isInterpreterStateEqual: () => (/* binding */ isInterpreterStateEqual),\n/* harmony export */   isService: () => (/* binding */ isService),\n/* harmony export */   partition: () => (/* binding */ partition),\n/* harmony export */   shallowEqual: () => (/* binding */ shallowEqual)\n/* harmony export */ });\n/* harmony import */ var xstate__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! xstate */ \"(ssr)/./node_modules/xstate/es/interpreter.js\");\nvar __read = (undefined && undefined.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __values = (undefined && undefined.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n\nfunction partition(items, predicate) {\n    var e_1, _a;\n    var _b = __read([[], []], 2), truthy = _b[0], falsy = _b[1];\n    try {\n        for (var items_1 = __values(items), items_1_1 = items_1.next(); !items_1_1.done; items_1_1 = items_1.next()) {\n            var item = items_1_1.value;\n            if (predicate(item)) {\n                truthy.push(item);\n            }\n            else {\n                falsy.push(item);\n            }\n        }\n    }\n    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n    finally {\n        try {\n            if (items_1_1 && !items_1_1.done && (_a = items_1.return)) _a.call(items_1);\n        }\n        finally { if (e_1) throw e_1.error; }\n    }\n    return [truthy, falsy];\n}\nfunction getServiceSnapshot(service) {\n    return service.status !== 0\n        ? service.getSnapshot()\n        : service.machine.initialState;\n}\n// From https://github.com/reduxjs/react-redux/blob/master/src/utils/shallowEqual.ts\nfunction is(x, y) {\n    if (x === y) {\n        return x !== 0 || y !== 0 || 1 / x === 1 / y;\n    }\n    else {\n        return x !== x && y !== y;\n    }\n}\nfunction shallowEqual(objA, objB) {\n    if (is(objA, objB))\n        return true;\n    if (typeof objA !== 'object' ||\n        objA === null ||\n        typeof objB !== 'object' ||\n        objB === null) {\n        return false;\n    }\n    var keysA = Object.keys(objA);\n    var keysB = Object.keys(objB);\n    if (keysA.length !== keysB.length)\n        return false;\n    for (var i = 0; i < keysA.length; i++) {\n        if (!Object.prototype.hasOwnProperty.call(objB, keysA[i]) ||\n            !is(objA[keysA[i]], objB[keysA[i]])) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction isService(actor) {\n    return 'state' in actor && 'machine' in actor;\n}\nfunction isInterpreterStateEqual(service, prevState, nextState) {\n    if (service.status === xstate__WEBPACK_IMPORTED_MODULE_0__.InterpreterStatus.NotStarted) {\n        return true;\n    }\n    // Only change the current state if:\n    // - the incoming state is the \"live\" initial state (since it might have new actors)\n    // - OR the incoming state actually changed.\n    //\n    // The \"live\" initial state will have .changed === undefined.\n    var initialStateChanged = nextState.changed === undefined &&\n        (Object.keys(nextState.children).length > 0 ||\n            typeof prevState.changed === 'boolean');\n    return !(nextState.changed || initialStateChanged);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHhzdGF0ZS9yZWFjdC9lcy91dGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBQSxjQUFjLFNBQUksSUFBSSxTQUFJO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixNQUFNO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixTQUFJLElBQUksU0FBSTtBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQzJDO0FBQ3BDO0FBQ1A7QUFDQTtBQUNBO0FBQ0Esd0VBQXdFLGlCQUFpQjtBQUN6RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsUUFBUTtBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0Isa0JBQWtCO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ087QUFDUCwyQkFBMkIscURBQWlCO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXNlY3VyaXR5LXNjYW5uZXItZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQHhzdGF0ZS9yZWFjdC9lcy91dGlscy5qcz9jNDk2Il0sInNvdXJjZXNDb250ZW50IjpbInZhciBfX3JlYWQgPSAodGhpcyAmJiB0aGlzLl9fcmVhZCkgfHwgZnVuY3Rpb24gKG8sIG4pIHtcbiAgICB2YXIgbSA9IHR5cGVvZiBTeW1ib2wgPT09IFwiZnVuY3Rpb25cIiAmJiBvW1N5bWJvbC5pdGVyYXRvcl07XG4gICAgaWYgKCFtKSByZXR1cm4gbztcbiAgICB2YXIgaSA9IG0uY2FsbChvKSwgciwgYXIgPSBbXSwgZTtcbiAgICB0cnkge1xuICAgICAgICB3aGlsZSAoKG4gPT09IHZvaWQgMCB8fCBuLS0gPiAwKSAmJiAhKHIgPSBpLm5leHQoKSkuZG9uZSkgYXIucHVzaChyLnZhbHVlKTtcbiAgICB9XG4gICAgY2F0Y2ggKGVycm9yKSB7IGUgPSB7IGVycm9yOiBlcnJvciB9OyB9XG4gICAgZmluYWxseSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBpZiAociAmJiAhci5kb25lICYmIChtID0gaVtcInJldHVyblwiXSkpIG0uY2FsbChpKTtcbiAgICAgICAgfVxuICAgICAgICBmaW5hbGx5IHsgaWYgKGUpIHRocm93IGUuZXJyb3I7IH1cbiAgICB9XG4gICAgcmV0dXJuIGFyO1xufTtcbnZhciBfX3ZhbHVlcyA9ICh0aGlzICYmIHRoaXMuX192YWx1ZXMpIHx8IGZ1bmN0aW9uKG8pIHtcbiAgICB2YXIgcyA9IHR5cGVvZiBTeW1ib2wgPT09IFwiZnVuY3Rpb25cIiAmJiBTeW1ib2wuaXRlcmF0b3IsIG0gPSBzICYmIG9bc10sIGkgPSAwO1xuICAgIGlmIChtKSByZXR1cm4gbS5jYWxsKG8pO1xuICAgIGlmIChvICYmIHR5cGVvZiBvLmxlbmd0aCA9PT0gXCJudW1iZXJcIikgcmV0dXJuIHtcbiAgICAgICAgbmV4dDogZnVuY3Rpb24gKCkge1xuICAgICAgICAgICAgaWYgKG8gJiYgaSA+PSBvLmxlbmd0aCkgbyA9IHZvaWQgMDtcbiAgICAgICAgICAgIHJldHVybiB7IHZhbHVlOiBvICYmIG9baSsrXSwgZG9uZTogIW8gfTtcbiAgICAgICAgfVxuICAgIH07XG4gICAgdGhyb3cgbmV3IFR5cGVFcnJvcihzID8gXCJPYmplY3QgaXMgbm90IGl0ZXJhYmxlLlwiIDogXCJTeW1ib2wuaXRlcmF0b3IgaXMgbm90IGRlZmluZWQuXCIpO1xufTtcbmltcG9ydCB7IEludGVycHJldGVyU3RhdHVzIH0gZnJvbSAneHN0YXRlJztcbmV4cG9ydCBmdW5jdGlvbiBwYXJ0aXRpb24oaXRlbXMsIHByZWRpY2F0ZSkge1xuICAgIHZhciBlXzEsIF9hO1xuICAgIHZhciBfYiA9IF9fcmVhZChbW10sIFtdXSwgMiksIHRydXRoeSA9IF9iWzBdLCBmYWxzeSA9IF9iWzFdO1xuICAgIHRyeSB7XG4gICAgICAgIGZvciAodmFyIGl0ZW1zXzEgPSBfX3ZhbHVlcyhpdGVtcyksIGl0ZW1zXzFfMSA9IGl0ZW1zXzEubmV4dCgpOyAhaXRlbXNfMV8xLmRvbmU7IGl0ZW1zXzFfMSA9IGl0ZW1zXzEubmV4dCgpKSB7XG4gICAgICAgICAgICB2YXIgaXRlbSA9IGl0ZW1zXzFfMS52YWx1ZTtcbiAgICAgICAgICAgIGlmIChwcmVkaWNhdGUoaXRlbSkpIHtcbiAgICAgICAgICAgICAgICB0cnV0aHkucHVzaChpdGVtKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIGZhbHN5LnB1c2goaXRlbSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgY2F0Y2ggKGVfMV8xKSB7IGVfMSA9IHsgZXJyb3I6IGVfMV8xIH07IH1cbiAgICBmaW5hbGx5IHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGlmIChpdGVtc18xXzEgJiYgIWl0ZW1zXzFfMS5kb25lICYmIChfYSA9IGl0ZW1zXzEucmV0dXJuKSkgX2EuY2FsbChpdGVtc18xKTtcbiAgICAgICAgfVxuICAgICAgICBmaW5hbGx5IHsgaWYgKGVfMSkgdGhyb3cgZV8xLmVycm9yOyB9XG4gICAgfVxuICAgIHJldHVybiBbdHJ1dGh5LCBmYWxzeV07XG59XG5leHBvcnQgZnVuY3Rpb24gZ2V0U2VydmljZVNuYXBzaG90KHNlcnZpY2UpIHtcbiAgICByZXR1cm4gc2VydmljZS5zdGF0dXMgIT09IDBcbiAgICAgICAgPyBzZXJ2aWNlLmdldFNuYXBzaG90KClcbiAgICAgICAgOiBzZXJ2aWNlLm1hY2hpbmUuaW5pdGlhbFN0YXRlO1xufVxuLy8gRnJvbSBodHRwczovL2dpdGh1Yi5jb20vcmVkdXhqcy9yZWFjdC1yZWR1eC9ibG9iL21hc3Rlci9zcmMvdXRpbHMvc2hhbGxvd0VxdWFsLnRzXG5mdW5jdGlvbiBpcyh4LCB5KSB7XG4gICAgaWYgKHggPT09IHkpIHtcbiAgICAgICAgcmV0dXJuIHggIT09IDAgfHwgeSAhPT0gMCB8fCAxIC8geCA9PT0gMSAvIHk7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICByZXR1cm4geCAhPT0geCAmJiB5ICE9PSB5O1xuICAgIH1cbn1cbmV4cG9ydCBmdW5jdGlvbiBzaGFsbG93RXF1YWwob2JqQSwgb2JqQikge1xuICAgIGlmIChpcyhvYmpBLCBvYmpCKSlcbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgaWYgKHR5cGVvZiBvYmpBICE9PSAnb2JqZWN0JyB8fFxuICAgICAgICBvYmpBID09PSBudWxsIHx8XG4gICAgICAgIHR5cGVvZiBvYmpCICE9PSAnb2JqZWN0JyB8fFxuICAgICAgICBvYmpCID09PSBudWxsKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgdmFyIGtleXNBID0gT2JqZWN0LmtleXMob2JqQSk7XG4gICAgdmFyIGtleXNCID0gT2JqZWN0LmtleXMob2JqQik7XG4gICAgaWYgKGtleXNBLmxlbmd0aCAhPT0ga2V5c0IubGVuZ3RoKVxuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgZm9yICh2YXIgaSA9IDA7IGkgPCBrZXlzQS5sZW5ndGg7IGkrKykge1xuICAgICAgICBpZiAoIU9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChvYmpCLCBrZXlzQVtpXSkgfHxcbiAgICAgICAgICAgICFpcyhvYmpBW2tleXNBW2ldXSwgb2JqQltrZXlzQVtpXV0pKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIHRydWU7XG59XG5leHBvcnQgZnVuY3Rpb24gaXNTZXJ2aWNlKGFjdG9yKSB7XG4gICAgcmV0dXJuICdzdGF0ZScgaW4gYWN0b3IgJiYgJ21hY2hpbmUnIGluIGFjdG9yO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGlzSW50ZXJwcmV0ZXJTdGF0ZUVxdWFsKHNlcnZpY2UsIHByZXZTdGF0ZSwgbmV4dFN0YXRlKSB7XG4gICAgaWYgKHNlcnZpY2Uuc3RhdHVzID09PSBJbnRlcnByZXRlclN0YXR1cy5Ob3RTdGFydGVkKSB7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgICAvLyBPbmx5IGNoYW5nZSB0aGUgY3VycmVudCBzdGF0ZSBpZjpcbiAgICAvLyAtIHRoZSBpbmNvbWluZyBzdGF0ZSBpcyB0aGUgXCJsaXZlXCIgaW5pdGlhbCBzdGF0ZSAoc2luY2UgaXQgbWlnaHQgaGF2ZSBuZXcgYWN0b3JzKVxuICAgIC8vIC0gT1IgdGhlIGluY29taW5nIHN0YXRlIGFjdHVhbGx5IGNoYW5nZWQuXG4gICAgLy9cbiAgICAvLyBUaGUgXCJsaXZlXCIgaW5pdGlhbCBzdGF0ZSB3aWxsIGhhdmUgLmNoYW5nZWQgPT09IHVuZGVmaW5lZC5cbiAgICB2YXIgaW5pdGlhbFN0YXRlQ2hhbmdlZCA9IG5leHRTdGF0ZS5jaGFuZ2VkID09PSB1bmRlZmluZWQgJiZcbiAgICAgICAgKE9iamVjdC5rZXlzKG5leHRTdGF0ZS5jaGlsZHJlbikubGVuZ3RoID4gMCB8fFxuICAgICAgICAgICAgdHlwZW9mIHByZXZTdGF0ZS5jaGFuZ2VkID09PSAnYm9vbGVhbicpO1xuICAgIHJldHVybiAhKG5leHRTdGF0ZS5jaGFuZ2VkIHx8IGluaXRpYWxTdGF0ZUNoYW5nZWQpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@xstate/react/es/utils.js\n");

/***/ })

};
;