import json
import boto3
import os
import re
import hashlib
import tempfile
from datetime import datetime
from typing import Dict, Any, List, Optional
import structlog
import chardet
from PyPDF2 import PdfReader
from docx import Document
import openpyxl

# Configure logging
logger = structlog.get_logger()

# Initialize AWS clients
s3 = boto3.client('s3')
dynamodb = boto3.resource('dynamodb')

# Environment variables
UPLOADS_BUCKET = os.environ['UPLOADS_BUCKET']
RESULTS_TABLE = os.environ['RESULTS_TABLE']

# Security patterns for detection
SECURITY_PATTERNS = {
    'api_keys': [
        r'(?i)api[_-]?key[_-]?[:=]\s*["\']?([a-zA-Z0-9_-]{20,})["\']?',
        r'(?i)secret[_-]?key[_-]?[:=]\s*["\']?([a-zA-Z0-9_-]{20,})["\']?',
        r'(?i)access[_-]?token[_-]?[:=]\s*["\']?([a-zA-Z0-9_-]{20,})["\']?'
    ],
    'aws_credentials': [
        r'AKIA[0-9A-Z]{16}',  # AWS Access Key ID
        r'(?i)aws[_-]?secret[_-]?access[_-]?key[_-]?[:=]\s*["\']?([a-zA-Z0-9/+=]{40})["\']?'
    ],
    'database_urls': [
        r'(?i)(mysql|postgresql|mongodb|redis)://[^\s]+',
        r'(?i)database[_-]?url[_-]?[:=]\s*["\']?([^\s"\']+)["\']?'
    ],
    'private_keys': [
        r'-----BEGIN\s+(RSA\s+)?PRIVATE\s+KEY-----',
        r'-----BEGIN\s+OPENSSH\s+PRIVATE\s+KEY-----'
    ],
    'jwt_tokens': [
        r'eyJ[A-Za-z0-9_-]*\.eyJ[A-Za-z0-9_-]*\.[A-Za-z0-9_-]*'
    ],
    'passwords': [
        r'(?i)password[_-]?[:=]\s*["\']?([^\s"\']{8,})["\']?',
        r'(?i)passwd[_-]?[:=]\s*["\']?([^\s"\']{8,})["\']?'
    ],
    'credit_cards': [
        r'\b4[0-9]{12}(?:[0-9]{3})?\b',  # Visa
        r'\b5[1-5][0-9]{14}\b',  # MasterCard
        r'\b3[47][0-9]{13}\b',  # American Express
        r'\b6(?:011|5[0-9]{2})[0-9]{12}\b'  # Discover
    ],
    'ip_addresses': [
        r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'
    ],
    'suspicious_urls': [
        r'(?i)https?://[^\s]*(?:malware|phishing|suspicious|hack|exploit)[^\s]*'
    ]
}

# Malware signatures (simplified examples)
MALWARE_SIGNATURES = {
    'suspicious_strings': [
        'eval(',
        'exec(',
        'system(',
        'shell_exec(',
        'base64_decode(',
        'gzinflate(',
        'str_rot13(',
        'powershell',
        'cmd.exe',
        '/bin/sh',
        'wget',
        'curl'
    ],
    'file_extensions': [
        '.exe',
        '.scr',
        '.bat',
        '.cmd',
        '.com',
        '.pif',
        '.vbs',
        '.js'
    ]
}

def download_file_from_s3(s3_key: str) -> str:
    """Download file from S3 to temporary location"""
    try:
        temp_file = tempfile.NamedTemporaryFile(delete=False)
        s3.download_fileobj(UPLOADS_BUCKET, s3_key, temp_file)
        temp_file.close()
        return temp_file.name
    except Exception as e:
        logger.error("Failed to download file from S3", s3_key=s3_key, error=str(e))
        raise

def detect_file_encoding(file_path: str) -> str:
    """Detect file encoding"""
    try:
        with open(file_path, 'rb') as f:
            raw_data = f.read(10000)  # Read first 10KB
            result = chardet.detect(raw_data)
            return result.get('encoding', 'utf-8')
    except Exception:
        return 'utf-8'

def extract_text_from_file(file_path: str, mime_type: str) -> str:
    """Extract text content from various file types"""
    try:
        if mime_type == 'application/pdf':
            return extract_text_from_pdf(file_path)
        elif mime_type in ['application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/msword']:
            return extract_text_from_docx(file_path)
        elif mime_type in ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']:
            return extract_text_from_excel(file_path)
        elif mime_type.startswith('text/'):
            return extract_text_from_text_file(file_path)
        else:
            # Try to read as text file
            return extract_text_from_text_file(file_path)
    except Exception as e:
        logger.error("Failed to extract text from file", file_path=file_path, mime_type=mime_type, error=str(e))
        return ""

def extract_text_from_pdf(file_path: str) -> str:
    """Extract text from PDF file"""
    try:
        text = ""
        with open(file_path, 'rb') as file:
            pdf_reader = PdfReader(file)
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
        return text
    except Exception as e:
        logger.error("Failed to extract text from PDF", error=str(e))
        return ""

def extract_text_from_docx(file_path: str) -> str:
    """Extract text from DOCX file"""
    try:
        doc = Document(file_path)
        text = ""
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
        return text
    except Exception as e:
        logger.error("Failed to extract text from DOCX", error=str(e))
        return ""

def extract_text_from_excel(file_path: str) -> str:
    """Extract text from Excel file"""
    try:
        workbook = openpyxl.load_workbook(file_path, data_only=True)
        text = ""
        for sheet_name in workbook.sheetnames:
            sheet = workbook[sheet_name]
            for row in sheet.iter_rows(values_only=True):
                for cell in row:
                    if cell is not None:
                        text += str(cell) + " "
                text += "\n"
        return text
    except Exception as e:
        logger.error("Failed to extract text from Excel", error=str(e))
        return ""

def extract_text_from_text_file(file_path: str) -> str:
    """Extract text from text file"""
    try:
        encoding = detect_file_encoding(file_path)
        with open(file_path, 'r', encoding=encoding, errors='ignore') as file:
            return file.read()
    except Exception as e:
        logger.error("Failed to extract text from text file", error=str(e))
        return ""

def scan_for_patterns(text: str, patterns: Dict[str, List[str]]) -> Dict[str, List[Dict[str, Any]]]:
    """Scan text for security patterns"""
    findings = {}
    
    for category, pattern_list in patterns.items():
        category_findings = []
        
        for pattern in pattern_list:
            try:
                matches = re.finditer(pattern, text, re.MULTILINE | re.IGNORECASE)
                for match in matches:
                    finding = {
                        'pattern': pattern,
                        'match': match.group(0),
                        'start': match.start(),
                        'end': match.end(),
                        'line_number': text[:match.start()].count('\n') + 1,
                        'context': get_context(text, match.start(), match.end())
                    }
                    category_findings.append(finding)
            except re.error as e:
                logger.warning("Invalid regex pattern", pattern=pattern, error=str(e))
        
        if category_findings:
            findings[category] = category_findings
    
    return findings

def get_context(text: str, start: int, end: int, context_size: int = 50) -> str:
    """Get context around a match"""
    context_start = max(0, start - context_size)
    context_end = min(len(text), end + context_size)
    return text[context_start:context_end]

def scan_for_malware_signatures(text: str, file_path: str) -> Dict[str, List[Dict[str, Any]]]:
    """Scan for malware signatures"""
    findings = {}
    
    # Check for suspicious strings
    suspicious_findings = []
    for suspicious_string in MALWARE_SIGNATURES['suspicious_strings']:
        if suspicious_string.lower() in text.lower():
            suspicious_findings.append({
                'type': 'suspicious_string',
                'value': suspicious_string,
                'description': f'Potentially malicious string: {suspicious_string}'
            })
    
    if suspicious_findings:
        findings['suspicious_strings'] = suspicious_findings
    
    # Check file extension
    file_ext = os.path.splitext(file_path)[1].lower()
    if file_ext in MALWARE_SIGNATURES['file_extensions']:
        findings['suspicious_extension'] = [{
            'type': 'suspicious_extension',
            'value': file_ext,
            'description': f'Potentially dangerous file extension: {file_ext}'
        }]
    
    return findings

def calculate_risk_score(findings: Dict[str, Any]) -> int:
    """Calculate risk score based on findings"""
    score = 0
    
    # Weight different types of findings
    weights = {
        'api_keys': 8,
        'aws_credentials': 9,
        'database_urls': 7,
        'private_keys': 10,
        'jwt_tokens': 6,
        'passwords': 7,
        'credit_cards': 9,
        'suspicious_strings': 5,
        'suspicious_extension': 4,
        'ip_addresses': 3,
        'suspicious_urls': 6
    }
    
    for category, category_findings in findings.items():
        if isinstance(category_findings, list):
            count = len(category_findings)
            weight = weights.get(category, 1)
            score += count * weight
    
    return min(score, 100)  # Cap at 100

def save_results(scan_id: str, results: Dict[str, Any]) -> None:
    """Save scan results to DynamoDB"""
    try:
        table = dynamodb.Table(RESULTS_TABLE)
        
        item = {
            'scanId': scan_id,
            'resultType': 'security',
            'results': results,
            'timestamp': datetime.utcnow().isoformat(),
            'ttl': int(datetime.utcnow().timestamp()) + (30 * 24 * 60 * 60)  # 30 days TTL
        }
        
        table.put_item(Item=item)
        logger.info("Security scan results saved", scan_id=scan_id)
        
    except Exception as e:
        logger.error("Failed to save results", scan_id=scan_id, error=str(e))
        raise

def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """Main Lambda handler for security scanning"""
    logger.info("Security scan started", event=event)
    
    try:
        # Extract parameters from event
        scan_id = event.get('scanId')
        s3_key = event.get('s3Key')
        
        if not scan_id or not s3_key:
            raise ValueError("Missing required parameters: scanId and s3Key")
        
        # Download file from S3
        local_file_path = download_file_from_s3(s3_key)
        
        try:
            # Determine MIME type from S3 metadata or file extension
            s3_response = s3.head_object(Bucket=UPLOADS_BUCKET, Key=s3_key)
            mime_type = s3_response.get('ContentType', 'application/octet-stream')
            
            # Extract text content
            text_content = extract_text_from_file(local_file_path, mime_type)
            
            # Perform security scans
            security_findings = scan_for_patterns(text_content, SECURITY_PATTERNS)
            malware_findings = scan_for_malware_signatures(text_content, local_file_path)
            
            # Combine all findings
            all_findings = {**security_findings, **malware_findings}
            
            # Calculate risk score
            risk_score = calculate_risk_score(all_findings)
            
            # Determine risk level
            if risk_score >= 80:
                risk_level = 'CRITICAL'
            elif risk_score >= 60:
                risk_level = 'HIGH'
            elif risk_score >= 40:
                risk_level = 'MEDIUM'
            elif risk_score >= 20:
                risk_level = 'LOW'
            else:
                risk_level = 'MINIMAL'
            
            # Prepare results
            results = {
                'scanType': 'security',
                'status': 'completed',
                'riskScore': risk_score,
                'riskLevel': risk_level,
                'findings': all_findings,
                'summary': {
                    'totalFindings': sum(len(f) if isinstance(f, list) else 1 for f in all_findings.values()),
                    'categoriesFound': list(all_findings.keys()),
                    'hasCredentials': any(cat in all_findings for cat in ['api_keys', 'aws_credentials', 'passwords']),
                    'hasMalware': any(cat in all_findings for cat in ['suspicious_strings', 'suspicious_extension'])
                },
                'completedAt': datetime.utcnow().isoformat()
            }
            
            # Save results
            save_results(scan_id, results)
            
            logger.info("Security scan completed", 
                       scan_id=scan_id, 
                       risk_score=risk_score, 
                       risk_level=risk_level,
                       total_findings=results['summary']['totalFindings'])
            
            return results
            
        finally:
            # Clean up temporary file
            try:
                os.unlink(local_file_path)
            except Exception:
                pass
    
    except Exception as e:
        logger.error("Security scan failed", scan_id=scan_id, error=str(e))
        
        error_results = {
            'scanType': 'security',
            'status': 'failed',
            'error': str(e),
            'completedAt': datetime.utcnow().isoformat()
        }
        
        try:
            save_results(scan_id, error_results)
        except Exception:
            pass
        
        raise
