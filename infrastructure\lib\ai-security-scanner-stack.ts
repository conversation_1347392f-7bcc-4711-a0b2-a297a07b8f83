import * as cdk from 'aws-cdk-lib';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import * as cognito from 'aws-cdk-lib/aws-cognito';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as stepfunctions from 'aws-cdk-lib/aws-stepfunctions';
import * as stepfunctionsTasks from 'aws-cdk-lib/aws-stepfunctions-tasks';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as kms from 'aws-cdk-lib/aws-kms';
import { Construct } from 'constructs';

export interface AiSecurityScannerStackProps extends cdk.StackProps {
  environment: string;
  config: any;
}

export class AiSecurityScannerStack extends cdk.Stack {
  public readonly apiGateway: apigateway.RestApi;
  public readonly apiGatewayUrl: string;
  public readonly userPoolId: string;
  public readonly userPoolClientId: string;
  public readonly identityPoolId: string;
  public readonly lambdaFunctions: { [key: string]: lambda.Function };
  public readonly stepFunction: stepfunctions.StateMachine;

  constructor(scope: Construct, id: string, props: AiSecurityScannerStackProps) {
    super(scope, id, props);

    const { environment, config } = props;

    // KMS Key for encryption
    const kmsKey = new kms.Key(this, 'AiScannerKmsKey', {
      description: `AI Security Scanner KMS Key - ${environment}`,
      enableKeyRotation: true,
      removalPolicy: environment === 'prod' ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY
    });

    // S3 Buckets
    const uploadsBucket = new s3.Bucket(this, 'UploadsBucket', {
      bucketName: `ai-scanner-uploads-${environment}-${this.account}`,
      encryption: s3.BucketEncryption.KMS,
      encryptionKey: kmsKey,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      versioned: true,
      lifecycleRules: [{
        id: 'DeleteUploadsAfterRetention',
        enabled: true,
        expiration: cdk.Duration.days(config.retentionDays)
      }],
      removalPolicy: environment === 'prod' ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY
    });

    const reportsBucket = new s3.Bucket(this, 'ReportsBucket', {
      bucketName: `ai-scanner-reports-${environment}-${this.account}`,
      encryption: s3.BucketEncryption.KMS,
      encryptionKey: kmsKey,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      versioned: true,
      lifecycleRules: [{
        id: 'ArchiveReportsAfter90Days',
        enabled: true,
        transitions: [{
          storageClass: s3.StorageClass.GLACIER,
          transitionAfter: cdk.Duration.days(90)
        }]
      }],
      removalPolicy: environment === 'prod' ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY
    });

    // DynamoDB Tables
    const usersTable = new dynamodb.Table(this, 'UsersTable', {
      tableName: `ai-scanner-users-${environment}`,
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
      encryption: dynamodb.TableEncryption.CUSTOMER_MANAGED,
      encryptionKey: kmsKey,
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      pointInTimeRecovery: environment === 'prod',
      removalPolicy: environment === 'prod' ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY
    });

    const scansTable = new dynamodb.Table(this, 'ScansTable', {
      tableName: `ai-scanner-scans-${environment}`,
      partitionKey: { name: 'scanId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'timestamp', type: dynamodb.AttributeType.NUMBER },
      encryption: dynamodb.TableEncryption.CUSTOMER_MANAGED,
      encryptionKey: kmsKey,
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      pointInTimeRecovery: environment === 'prod',
      removalPolicy: environment === 'prod' ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY
    });

    // Add GSI for user scans
    scansTable.addGlobalSecondaryIndex({
      indexName: 'UserScansIndex',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'timestamp', type: dynamodb.AttributeType.NUMBER }
    });

    const resultsTable = new dynamodb.Table(this, 'ResultsTable', {
      tableName: `ai-scanner-results-${environment}`,
      partitionKey: { name: 'scanId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'resultType', type: dynamodb.AttributeType.STRING },
      encryption: dynamodb.TableEncryption.CUSTOMER_MANAGED,
      encryptionKey: kmsKey,
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      pointInTimeRecovery: environment === 'prod',
      removalPolicy: environment === 'prod' ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY
    });

    // Cognito User Pool
    const userPool = new cognito.UserPool(this, 'UserPool', {
      userPoolName: `ai-scanner-users-${environment}`,
      selfSignUpEnabled: true,
      signInAliases: { email: true },
      autoVerify: { email: true },
      passwordPolicy: {
        minLength: 8,
        requireLowercase: true,
        requireUppercase: true,
        requireDigits: true,
        requireSymbols: true
      },
      mfa: cognito.Mfa.OPTIONAL,
      mfaSecondFactor: {
        sms: true,
        otp: true
      },
      accountRecovery: cognito.AccountRecovery.EMAIL_ONLY,
      removalPolicy: environment === 'prod' ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY
    });

    const userPoolClient = new cognito.UserPoolClient(this, 'UserPoolClient', {
      userPool,
      userPoolClientName: `ai-scanner-client-${environment}`,
      generateSecret: false,
      authFlows: {
        userSrp: true,
        userPassword: false,
        adminUserPassword: false
      },
      oAuth: {
        flows: {
          authorizationCodeGrant: true
        },
        scopes: [cognito.OAuthScope.OPENID, cognito.OAuthScope.EMAIL, cognito.OAuthScope.PROFILE],
        callbackUrls: environment === 'prod' 
          ? ['https://ai-security-scanner.com/auth/callback']
          : ['http://localhost:3000/auth/callback']
      }
    });

    const identityPool = new cognito.CfnIdentityPool(this, 'IdentityPool', {
      identityPoolName: `ai_scanner_identity_${environment}`,
      allowUnauthenticatedIdentities: false,
      cognitoIdentityProviders: [{
        clientId: userPoolClient.userPoolClientId,
        providerName: userPool.userPoolProviderName
      }]
    });

    // Lambda Layer for common dependencies
    const commonLayer = new lambda.LayerVersion(this, 'CommonLayer', {
      layerVersionName: `ai-scanner-common-${environment}`,
      code: lambda.Code.fromAsset('../backend/layers/common'),
      compatibleRuntimes: [lambda.Runtime.PYTHON_3_9, lambda.Runtime.NODEJS_18_X],
      description: 'Common dependencies for AI Scanner Lambda functions'
    });

    // Lambda Functions
    this.lambdaFunctions = this.createLambdaFunctions(
      environment,
      config,
      uploadsBucket,
      reportsBucket,
      usersTable,
      scansTable,
      resultsTable,
      kmsKey,
      commonLayer
    );

    // Step Functions State Machine
    this.stepFunction = this.createStepFunction(environment, this.lambdaFunctions);

    // API Gateway
    this.apiGateway = this.createApiGateway(environment, userPool, this.lambdaFunctions);

    // Store outputs
    this.apiGatewayUrl = this.apiGateway.url;
    this.userPoolId = userPool.userPoolId;
    this.userPoolClientId = userPoolClient.userPoolClientId;
    this.identityPoolId = identityPool.ref;

    // CloudFormation Outputs
    new cdk.CfnOutput(this, 'ApiGatewayUrl', {
      value: this.apiGatewayUrl,
      description: 'API Gateway URL'
    });

    new cdk.CfnOutput(this, 'UserPoolId', {
      value: this.userPoolId,
      description: 'Cognito User Pool ID'
    });

    new cdk.CfnOutput(this, 'UserPoolClientId', {
      value: this.userPoolClientId,
      description: 'Cognito User Pool Client ID'
    });

    new cdk.CfnOutput(this, 'IdentityPoolId', {
      value: this.identityPoolId,
      description: 'Cognito Identity Pool ID'
    });
  }

  private createLambdaFunctions(
    environment: string,
    config: any,
    uploadsBucket: s3.Bucket,
    reportsBucket: s3.Bucket,
    usersTable: dynamodb.Table,
    scansTable: dynamodb.Table,
    resultsTable: dynamodb.Table,
    kmsKey: kms.Key,
    commonLayer: lambda.LayerVersion
  ): { [key: string]: lambda.Function } {
    // Common environment variables
    const commonEnvVars = {
      ENVIRONMENT: environment,
      LOG_LEVEL: config.logLevel,
      UPLOADS_BUCKET: uploadsBucket.bucketName,
      REPORTS_BUCKET: reportsBucket.bucketName,
      USERS_TABLE: usersTable.tableName,
      SCANS_TABLE: scansTable.tableName,
      RESULTS_TABLE: resultsTable.tableName,
      KMS_KEY_ID: kmsKey.keyId
    };

    // Common IAM policies
    const comprehendPolicy = new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'comprehend:DetectPiiEntities',
        'comprehend:DetectEntities',
        'comprehend:DetectSentiment',
        'comprehend:ClassifyDocument'
      ],
      resources: ['*']
    });

    const rekognitionPolicy = new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'rekognition:DetectText',
        'rekognition:DetectModerationLabels',
        'rekognition:DetectFaces'
      ],
      resources: ['*']
    });

    const bedrockPolicy = new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'bedrock:InvokeModel',
        'bedrock:InvokeModelWithResponseStream'
      ],
      resources: ['*']
    });

    const functions: { [key: string]: lambda.Function } = {};

    // Auth Lambda
    functions.auth = new lambda.Function(this, 'AuthFunction', {
      functionName: `ai-scanner-auth-${environment}`,
      runtime: lambda.Runtime.NODEJS_18_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('../backend/functions/auth'),
      layers: [commonLayer],
      environment: commonEnvVars,
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      logRetention: logs.RetentionDays.ONE_WEEK
    });

    // File Upload Lambda
    functions.upload = new lambda.Function(this, 'UploadFunction', {
      functionName: `ai-scanner-upload-${environment}`,
      runtime: lambda.Runtime.NODEJS_18_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('../backend/functions/upload'),
      layers: [commonLayer],
      environment: commonEnvVars,
      timeout: cdk.Duration.seconds(30),
      memorySize: 512,
      logRetention: logs.RetentionDays.ONE_WEEK
    });

    // Scan Orchestrator Lambda
    functions.scanOrchestrator = new lambda.Function(this, 'ScanOrchestratorFunction', {
      functionName: `ai-scanner-orchestrator-${environment}`,
      runtime: lambda.Runtime.PYTHON_3_9,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('../backend/functions/scan-orchestrator'),
      layers: [commonLayer],
      environment: commonEnvVars,
      timeout: cdk.Duration.minutes(5),
      memorySize: 1024,
      logRetention: logs.RetentionDays.ONE_WEEK
    });

    // Security Scanner Lambda
    functions.securityScanner = new lambda.Function(this, 'SecurityScannerFunction', {
      functionName: `ai-scanner-security-${environment}`,
      runtime: lambda.Runtime.PYTHON_3_9,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('../backend/functions/security-scanner'),
      layers: [commonLayer],
      environment: commonEnvVars,
      timeout: cdk.Duration.minutes(10),
      memorySize: 2048,
      logRetention: logs.RetentionDays.ONE_WEEK
    });

    // PII Detection Lambda
    functions.piiDetector = new lambda.Function(this, 'PiiDetectorFunction', {
      functionName: `ai-scanner-pii-${environment}`,
      runtime: lambda.Runtime.PYTHON_3_9,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('../backend/functions/pii-detector'),
      layers: [commonLayer],
      environment: commonEnvVars,
      timeout: cdk.Duration.minutes(10),
      memorySize: 1024,
      logRetention: logs.RetentionDays.ONE_WEEK
    });

    // Anomaly Detection Lambda
    functions.anomalyDetector = new lambda.Function(this, 'AnomalyDetectorFunction', {
      functionName: `ai-scanner-anomaly-${environment}`,
      runtime: lambda.Runtime.PYTHON_3_9,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('../backend/functions/anomaly-detector'),
      layers: [commonLayer],
      environment: commonEnvVars,
      timeout: cdk.Duration.minutes(15),
      memorySize: 3008,
      logRetention: logs.RetentionDays.ONE_WEEK
    });

    // Report Generator Lambda
    functions.reportGenerator = new lambda.Function(this, 'ReportGeneratorFunction', {
      functionName: `ai-scanner-report-${environment}`,
      runtime: lambda.Runtime.PYTHON_3_9,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('../backend/functions/report-generator'),
      layers: [commonLayer],
      environment: commonEnvVars,
      timeout: cdk.Duration.minutes(10),
      memorySize: 2048,
      logRetention: logs.RetentionDays.ONE_WEEK
    });

    // Grant permissions to Lambda functions
    Object.values(functions).forEach(func => {
      uploadsBucket.grantReadWrite(func);
      reportsBucket.grantReadWrite(func);
      usersTable.grantReadWriteData(func);
      scansTable.grantReadWriteData(func);
      resultsTable.grantReadWriteData(func);
      kmsKey.grantEncryptDecrypt(func);

      func.addToRolePolicy(comprehendPolicy);
      func.addToRolePolicy(rekognitionPolicy);
      func.addToRolePolicy(bedrockPolicy);
    });

    return functions;
  }

  private createStepFunction(environment: string, lambdaFunctions: { [key: string]: lambda.Function }): stepfunctions.StateMachine {
    // Define Lambda tasks
    const securityScanTask = new stepfunctionsTasks.LambdaInvoke(this, 'SecurityScanTask', {
      lambdaFunction: lambdaFunctions.securityScanner,
      outputPath: '$.Payload'
    });

    const piiDetectionTask = new stepfunctionsTasks.LambdaInvoke(this, 'PiiDetectionTask', {
      lambdaFunction: lambdaFunctions.piiDetector,
      outputPath: '$.Payload'
    });

    const anomalyDetectionTask = new stepfunctionsTasks.LambdaInvoke(this, 'AnomalyDetectionTask', {
      lambdaFunction: lambdaFunctions.anomalyDetector,
      outputPath: '$.Payload'
    });

    const reportGenerationTask = new stepfunctionsTasks.LambdaInvoke(this, 'ReportGenerationTask', {
      lambdaFunction: lambdaFunctions.reportGenerator,
      outputPath: '$.Payload'
    });

    // Define parallel analysis
    const parallelAnalysis = new stepfunctions.Parallel(this, 'ParallelAnalysis', {
      comment: 'Run security, PII, and anomaly detection in parallel'
    });

    parallelAnalysis.branch(securityScanTask);
    parallelAnalysis.branch(piiDetectionTask);
    parallelAnalysis.branch(anomalyDetectionTask);

    // Define the workflow
    const definition = parallelAnalysis.next(reportGenerationTask);

    // Create the state machine
    const stateMachine = new stepfunctions.StateMachine(this, 'ScanWorkflow', {
      stateMachineName: `ai-scanner-workflow-${environment}`,
      definition,
      timeout: cdk.Duration.minutes(30),
      logs: {
        destination: new logs.LogGroup(this, 'StepFunctionLogs', {
          logGroupName: `/aws/stepfunctions/ai-scanner-${environment}`,
          retention: logs.RetentionDays.ONE_WEEK,
          removalPolicy: cdk.RemovalPolicy.DESTROY
        }),
        level: stepfunctions.LogLevel.ALL
      }
    });

    // Grant Step Functions permission to invoke Lambda functions
    Object.values(lambdaFunctions).forEach(func => {
      stateMachine.grantTaskResponse(func);
    });

    return stateMachine;
  }

  private createApiGateway(environment: string, userPool: cognito.UserPool, lambdaFunctions: { [key: string]: lambda.Function }): apigateway.RestApi {
    // Create Cognito authorizer
    const authorizer = new apigateway.CognitoUserPoolsAuthorizer(this, 'ApiAuthorizer', {
      cognitoUserPools: [userPool],
      authorizerName: `ai-scanner-authorizer-${environment}`,
      identitySource: 'method.request.header.Authorization'
    });

    // Create API Gateway
    const api = new apigateway.RestApi(this, 'AiScannerApi', {
      restApiName: `ai-scanner-api-${environment}`,
      description: `AI Security Scanner API - ${environment}`,
      defaultCorsPreflightOptions: {
        allowOrigins: environment === 'prod'
          ? ['https://ai-security-scanner.com']
          : ['http://localhost:3000'],
        allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowHeaders: ['Content-Type', 'Authorization', 'X-Amz-Date', 'X-Api-Key', 'X-Amz-Security-Token']
      },
      deployOptions: {
        stageName: environment,
        loggingLevel: apigateway.MethodLoggingLevel.INFO,
        dataTraceEnabled: true,
        metricsEnabled: true
      }
    });

    // Create Lambda integrations
    const authIntegration = new apigateway.LambdaIntegration(lambdaFunctions.auth);
    const uploadIntegration = new apigateway.LambdaIntegration(lambdaFunctions.upload);
    const orchestratorIntegration = new apigateway.LambdaIntegration(lambdaFunctions.scanOrchestrator);

    // Auth endpoints (no authorization required)
    const authResource = api.root.addResource('auth');
    authResource.addMethod('POST', authIntegration);

    // Upload endpoints (authorization required)
    const uploadResource = api.root.addResource('upload');
    uploadResource.addMethod('POST', uploadIntegration, {
      authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO
    });

    // Scan endpoints (authorization required)
    const scanResource = api.root.addResource('scan');
    scanResource.addMethod('POST', orchestratorIntegration, {
      authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO
    });

    const scanStatusResource = scanResource.addResource('{scanId}');
    scanStatusResource.addMethod('GET', orchestratorIntegration, {
      authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO
    });

    // Results endpoints (authorization required)
    const resultsResource = api.root.addResource('results');
    resultsResource.addMethod('GET', orchestratorIntegration, {
      authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO
    });

    const resultResource = resultsResource.addResource('{scanId}');
    resultResource.addMethod('GET', orchestratorIntegration, {
      authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO
    });

    return api;
  }
}
