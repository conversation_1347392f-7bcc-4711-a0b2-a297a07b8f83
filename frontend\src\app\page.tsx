'use client';

import { useAuthenticator } from '@aws-amplify/ui-react';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { LandingPage } from '@/components/LandingPage';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

export default function HomePage() {
  const { authStatus, user } = useAuthenticator((context) => [context.authStatus, context.user]);
  const router = useRouter();

  useEffect(() => {
    if (authStatus === 'authenticated' && user) {
      router.push('/dashboard');
    }
  }, [authStatus, user, router]);

  if (authStatus === 'configuring') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (authStatus === 'authenticated') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
        <p className="ml-3 text-gray-600">Redirecting to dashboard...</p>
      </div>
    );
  }

  return <LandingPage />;
}
