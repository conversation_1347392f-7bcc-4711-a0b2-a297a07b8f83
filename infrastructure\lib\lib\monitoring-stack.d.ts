import * as cdk from 'aws-cdk-lib';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import * as stepfunctions from 'aws-cdk-lib/aws-stepfunctions';
import { Construct } from 'constructs';
export interface MonitoringStackProps extends cdk.StackProps {
    environment: string;
    config: any;
    apiGateway: apigateway.RestApi;
    lambdaFunctions: {
        [key: string]: lambda.Function;
    };
    stepFunction: stepfunctions.StateMachine;
}
export declare class MonitoringStack extends cdk.Stack {
    constructor(scope: Construct, id: string, props: MonitoringStackProps);
    private createAlarms;
}
