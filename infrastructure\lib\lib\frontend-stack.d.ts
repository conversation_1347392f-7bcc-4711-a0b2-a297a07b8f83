import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
export interface FrontendStackProps extends cdk.StackProps {
    environment: string;
    config: any;
    apiGatewayUrl: string;
    userPoolId: string;
    userPoolClientId: string;
    identityPoolId: string;
}
export declare class FrontendStack extends cdk.Stack {
    readonly distributionDomainName: string;
    readonly distributionId: string;
    constructor(scope: Construct, id: string, props: FrontendStackProps);
    private getPlaceholderHtml;
}
