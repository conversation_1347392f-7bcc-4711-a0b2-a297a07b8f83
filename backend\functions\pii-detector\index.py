import json
import boto3
import os
import tempfile
import re
from datetime import datetime
from typing import Dict, Any, List, Optional
import structlog
import chardet
from PyPDF2 import PdfReader
from docx import Document
import openpyxl

# Configure logging
logger = structlog.get_logger()

# Initialize AWS clients
s3 = boto3.client('s3')
comprehend = boto3.client('comprehend')
dynamodb = boto3.resource('dynamodb')

# Environment variables
UPLOADS_BUCKET = os.environ['UPLOADS_BUCKET']
RESULTS_TABLE = os.environ['RESULTS_TABLE']

# PII patterns for additional detection
PII_PATTERNS = {
    'ssn': [
        r'\b\d{3}-\d{2}-\d{4}\b',  # XXX-XX-XXXX
        r'\b\d{3}\s\d{2}\s\d{4}\b',  # XXX XX XXXX
        r'\b\d{9}\b'  # XXXXXXXXX
    ],
    'phone': [
        r'\b\d{3}-\d{3}-\d{4}\b',  # XXX-XXX-XXXX
        r'\(\d{3}\)\s?\d{3}-\d{4}',  # (XXX) XXX-XXXX
        r'\b\d{10}\b'  # XXXXXXXXXX
    ],
    'email': [
        r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    ],
    'credit_card': [
        r'\b4[0-9]{12}(?:[0-9]{3})?\b',  # Visa
        r'\b5[1-5][0-9]{14}\b',  # MasterCard
        r'\b3[47][0-9]{13}\b',  # American Express
        r'\b6(?:011|5[0-9]{2})[0-9]{12}\b'  # Discover
    ],
    'ip_address': [
        r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'
    ],
    'date_of_birth': [
        r'\b\d{1,2}[/-]\d{1,2}[/-]\d{4}\b',  # MM/DD/YYYY or MM-DD-YYYY
        r'\b\d{4}[/-]\d{1,2}[/-]\d{1,2}\b'   # YYYY/MM/DD or YYYY-MM-DD
    ]
}

# Compliance frameworks
COMPLIANCE_FRAMEWORKS = {
    'GDPR': {
        'description': 'General Data Protection Regulation',
        'pii_types': ['email', 'phone', 'name', 'address', 'date_of_birth', 'ip_address'],
        'severity_weights': {'email': 3, 'phone': 3, 'name': 2, 'address': 4, 'date_of_birth': 4, 'ip_address': 2}
    },
    'HIPAA': {
        'description': 'Health Insurance Portability and Accountability Act',
        'pii_types': ['ssn', 'date_of_birth', 'name', 'address', 'phone', 'email'],
        'severity_weights': {'ssn': 5, 'date_of_birth': 4, 'name': 3, 'address': 3, 'phone': 3, 'email': 2}
    },
    'PCI_DSS': {
        'description': 'Payment Card Industry Data Security Standard',
        'pii_types': ['credit_card', 'name', 'address'],
        'severity_weights': {'credit_card': 5, 'name': 2, 'address': 3}
    },
    'CCPA': {
        'description': 'California Consumer Privacy Act',
        'pii_types': ['email', 'phone', 'name', 'address', 'ssn', 'ip_address'],
        'severity_weights': {'email': 3, 'phone': 3, 'name': 2, 'address': 3, 'ssn': 5, 'ip_address': 2}
    }
}

def download_file_from_s3(s3_key: str) -> str:
    """Download file from S3 to temporary location"""
    try:
        temp_file = tempfile.NamedTemporaryFile(delete=False)
        s3.download_fileobj(UPLOADS_BUCKET, s3_key, temp_file)
        temp_file.close()
        return temp_file.name
    except Exception as e:
        logger.error("Failed to download file from S3", s3_key=s3_key, error=str(e))
        raise

def extract_text_from_file(file_path: str, mime_type: str) -> str:
    """Extract text content from various file types"""
    try:
        if mime_type == 'application/pdf':
            return extract_text_from_pdf(file_path)
        elif mime_type in ['application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/msword']:
            return extract_text_from_docx(file_path)
        elif mime_type in ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']:
            return extract_text_from_excel(file_path)
        elif mime_type.startswith('text/'):
            return extract_text_from_text_file(file_path)
        else:
            return extract_text_from_text_file(file_path)
    except Exception as e:
        logger.error("Failed to extract text from file", file_path=file_path, mime_type=mime_type, error=str(e))
        return ""

def extract_text_from_pdf(file_path: str) -> str:
    """Extract text from PDF file"""
    try:
        text = ""
        with open(file_path, 'rb') as file:
            pdf_reader = PdfReader(file)
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
        return text
    except Exception as e:
        logger.error("Failed to extract text from PDF", error=str(e))
        return ""

def extract_text_from_docx(file_path: str) -> str:
    """Extract text from DOCX file"""
    try:
        doc = Document(file_path)
        text = ""
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
        return text
    except Exception as e:
        logger.error("Failed to extract text from DOCX", error=str(e))
        return ""

def extract_text_from_excel(file_path: str) -> str:
    """Extract text from Excel file"""
    try:
        workbook = openpyxl.load_workbook(file_path, data_only=True)
        text = ""
        for sheet_name in workbook.sheetnames:
            sheet = workbook[sheet_name]
            for row in sheet.iter_rows(values_only=True):
                for cell in row:
                    if cell is not None:
                        text += str(cell) + " "
                text += "\n"
        return text
    except Exception as e:
        logger.error("Failed to extract text from Excel", error=str(e))
        return ""

def extract_text_from_text_file(file_path: str) -> str:
    """Extract text from text file"""
    try:
        with open(file_path, 'rb') as f:
            raw_data = f.read(10000)
            encoding = chardet.detect(raw_data).get('encoding', 'utf-8')
        
        with open(file_path, 'r', encoding=encoding, errors='ignore') as file:
            return file.read()
    except Exception as e:
        logger.error("Failed to extract text from text file", error=str(e))
        return ""

def detect_pii_with_comprehend(text: str) -> List[Dict[str, Any]]:
    """Use Amazon Comprehend to detect PII entities"""
    try:
        # Split text into chunks if it's too long (Comprehend has a 5000 byte limit)
        max_bytes = 4500  # Leave some buffer
        text_bytes = text.encode('utf-8')
        
        if len(text_bytes) <= max_bytes:
            chunks = [text]
        else:
            # Split into chunks
            chunks = []
            current_chunk = ""
            for line in text.split('\n'):
                if len((current_chunk + line + '\n').encode('utf-8')) > max_bytes:
                    if current_chunk:
                        chunks.append(current_chunk)
                        current_chunk = line + '\n'
                    else:
                        # Single line is too long, truncate it
                        chunks.append(line[:max_bytes].decode('utf-8', errors='ignore'))
                else:
                    current_chunk += line + '\n'
            
            if current_chunk:
                chunks.append(current_chunk)
        
        all_entities = []
        offset = 0
        
        for chunk in chunks:
            try:
                response = comprehend.detect_pii_entities(
                    Text=chunk,
                    LanguageCode='en'
                )
                
                for entity in response.get('Entities', []):
                    entity_with_offset = entity.copy()
                    entity_with_offset['BeginOffset'] += offset
                    entity_with_offset['EndOffset'] += offset
                    all_entities.append(entity_with_offset)
                
                offset += len(chunk.encode('utf-8'))
                
            except Exception as e:
                logger.warning("Failed to process chunk with Comprehend", error=str(e))
                continue
        
        return all_entities
        
    except Exception as e:
        logger.error("Failed to detect PII with Comprehend", error=str(e))
        return []

def detect_pii_with_patterns(text: str) -> Dict[str, List[Dict[str, Any]]]:
    """Detect PII using regex patterns"""
    findings = {}
    
    for pii_type, patterns in PII_PATTERNS.items():
        type_findings = []
        
        for pattern in patterns:
            try:
                matches = re.finditer(pattern, text, re.MULTILINE | re.IGNORECASE)
                for match in matches:
                    finding = {
                        'type': pii_type,
                        'value': match.group(0),
                        'start': match.start(),
                        'end': match.end(),
                        'confidence': 0.8,  # Pattern-based detection has lower confidence
                        'line_number': text[:match.start()].count('\n') + 1,
                        'context': get_context(text, match.start(), match.end())
                    }
                    type_findings.append(finding)
            except re.error as e:
                logger.warning("Invalid regex pattern", pattern=pattern, error=str(e))
        
        if type_findings:
            findings[pii_type] = type_findings
    
    return findings

def get_context(text: str, start: int, end: int, context_size: int = 50) -> str:
    """Get context around a match"""
    context_start = max(0, start - context_size)
    context_end = min(len(text), end + context_size)
    return text[context_start:context_end]

def assess_compliance_violations(pii_findings: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
    """Assess compliance violations based on detected PII"""
    violations = {}
    
    for framework, config in COMPLIANCE_FRAMEWORKS.items():
        framework_violations = []
        severity_score = 0
        
        for pii_type in config['pii_types']:
            if pii_type in pii_findings:
                count = len(pii_findings[pii_type])
                weight = config['severity_weights'].get(pii_type, 1)
                severity_score += count * weight
                
                framework_violations.append({
                    'pii_type': pii_type,
                    'count': count,
                    'severity_weight': weight,
                    'description': f'Found {count} instances of {pii_type}'
                })
        
        if framework_violations:
            # Determine violation level
            if severity_score >= 20:
                violation_level = 'CRITICAL'
            elif severity_score >= 15:
                violation_level = 'HIGH'
            elif severity_score >= 10:
                violation_level = 'MEDIUM'
            elif severity_score >= 5:
                violation_level = 'LOW'
            else:
                violation_level = 'MINIMAL'
            
            violations[framework] = {
                'description': config['description'],
                'violation_level': violation_level,
                'severity_score': severity_score,
                'violations': framework_violations,
                'total_pii_instances': sum(v['count'] for v in framework_violations)
            }
    
    return violations

def save_results(scan_id: str, results: Dict[str, Any]) -> None:
    """Save scan results to DynamoDB"""
    try:
        table = dynamodb.Table(RESULTS_TABLE)
        
        item = {
            'scanId': scan_id,
            'resultType': 'pii',
            'results': results,
            'timestamp': datetime.utcnow().isoformat(),
            'ttl': int(datetime.utcnow().timestamp()) + (30 * 24 * 60 * 60)  # 30 days TTL
        }
        
        table.put_item(Item=item)
        logger.info("PII scan results saved", scan_id=scan_id)
        
    except Exception as e:
        logger.error("Failed to save results", scan_id=scan_id, error=str(e))
        raise

def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """Main Lambda handler for PII detection"""
    logger.info("PII detection started", event=event)
    
    try:
        # Extract parameters from event
        scan_id = event.get('scanId')
        s3_key = event.get('s3Key')
        
        if not scan_id or not s3_key:
            raise ValueError("Missing required parameters: scanId and s3Key")
        
        # Download file from S3
        local_file_path = download_file_from_s3(s3_key)
        
        try:
            # Determine MIME type from S3 metadata
            s3_response = s3.head_object(Bucket=UPLOADS_BUCKET, Key=s3_key)
            mime_type = s3_response.get('ContentType', 'application/octet-stream')
            
            # Extract text content
            text_content = extract_text_from_file(local_file_path, mime_type)
            
            if not text_content.strip():
                logger.warning("No text content extracted from file", scan_id=scan_id)
                text_content = ""
            
            # Detect PII using Amazon Comprehend
            comprehend_entities = detect_pii_with_comprehend(text_content)
            
            # Detect PII using regex patterns
            pattern_findings = detect_pii_with_patterns(text_content)
            
            # Combine findings
            all_pii_findings = {}
            
            # Process Comprehend entities
            for entity in comprehend_entities:
                pii_type = entity['Type'].lower()
                if pii_type not in all_pii_findings:
                    all_pii_findings[pii_type] = []
                
                finding = {
                    'type': pii_type,
                    'value': text_content[entity['BeginOffset']:entity['EndOffset']],
                    'start': entity['BeginOffset'],
                    'end': entity['EndOffset'],
                    'confidence': entity['Score'],
                    'source': 'comprehend',
                    'line_number': text_content[:entity['BeginOffset']].count('\n') + 1,
                    'context': get_context(text_content, entity['BeginOffset'], entity['EndOffset'])
                }
                all_pii_findings[pii_type].append(finding)
            
            # Add pattern-based findings
            for pii_type, findings in pattern_findings.items():
                if pii_type not in all_pii_findings:
                    all_pii_findings[pii_type] = []
                
                for finding in findings:
                    finding['source'] = 'pattern'
                    all_pii_findings[pii_type].append(finding)
            
            # Assess compliance violations
            compliance_violations = assess_compliance_violations(all_pii_findings)
            
            # Calculate overall risk score
            total_pii_count = sum(len(findings) for findings in all_pii_findings.values())
            risk_score = min(total_pii_count * 5, 100)  # Cap at 100
            
            # Determine risk level
            if risk_score >= 80:
                risk_level = 'CRITICAL'
            elif risk_score >= 60:
                risk_level = 'HIGH'
            elif risk_score >= 40:
                risk_level = 'MEDIUM'
            elif risk_score >= 20:
                risk_level = 'LOW'
            else:
                risk_level = 'MINIMAL'
            
            # Prepare results
            results = {
                'scanType': 'pii',
                'status': 'completed',
                'riskScore': risk_score,
                'riskLevel': risk_level,
                'piiFindings': all_pii_findings,
                'complianceViolations': compliance_violations,
                'summary': {
                    'totalPiiInstances': total_pii_count,
                    'piiTypesFound': list(all_pii_findings.keys()),
                    'complianceFrameworksViolated': list(compliance_violations.keys()),
                    'highestViolationLevel': max([v['violation_level'] for v in compliance_violations.values()], default='NONE')
                },
                'completedAt': datetime.utcnow().isoformat()
            }
            
            # Save results
            save_results(scan_id, results)
            
            logger.info("PII detection completed", 
                       scan_id=scan_id, 
                       risk_score=risk_score, 
                       risk_level=risk_level,
                       total_pii=total_pii_count)
            
            return results
            
        finally:
            # Clean up temporary file
            try:
                os.unlink(local_file_path)
            except Exception:
                pass
    
    except Exception as e:
        logger.error("PII detection failed", scan_id=scan_id, error=str(e))
        
        error_results = {
            'scanType': 'pii',
            'status': 'failed',
            'error': str(e),
            'completedAt': datetime.utcnow().isoformat()
        }
        
        try:
            save_results(scan_id, error_results)
        except Exception:
            pass
        
        raise
