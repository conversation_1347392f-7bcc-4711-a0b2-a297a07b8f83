{"name": "aws-ai-security-scanner", "version": "1.0.0", "description": "AI-powered security and compliance scanner using AWS cloud services", "main": "index.js", "scripts": {"install:all": "npm run install:infrastructure && npm run install:backend && npm run install:frontend", "install:infrastructure": "cd infrastructure && npm install", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install", "build": "npm run build:infrastructure && npm run build:backend && npm run build:frontend", "build:infrastructure": "cd infrastructure && npm run build", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "deploy": "npm run deploy:infrastructure && npm run deploy:backend", "deploy:infrastructure": "cd infrastructure && npm run deploy", "deploy:backend": "cd backend && npm run deploy", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm run test", "test:frontend": "cd frontend && npm run test", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "clean": "npm run clean:infrastructure && npm run clean:backend && npm run clean:frontend", "clean:infrastructure": "cd infrastructure && npm run clean", "clean:backend": "cd backend && npm run clean", "clean:frontend": "cd frontend && npm run clean"}, "keywords": ["aws", "security", "compliance", "ai", "ml", "scanner", "serverless", "lambda", "s3", "dynamodb", "comprehend", "rekognition", "sagemaker", "bedrock"], "author": "AWS AI Security Scanner Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "husky": "^8.0.3", "lint-staged": "^15.2.0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/aws-ai-security-scanner.git"}, "bugs": {"url": "https://github.com/your-org/aws-ai-security-scanner/issues"}, "homepage": "https://github.com/your-org/aws-ai-security-scanner#readme"}