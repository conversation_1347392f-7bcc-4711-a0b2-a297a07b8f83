{"name": "ai-scanner-auth-function", "version": "1.0.0", "description": "Authentication Lambda function for AI Security Scanner", "main": "index.js", "scripts": {"build": "npm run clean && npm run compile", "compile": "tsc", "clean": "rimraf dist *.js *.js.map", "test": "jest", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "dev": "npm run build && sam local start-api"}, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.450.0", "@aws-sdk/client-dynamodb": "^3.450.0", "@aws-sdk/lib-dynamodb": "^3.450.0", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "uuid": "^9.0.1"}, "devDependencies": {"@types/aws-lambda": "^8.10.126", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.9.0", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "eslint": "^8.53.0", "jest": "^29.7.0", "rimraf": "^5.0.5", "typescript": "^5.2.2"}, "keywords": ["aws", "lambda", "authentication", "cognito"], "author": "AWS AI Security Scanner Team", "license": "MIT"}