"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[840],{99376:function(t,e,r){var i=r(35475);r.o(i,"useRouter")&&r.d(e,{useRouter:function(){return i.useRouter}})},16331:function(t,e,r){var i=r(2265);let n=i.forwardRef(function(t,e){let{title:r,titleId:n,...o}=t;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":n},o),r?i.createElement("title",{id:n},r):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))});e.Z=n},17653:function(t,e,r){var i=r(2265);let n=i.forwardRef(function(t,e){let{title:r,titleId:n,...o}=t;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":n},o),r?i.createElement("title",{id:n},r):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 16.5V9.75m0 0 3 3m-3-3-3 3M6.75 19.5a4.5 4.5 0 0 1-1.41-8.775 5.25 5.25 0 0 1 10.233-2.33 3 3 0 0 1 3.758 3.848A3.752 3.752 0 0 1 18 19.5H6.75Z"}))});e.Z=n},31331:function(t,e,r){var i=r(2265);let n=i.forwardRef(function(t,e){let{title:r,titleId:n,...o}=t;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":n},o),r?i.createElement("title",{id:n},r):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z"}))});e.Z=n},91849:function(t,e,r){var i=r(2265);let n=i.forwardRef(function(t,e){let{title:r,titleId:n,...o}=t;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":n},o),r?i.createElement("title",{id:n},r):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m5.231 13.481L15 17.25m-4.5-15H5.625c-.621 0-1.125.504-1.125 1.125v16.5c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Zm3.75 11.625a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))});e.Z=n},94675:function(t,e,r){var i=r(2265);let n=i.forwardRef(function(t,e){let{title:r,titleId:n,...o}=t;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":n},o),r?i.createElement("title",{id:n},r):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"}))});e.Z=n},68690:function(t,e,r){var i=r(2265);let n=i.forwardRef(function(t,e){let{title:r,titleId:n,...o}=t;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":n},o),r?i.createElement("title",{id:n},r):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))});e.Z=n},61994:function(t,e,r){r.d(e,{W:function(){return i}});function i(){for(var t,e,r=0,i="",n=arguments.length;r<n;r++)(t=arguments[r])&&(e=function t(e){var r,i,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e){if(Array.isArray(e)){var o=e.length;for(r=0;r<o;r++)e[r]&&(i=t(e[r]))&&(n&&(n+=" "),n+=i)}else for(i in e)e[i]&&(n&&(n+=" "),n+=i)}return n}(t))&&(i&&(i+=" "),i+=e);return i}},13287:function(t,e,r){let i;r.d(e,{E:function(){return nX}});var n,o,s=r(2265);let a=(0,s.createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),l=(0,s.createContext)({}),u=(0,s.createContext)(null),h="undefined"!=typeof document,c=h?s.useLayoutEffect:s.useEffect,d=(0,s.createContext)({strict:!1}),p=t=>t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),m="data-"+p("framerAppearId");function f(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function g(t){return"string"==typeof t||Array.isArray(t)}function v(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}let y=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],b=["initial",...y];function x(t){return v(t.animate)||b.some(e=>g(t[e]))}function w(t){return!!(x(t)||t.variants)}function P(t){return Array.isArray(t)?t.join(" "):t}let T={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},A={};for(let t in T)A[t]={isEnabled:e=>T[t].some(t=>!!e[t])};let S=(0,s.createContext)({}),k=(0,s.createContext)({}),V=Symbol.for("motionComponentSymbol"),E=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function C(t){if("string"!=typeof t||t.includes("-"));else if(E.indexOf(t)>-1||/[A-Z]/.test(t))return!0;return!1}let M={},j=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],D=new Set(j);function R(t,{layout:e,layoutId:r}){return D.has(t)||t.startsWith("origin")||(e||void 0!==r)&&(!!M[t]||"opacity"===t)}let L=t=>!!(t&&t.getVelocity),B={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},F=j.length,O=t=>e=>"string"==typeof e&&e.startsWith(t),I=O("--"),z=O("var(--"),U=(t,e)=>e&&"number"==typeof t?e.transform(t):t,N=(t,e,r)=>Math.min(Math.max(r,t),e),$={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},W={...$,transform:t=>N(0,1,t)},H={...$,default:1},G=t=>Math.round(1e5*t)/1e5,Z=/(-)?([\d]*\.?[\d])+/g,Y=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,X=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function q(t){return"string"==typeof t}let _=t=>({test:e=>q(e)&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),K=_("deg"),J=_("%"),Q=_("px"),tt=_("vh"),te=_("vw"),tr={...J,parse:t=>J.parse(t)/100,transform:t=>J.transform(100*t)},ti={...$,transform:Math.round},tn={borderWidth:Q,borderTopWidth:Q,borderRightWidth:Q,borderBottomWidth:Q,borderLeftWidth:Q,borderRadius:Q,radius:Q,borderTopLeftRadius:Q,borderTopRightRadius:Q,borderBottomRightRadius:Q,borderBottomLeftRadius:Q,width:Q,maxWidth:Q,height:Q,maxHeight:Q,size:Q,top:Q,right:Q,bottom:Q,left:Q,padding:Q,paddingTop:Q,paddingRight:Q,paddingBottom:Q,paddingLeft:Q,margin:Q,marginTop:Q,marginRight:Q,marginBottom:Q,marginLeft:Q,rotate:K,rotateX:K,rotateY:K,rotateZ:K,scale:H,scaleX:H,scaleY:H,scaleZ:H,skew:K,skewX:K,skewY:K,distance:Q,translateX:Q,translateY:Q,translateZ:Q,x:Q,y:Q,z:Q,perspective:Q,transformPerspective:Q,opacity:W,originX:tr,originY:tr,originZ:Q,zIndex:ti,fillOpacity:W,strokeOpacity:W,numOctaves:ti};function to(t,e,r,i){let{style:n,vars:o,transform:s,transformOrigin:a}=t,l=!1,u=!1,h=!0;for(let t in e){let r=e[t];if(I(t)){o[t]=r;continue}let i=tn[t],c=U(r,i);if(D.has(t)){if(l=!0,s[t]=c,!h)continue;r!==(i.default||0)&&(h=!1)}else t.startsWith("origin")?(u=!0,a[t]=c):n[t]=c}if(!e.transform&&(l||i?n.transform=function(t,{enableHardwareAcceleration:e=!0,allowTransformNone:r=!0},i,n){let o="";for(let e=0;e<F;e++){let r=j[e];if(void 0!==t[r]){let e=B[r]||r;o+=`${e}(${t[r]}) `}}return e&&!t.z&&(o+="translateZ(0)"),o=o.trim(),n?o=n(t,i?"":o):r&&i&&(o="none"),o}(t.transform,r,h,i):n.transform&&(n.transform="none")),u){let{originX:t="50%",originY:e="50%",originZ:r=0}=a;n.transformOrigin=`${t} ${e} ${r}`}}let ts=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function ta(t,e,r){for(let i in e)L(e[i])||R(i,r)||(t[i]=e[i])}let tl=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function tu(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||tl.has(t)}let th=t=>!tu(t);try{(n=require("@emotion/is-prop-valid").default)&&(th=t=>t.startsWith("on")?!tu(t):n(t))}catch(t){}function tc(t,e,r){return"string"==typeof t?t:Q.transform(e+r*t)}let td={offset:"stroke-dashoffset",array:"stroke-dasharray"},tp={offset:"strokeDashoffset",array:"strokeDasharray"};function tm(t,{attrX:e,attrY:r,attrScale:i,originX:n,originY:o,pathLength:s,pathSpacing:a=1,pathOffset:l=0,...u},h,c,d){if(to(t,u,h,d),c){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:p,style:m,dimensions:f}=t;p.transform&&(f&&(m.transform=p.transform),delete p.transform),f&&(void 0!==n||void 0!==o||m.transform)&&(m.transformOrigin=function(t,e,r){let i=tc(e,t.x,t.width),n=tc(r,t.y,t.height);return`${i} ${n}`}(f,void 0!==n?n:.5,void 0!==o?o:.5)),void 0!==e&&(p.x=e),void 0!==r&&(p.y=r),void 0!==i&&(p.scale=i),void 0!==s&&function(t,e,r=1,i=0,n=!0){t.pathLength=1;let o=n?td:tp;t[o.offset]=Q.transform(-i);let s=Q.transform(e),a=Q.transform(r);t[o.array]=`${s} ${a}`}(p,s,a,l,!1)}let tf=()=>({...ts(),attrs:{}}),tg=t=>"string"==typeof t&&"svg"===t.toLowerCase();function tv(t,{style:e,vars:r},i,n){for(let o in Object.assign(t.style,e,n&&n.getProjectionStyles(i)),r)t.style.setProperty(o,r[o])}let ty=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function tb(t,e,r,i){for(let r in tv(t,e,void 0,i),e.attrs)t.setAttribute(ty.has(r)?r:p(r),e.attrs[r])}function tx(t,e){let{style:r}=t,i={};for(let n in r)(L(r[n])||e.style&&L(e.style[n])||R(n,t))&&(i[n]=r[n]);return i}function tw(t,e){let r=tx(t,e);for(let i in t)(L(t[i])||L(e[i]))&&(r[-1!==j.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return r}function tP(t,e,r,i={},n={}){return"function"==typeof e&&(e=e(void 0!==r?r:t.custom,i,n)),"string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e&&(e=e(void 0!==r?r:t.custom,i,n)),e}let tT=t=>Array.isArray(t),tA=t=>!!(t&&"object"==typeof t&&t.mix&&t.toValue),tS=t=>tT(t)?t[t.length-1]||0:t;function tk(t){let e=L(t)?t.get():t;return tA(e)?e.toValue():e}let tV=t=>(e,r)=>{let i=(0,s.useContext)(l),n=(0,s.useContext)(u),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e,onMount:r},i,n,o){let s={latestValues:function(t,e,r,i){let n={},o=i(t,{});for(let t in o)n[t]=tk(o[t]);let{initial:s,animate:a}=t,l=x(t),u=w(t);e&&u&&!l&&!1!==t.inherit&&(void 0===s&&(s=e.initial),void 0===a&&(a=e.animate));let h=!!r&&!1===r.initial,c=(h=h||!1===s)?a:s;return c&&"boolean"!=typeof c&&!v(c)&&(Array.isArray(c)?c:[c]).forEach(e=>{let r=tP(t,e);if(!r)return;let{transitionEnd:i,transition:o,...s}=r;for(let t in s){let e=s[t];if(Array.isArray(e)){let t=h?e.length-1:0;e=e[t]}null!==e&&(n[t]=e)}for(let t in i)n[t]=i[t]}),n}(i,n,o,t),renderState:e()};return r&&(s.mount=t=>r(i,t,s)),s})(t,e,i,n);return r?o():function(t){let e=(0,s.useRef)(null);return null===e.current&&(e.current=t()),e.current}(o)},tE=t=>t;class tC{constructor(){this.order=[],this.scheduled=new Set}add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){let e=this.order.indexOf(t);-1!==e&&(this.order.splice(e,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}}let tM=["prepare","read","update","preRender","render","postRender"],{schedule:tj,cancel:tD,state:tR,steps:tL}=function(t,e){let r=!1,i=!0,n={delta:0,timestamp:0,isProcessing:!1},o=tM.reduce((t,e)=>(t[e]=function(t){let e=new tC,r=new tC,i=0,n=!1,o=!1,s=new WeakSet,a={schedule:(t,o=!1,a=!1)=>{let l=a&&n,u=l?e:r;return o&&s.add(t),u.add(t)&&l&&n&&(i=e.order.length),t},cancel:t=>{r.remove(t),s.delete(t)},process:l=>{if(n){o=!0;return}if(n=!0,[e,r]=[r,e],r.clear(),i=e.order.length)for(let r=0;r<i;r++){let i=e.order[r];i(l),s.has(i)&&(a.schedule(i),t())}n=!1,o&&(o=!1,a.process(l))}};return a}(()=>r=!0),t),{}),s=t=>o[t].process(n),a=()=>{let o=performance.now();r=!1,n.delta=i?1e3/60:Math.max(Math.min(o-n.timestamp,40),1),n.timestamp=o,n.isProcessing=!0,tM.forEach(s),n.isProcessing=!1,r&&e&&(i=!1,t(a))},l=()=>{r=!0,i=!0,n.isProcessing||t(a)};return{schedule:tM.reduce((t,e)=>{let i=o[e];return t[e]=(t,e=!1,n=!1)=>(r||l(),i.schedule(t,e,n)),t},{}),cancel:t=>tM.forEach(e=>o[e].cancel(t)),state:n,steps:o}}("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:tE,!0),tB={useVisualState:tV({scrapeMotionValuesFromProps:tw,createRenderState:tf,onMount:(t,e,{renderState:r,latestValues:i})=>{tj.read(()=>{try{r.dimensions="function"==typeof e.getBBox?e.getBBox():e.getBoundingClientRect()}catch(t){r.dimensions={x:0,y:0,width:0,height:0}}}),tj.render(()=>{tm(r,i,{enableHardwareAcceleration:!1},tg(e.tagName),t.transformTemplate),tb(e,r)})}})},tF={useVisualState:tV({scrapeMotionValuesFromProps:tx,createRenderState:ts})};function tO(t,e,r,i={passive:!0}){return t.addEventListener(e,r,i),()=>t.removeEventListener(e,r)}let tI=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function tz(t,e="page"){return{point:{x:t[e+"X"],y:t[e+"Y"]}}}let tU=t=>e=>tI(e)&&t(e,tz(e));function tN(t,e,r,i){return tO(t,e,tU(r),i)}let t$=(t,e)=>r=>e(t(r)),tW=(...t)=>t.reduce(t$);function tH(t){let e=null;return()=>null===e&&(e=t,()=>{e=null})}let tG=tH("dragHorizontal"),tZ=tH("dragVertical");function tY(t){let e=!1;if("y"===t)e=tZ();else if("x"===t)e=tG();else{let t=tG(),r=tZ();t&&r?e=()=>{t(),r()}:(t&&t(),r&&r())}return e}function tX(){let t=tY(!0);return!t||(t(),!1)}class tq{constructor(t){this.isMounted=!1,this.node=t}update(){}}function t_(t,e){let r="onHover"+(e?"Start":"End");return tN(t.current,"pointer"+(e?"enter":"leave"),(i,n)=>{if("touch"===i.pointerType||tX())return;let o=t.getProps();t.animationState&&o.whileHover&&t.animationState.setActive("whileHover",e),o[r]&&tj.update(()=>o[r](i,n))},{passive:!t.getProps()[r]})}class tK extends tq{mount(){this.unmount=tW(t_(this.node,!0),t_(this.node,!1))}unmount(){}}class tJ extends tq{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=tW(tO(this.node.current,"focus",()=>this.onFocus()),tO(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let tQ=(t,e)=>!!e&&(t===e||tQ(t,e.parentElement));function t0(t,e){if(!e)return;let r=new PointerEvent("pointer"+t);e(r,tz(r))}class t1 extends tq{constructor(){super(...arguments),this.removeStartListeners=tE,this.removeEndListeners=tE,this.removeAccessibleListeners=tE,this.startPointerPress=(t,e)=>{if(this.isPressing)return;this.removeEndListeners();let r=this.node.getProps(),i=tN(window,"pointerup",(t,e)=>{if(!this.checkPressEnd())return;let{onTap:r,onTapCancel:i,globalTapTarget:n}=this.node.getProps();tj.update(()=>{n||tQ(this.node.current,t.target)?r&&r(t,e):i&&i(t,e)})},{passive:!(r.onTap||r.onPointerUp)}),n=tN(window,"pointercancel",(t,e)=>this.cancelPress(t,e),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=tW(i,n),this.startPress(t,e)},this.startAccessiblePress=()=>{let t=tO(this.node.current,"keydown",t=>{"Enter"!==t.key||this.isPressing||(this.removeEndListeners(),this.removeEndListeners=tO(this.node.current,"keyup",t=>{"Enter"===t.key&&this.checkPressEnd()&&t0("up",(t,e)=>{let{onTap:r}=this.node.getProps();r&&tj.update(()=>r(t,e))})}),t0("down",(t,e)=>{this.startPress(t,e)}))}),e=tO(this.node.current,"blur",()=>{this.isPressing&&t0("cancel",(t,e)=>this.cancelPress(t,e))});this.removeAccessibleListeners=tW(t,e)}}startPress(t,e){this.isPressing=!0;let{onTapStart:r,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&tj.update(()=>r(t,e))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!tX()}cancelPress(t,e){if(!this.checkPressEnd())return;let{onTapCancel:r}=this.node.getProps();r&&tj.update(()=>r(t,e))}mount(){let t=this.node.getProps(),e=tN(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),r=tO(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=tW(e,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}let t5=new WeakMap,t2=new WeakMap,t3=t=>{let e=t5.get(t.target);e&&e(t)},t6=t=>{t.forEach(t3)},t9={some:0,all:1};class t4 extends tq{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:r,amount:i="some",once:n}=t,o={root:e?e.current:void 0,rootMargin:r,threshold:"number"==typeof i?i:t9[i]};return function(t,e,r){let i=function({root:t,...e}){let r=t||document;t2.has(r)||t2.set(r,{});let i=t2.get(r),n=JSON.stringify(e);return i[n]||(i[n]=new IntersectionObserver(t6,{root:t,...e})),i[n]}(e);return t5.set(t,r),i.observe(t),()=>{t5.delete(t),i.unobserve(t)}}(this.node.current,o,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:r,onViewportLeave:i}=this.node.getProps(),o=e?r:i;o&&o(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return r=>t[r]!==e[r]}(t,e))&&this.startObserver()}unmount(){}}function t7(t,e){if(!Array.isArray(e))return!1;let r=e.length;if(r!==t.length)return!1;for(let i=0;i<r;i++)if(e[i]!==t[i])return!1;return!0}function t8(t,e,r){let i=t.getProps();return tP(i,e,void 0!==r?r:i.custom,function(t){let e={};return t.values.forEach((t,r)=>e[r]=t.get()),e}(t),function(t){let e={};return t.values.forEach((t,r)=>e[r]=t.getVelocity()),e}(t))}let et=t=>1e3*t,ee=t=>t/1e3,er={current:!1},ei=t=>Array.isArray(t)&&"number"==typeof t[0],en=([t,e,r,i])=>`cubic-bezier(${t}, ${e}, ${r}, ${i})`,eo={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:en([0,.65,.55,1]),circOut:en([.55,0,1,.45]),backIn:en([.31,.01,.66,-.59]),backOut:en([.33,1.53,.69,.99])},es=(t,e,r)=>(((1-3*r+3*e)*t+(3*r-6*e))*t+3*e)*t;function ea(t,e,r,i){if(t===e&&r===i)return tE;let n=e=>(function(t,e,r,i,n){let o,s;let a=0;do(o=es(s=e+(r-e)/2,i,n)-t)>0?r=s:e=s;while(Math.abs(o)>1e-7&&++a<12);return s})(e,0,1,t,r);return t=>0===t||1===t?t:es(n(t),e,i)}let el=ea(.42,0,1,1),eu=ea(0,0,.58,1),eh=ea(.42,0,.58,1),ec=t=>Array.isArray(t)&&"number"!=typeof t[0],ed=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,ep=t=>e=>1-t(1-e),em=t=>1-Math.sin(Math.acos(t)),ef=ep(em),eg=ed(em),ev=ea(.33,1.53,.69,.99),ey=ep(ev),eb=ed(ey),ex={linear:tE,easeIn:el,easeInOut:eh,easeOut:eu,circIn:em,circInOut:eg,circOut:ef,backIn:ey,backInOut:eb,backOut:ev,anticipate:t=>(t*=2)<1?.5*ey(t):.5*(2-Math.pow(2,-10*(t-1)))},ew=t=>{if(Array.isArray(t)){tE(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,r,i,n]=t;return ea(e,r,i,n)}return"string"==typeof t?(tE(void 0!==ex[t],`Invalid easing type '${t}'`),ex[t]):t},eP=(t,e)=>r=>!!(q(r)&&X.test(r)&&r.startsWith(t)||e&&Object.prototype.hasOwnProperty.call(r,e)),eT=(t,e,r)=>i=>{if(!q(i))return i;let[n,o,s,a]=i.match(Z);return{[t]:parseFloat(n),[e]:parseFloat(o),[r]:parseFloat(s),alpha:void 0!==a?parseFloat(a):1}},eA=t=>N(0,255,t),eS={...$,transform:t=>Math.round(eA(t))},ek={test:eP("rgb","red"),parse:eT("red","green","blue"),transform:({red:t,green:e,blue:r,alpha:i=1})=>"rgba("+eS.transform(t)+", "+eS.transform(e)+", "+eS.transform(r)+", "+G(W.transform(i))+")"},eV={test:eP("#"),parse:function(t){let e="",r="",i="",n="";return t.length>5?(e=t.substring(1,3),r=t.substring(3,5),i=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),r=t.substring(2,3),i=t.substring(3,4),n=t.substring(4,5),e+=e,r+=r,i+=i,n+=n),{red:parseInt(e,16),green:parseInt(r,16),blue:parseInt(i,16),alpha:n?parseInt(n,16)/255:1}},transform:ek.transform},eE={test:eP("hsl","hue"),parse:eT("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:r,alpha:i=1})=>"hsla("+Math.round(t)+", "+J.transform(G(e))+", "+J.transform(G(r))+", "+G(W.transform(i))+")"},eC={test:t=>ek.test(t)||eV.test(t)||eE.test(t),parse:t=>ek.test(t)?ek.parse(t):eE.test(t)?eE.parse(t):eV.parse(t),transform:t=>q(t)?t:t.hasOwnProperty("red")?ek.transform(t):eE.transform(t)},eM=(t,e,r)=>-r*t+r*e+t;function ej(t,e,r){return(r<0&&(r+=1),r>1&&(r-=1),r<1/6)?t+(e-t)*6*r:r<.5?e:r<2/3?t+(e-t)*(2/3-r)*6:t}let eD=(t,e,r)=>{let i=t*t;return Math.sqrt(Math.max(0,r*(e*e-i)+i))},eR=[eV,ek,eE],eL=t=>eR.find(e=>e.test(t));function eB(t){let e=eL(t);tE(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`);let r=e.parse(t);return e===eE&&(r=function({hue:t,saturation:e,lightness:r,alpha:i}){t/=360,r/=100;let n=0,o=0,s=0;if(e/=100){let i=r<.5?r*(1+e):r+e-r*e,a=2*r-i;n=ej(a,i,t+1/3),o=ej(a,i,t),s=ej(a,i,t-1/3)}else n=o=s=r;return{red:Math.round(255*n),green:Math.round(255*o),blue:Math.round(255*s),alpha:i}}(r)),r}let eF=(t,e)=>{let r=eB(t),i=eB(e),n={...r};return t=>(n.red=eD(r.red,i.red,t),n.green=eD(r.green,i.green,t),n.blue=eD(r.blue,i.blue,t),n.alpha=eM(r.alpha,i.alpha,t),ek.transform(n))},eO={regex:/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,countKey:"Vars",token:"${v}",parse:tE},eI={regex:Y,countKey:"Colors",token:"${c}",parse:eC.parse},ez={regex:Z,countKey:"Numbers",token:"${n}",parse:$.parse};function eU(t,{regex:e,countKey:r,token:i,parse:n}){let o=t.tokenised.match(e);o&&(t["num"+r]=o.length,t.tokenised=t.tokenised.replace(e,i),t.values.push(...o.map(n)))}function eN(t){let e=t.toString(),r={value:e,tokenised:e,values:[],numVars:0,numColors:0,numNumbers:0};return r.value.includes("var(--")&&eU(r,eO),eU(r,eI),eU(r,ez),r}function e$(t){return eN(t).values}function eW(t){let{values:e,numColors:r,numVars:i,tokenised:n}=eN(t),o=e.length;return t=>{let e=n;for(let n=0;n<o;n++)e=n<i?e.replace(eO.token,t[n]):n<i+r?e.replace(eI.token,eC.transform(t[n])):e.replace(ez.token,G(t[n]));return e}}let eH=t=>"number"==typeof t?0:t,eG={test:function(t){var e,r;return isNaN(t)&&q(t)&&((null===(e=t.match(Z))||void 0===e?void 0:e.length)||0)+((null===(r=t.match(Y))||void 0===r?void 0:r.length)||0)>0},parse:e$,createTransformer:eW,getAnimatableNone:function(t){let e=e$(t);return eW(t)(e.map(eH))}},eZ=(t,e)=>r=>`${r>0?e:t}`;function eY(t,e){return"number"==typeof t?r=>eM(t,e,r):eC.test(t)?eF(t,e):t.startsWith("var(")?eZ(t,e):e_(t,e)}let eX=(t,e)=>{let r=[...t],i=r.length,n=t.map((t,r)=>eY(t,e[r]));return t=>{for(let e=0;e<i;e++)r[e]=n[e](t);return r}},eq=(t,e)=>{let r={...t,...e},i={};for(let n in r)void 0!==t[n]&&void 0!==e[n]&&(i[n]=eY(t[n],e[n]));return t=>{for(let e in i)r[e]=i[e](t);return r}},e_=(t,e)=>{let r=eG.createTransformer(e),i=eN(t),n=eN(e);return i.numVars===n.numVars&&i.numColors===n.numColors&&i.numNumbers>=n.numNumbers?tW(eX(i.values,n.values),r):(tE(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eZ(t,e))},eK=(t,e,r)=>{let i=e-t;return 0===i?1:(r-t)/i},eJ=(t,e)=>r=>eM(t,e,r);function eQ(t,e,{clamp:r=!0,ease:i,mixer:n}={}){let o=t.length;if(tE(o===e.length,"Both input and output ranges must be the same length"),1===o)return()=>e[0];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());let s=function(t,e,r){let i=[],n=r||function(t){if("number"==typeof t);else if("string"==typeof t)return eC.test(t)?eF:e_;else if(Array.isArray(t))return eX;else if("object"==typeof t)return eq;return eJ}(t[0]),o=t.length-1;for(let r=0;r<o;r++){let o=n(t[r],t[r+1]);e&&(o=tW(Array.isArray(e)?e[r]||tE:e,o)),i.push(o)}return i}(e,i,n),a=s.length,l=e=>{let r=0;if(a>1)for(;r<t.length-2&&!(e<t[r+1]);r++);let i=eK(t[r],t[r+1],e);return s[r](i)};return r?e=>l(N(t[0],t[o-1],e)):l}function e0({duration:t=300,keyframes:e,times:r,ease:i="easeInOut"}){let n=ec(i)?i.map(ew):ew(i),o={done:!1,value:e[0]},s=eQ((r&&r.length===e.length?r:function(t){let e=[0];return function(t,e){let r=t[t.length-1];for(let i=1;i<=e;i++){let n=eK(0,e,i);t.push(eM(r,1,n))}}(e,t.length-1),e}(e)).map(e=>e*t),e,{ease:Array.isArray(n)?n:e.map(()=>n||eh).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=s(e),o.done=e>=t,o)}}function e1(t,e,r){var i,n;let o=Math.max(e-5,0);return i=r-t(o),(n=e-o)?1e3/n*i:0}function e5(t,e){return t*Math.sqrt(1-e*e)}let e2=["duration","bounce"],e3=["stiffness","damping","mass"];function e6(t,e){return e.some(e=>void 0!==t[e])}function e9({keyframes:t,restDelta:e,restSpeed:r,...i}){let n;let o=t[0],s=t[t.length-1],a={done:!1,value:o},{stiffness:l,damping:u,mass:h,duration:c,velocity:d,isResolvedFromDuration:p}=function(t){let e={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...t};if(!e6(t,e3)&&e6(t,e2)){let r=function({duration:t=800,bounce:e=.25,velocity:r=0,mass:i=1}){let n,o;tE(t<=et(10),"Spring duration must be 10 seconds or less");let s=1-e;s=N(.05,1,s),t=N(.01,10,ee(t)),s<1?(n=e=>{let i=e*s,n=i*t;return .001-(i-r)/e5(e,s)*Math.exp(-n)},o=e=>{let i=e*s*t,o=Math.pow(s,2)*Math.pow(e,2)*t,a=e5(Math.pow(e,2),s);return(i*r+r-o)*Math.exp(-i)*(-n(e)+.001>0?-1:1)/a}):(n=e=>-.001+Math.exp(-e*t)*((e-r)*t+1),o=e=>t*t*(r-e)*Math.exp(-e*t));let a=function(t,e,r){let i=r;for(let r=1;r<12;r++)i-=t(i)/e(i);return i}(n,o,5/t);if(t=et(t),isNaN(a))return{stiffness:100,damping:10,duration:t};{let e=Math.pow(a,2)*i;return{stiffness:e,damping:2*s*Math.sqrt(i*e),duration:t}}}(t);(e={...e,...r,mass:1}).isResolvedFromDuration=!0}return e}({...i,velocity:-ee(i.velocity||0)}),m=d||0,f=u/(2*Math.sqrt(l*h)),g=s-o,v=ee(Math.sqrt(l/h)),y=5>Math.abs(g);if(r||(r=y?.01:2),e||(e=y?.005:.5),f<1){let t=e5(v,f);n=e=>s-Math.exp(-f*v*e)*((m+f*v*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}else if(1===f)n=t=>s-Math.exp(-v*t)*(g+(m+v*g)*t);else{let t=v*Math.sqrt(f*f-1);n=e=>{let r=Math.exp(-f*v*e),i=Math.min(t*e,300);return s-r*((m+f*v*g)*Math.sinh(i)+t*g*Math.cosh(i))/t}}return{calculatedDuration:p&&c||null,next:t=>{let i=n(t);if(p)a.done=t>=c;else{let o=m;0!==t&&(o=f<1?e1(n,t,i):0);let l=Math.abs(o)<=r,u=Math.abs(s-i)<=e;a.done=l&&u}return a.value=a.done?s:i,a}}}function e4({keyframes:t,velocity:e=0,power:r=.8,timeConstant:i=325,bounceDamping:n=10,bounceStiffness:o=500,modifyTarget:s,min:a,max:l,restDelta:u=.5,restSpeed:h}){let c,d;let p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,g=t=>void 0===a?l:void 0===l?a:Math.abs(a-t)<Math.abs(l-t)?a:l,v=r*e,y=p+v,b=void 0===s?y:s(y);b!==y&&(v=b-p);let x=t=>-v*Math.exp(-t/i),w=t=>b+x(t),P=t=>{let e=x(t),r=w(t);m.done=Math.abs(e)<=u,m.value=m.done?b:r},T=t=>{f(m.value)&&(c=t,d=e9({keyframes:[m.value,g(m.value)],velocity:e1(w,t,m.value),damping:n,stiffness:o,restDelta:u,restSpeed:h}))};return T(0),{calculatedDuration:null,next:t=>{let e=!1;return(d||void 0!==c||(e=!0,P(t),T(t)),void 0!==c&&t>c)?d.next(t-c):(e||P(t),m)}}}let e7=t=>{let e=({timestamp:e})=>t(e);return{start:()=>tj.update(e,!0),stop:()=>tD(e),now:()=>tR.isProcessing?tR.timestamp:performance.now()}};function e8(t){let e=0,r=t.next(e);for(;!r.done&&e<2e4;)e+=50,r=t.next(e);return e>=2e4?1/0:e}let rt={decay:e4,inertia:e4,tween:e0,keyframes:e0,spring:e9};function re({autoplay:t=!0,delay:e=0,driver:r=e7,keyframes:i,type:n="keyframes",repeat:o=0,repeatDelay:s=0,repeatType:a="loop",onPlay:l,onStop:u,onComplete:h,onUpdate:c,...d}){let p,m,f,g,v,y=1,b=!1,x=()=>{m=new Promise(t=>{p=t})};x();let w=rt[n]||e0;w!==e0&&"number"!=typeof i[0]&&(g=eQ([0,100],i,{clamp:!1}),i=[0,100]);let P=w({...d,keyframes:i});"mirror"===a&&(v=w({...d,keyframes:[...i].reverse(),velocity:-(d.velocity||0)}));let T="idle",A=null,S=null,k=null;null===P.calculatedDuration&&o&&(P.calculatedDuration=e8(P));let{calculatedDuration:V}=P,E=1/0,C=1/0;null!==V&&(C=(E=V+s)*(o+1)-s);let M=0,j=t=>{if(null===S)return;y>0&&(S=Math.min(S,t)),y<0&&(S=Math.min(t-C/y,S));let r=(M=null!==A?A:Math.round(t-S)*y)-e*(y>=0?1:-1),n=y>=0?r<0:r>C;M=Math.max(r,0),"finished"===T&&null===A&&(M=C);let l=M,u=P;if(o){let t=Math.min(M,C)/E,e=Math.floor(t),r=t%1;!r&&t>=1&&(r=1),1===r&&e--,(e=Math.min(e,o+1))%2&&("reverse"===a?(r=1-r,s&&(r-=s/E)):"mirror"===a&&(u=v)),l=N(0,1,r)*E}let h=n?{done:!1,value:i[0]}:u.next(l);g&&(h.value=g(h.value));let{done:d}=h;n||null===V||(d=y>=0?M>=C:M<=0);let p=null===A&&("finished"===T||"running"===T&&d);return c&&c(h.value),p&&L(),h},D=()=>{f&&f.stop(),f=void 0},R=()=>{T="idle",D(),p(),x(),S=k=null},L=()=>{T="finished",h&&h(),D(),p()},B=()=>{if(b)return;f||(f=r(j));let t=f.now();l&&l(),null!==A?S=t-A:S&&"finished"!==T||(S=t),"finished"===T&&x(),k=S,A=null,T="running",f.start()};t&&B();let F={then:(t,e)=>m.then(t,e),get time(){return ee(M)},set time(newTime){M=newTime=et(newTime),null===A&&f&&0!==y?S=f.now()-newTime/y:A=newTime},get duration(){return ee(null===P.calculatedDuration?e8(P):P.calculatedDuration)},get speed(){return y},set speed(newSpeed){if(newSpeed===y||!f)return;y=newSpeed,F.time=ee(M)},get state(){return T},play:B,pause:()=>{T="paused",A=M},stop:()=>{b=!0,"idle"!==T&&(T="idle",u&&u(),R())},cancel:()=>{null!==k&&j(k),R()},complete:()=>{T="finished"},sample:t=>(S=0,j(t))};return F}let rr=(o=()=>Object.hasOwnProperty.call(Element.prototype,"animate"),()=>(void 0===i&&(i=o()),i)),ri=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),rn=(t,e)=>"spring"===e.type||"backgroundColor"===t||!function t(e){return!!(!e||"string"==typeof e&&eo[e]||ei(e)||Array.isArray(e)&&e.every(t))}(e.ease),ro={type:"spring",stiffness:500,damping:25,restSpeed:10},rs=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),ra={type:"keyframes",duration:.8},rl={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ru=(t,{keyframes:e})=>e.length>2?ra:D.has(t)?t.startsWith("scale")?rs(e[1]):ro:rl,rh=(t,e)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eG.test(e)||"0"===e)&&!e.startsWith("url(")),rc=new Set(["brightness","contrast","saturate","opacity"]);function rd(t){let[e,r]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[i]=r.match(Z)||[];if(!i)return t;let n=r.replace(i,""),o=rc.has(e)?1:0;return i!==r&&(o*=100),e+"("+o+n+")"}let rp=/([a-z-]*)\(.*?\)/g,rm={...eG,getAnimatableNone:t=>{let e=t.match(rp);return e?e.map(rd).join(" "):t}},rf={...tn,color:eC,backgroundColor:eC,outlineColor:eC,fill:eC,stroke:eC,borderColor:eC,borderTopColor:eC,borderRightColor:eC,borderBottomColor:eC,borderLeftColor:eC,filter:rm,WebkitFilter:rm},rg=t=>rf[t];function rv(t,e){let r=rg(t);return r!==rm&&(r=eG),r.getAnimatableNone?r.getAnimatableNone(e):void 0}let ry=t=>/^0[^.\s]+$/.test(t);function rb(t,e){return t[e]||t.default||t}let rx={skipAnimations:!1},rw=(t,e,r,i={})=>n=>{let o=rb(i,t)||{},s=o.delay||i.delay||0,{elapsed:a=0}=i;a-=et(s);let l=function(t,e,r,i){let n,o;let s=rh(e,r);n=Array.isArray(r)?[...r]:[null,r];let a=void 0!==i.from?i.from:t.get(),l=[];for(let t=0;t<n.length;t++){var u;null===n[t]&&(n[t]=0===t?a:n[t-1]),("number"==typeof(u=n[t])?0===u:null!==u?"none"===u||"0"===u||ry(u):void 0)&&l.push(t),"string"==typeof n[t]&&"none"!==n[t]&&"0"!==n[t]&&(o=n[t])}if(s&&l.length&&o)for(let t=0;t<l.length;t++)n[l[t]]=rv(e,o);return n}(e,t,r,o),u=l[0],h=l[l.length-1],c=rh(t,u),d=rh(t,h);tE(c===d,`You are trying to animate ${t} from "${u}" to "${h}". ${u} is not an animatable value - to enable this animation set ${u} to a value animatable to ${h} via the \`style\` property.`);let p={keyframes:l,velocity:e.getVelocity(),ease:"easeOut",...o,delay:-a,onUpdate:t=>{e.set(t),o.onUpdate&&o.onUpdate(t)},onComplete:()=>{n(),o.onComplete&&o.onComplete()}};if(!function({when:t,delay:e,delayChildren:r,staggerChildren:i,staggerDirection:n,repeat:o,repeatType:s,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(o)&&(p={...p,...ru(t,p)}),p.duration&&(p.duration=et(p.duration)),p.repeatDelay&&(p.repeatDelay=et(p.repeatDelay)),!c||!d||er.current||!1===o.type||rx.skipAnimations)return function({keyframes:t,delay:e,onUpdate:r,onComplete:i}){let n=()=>(r&&r(t[t.length-1]),i&&i(),{time:0,speed:1,duration:0,play:tE,pause:tE,stop:tE,then:t=>(t(),Promise.resolve()),cancel:tE,complete:tE});return e?re({keyframes:[0,1],duration:0,delay:e,onComplete:n}):n()}(er.current?{...p,delay:0}:p);if(!i.isHandoff&&e.owner&&e.owner.current instanceof HTMLElement&&!e.owner.getProps().onUpdate){let r=function(t,e,{onUpdate:r,onComplete:i,...n}){let o,s;if(!(rr()&&ri.has(e)&&!n.repeatDelay&&"mirror"!==n.repeatType&&0!==n.damping&&"inertia"!==n.type))return!1;let a=!1,l=!1,u=()=>{s=new Promise(t=>{o=t})};u();let{keyframes:h,duration:c=300,ease:d,times:p}=n;if(rn(e,n)){let t=re({...n,repeat:0,delay:0}),e={done:!1,value:h[0]},r=[],i=0;for(;!e.done&&i<2e4;)e=t.sample(i),r.push(e.value),i+=10;p=void 0,h=r,c=i-10,d="linear"}let m=function(t,e,r,{delay:i=0,duration:n,repeat:o=0,repeatType:s="loop",ease:a,times:l}={}){let u={[e]:r};l&&(u.offset=l);let h=function t(e){if(e)return ei(e)?en(e):Array.isArray(e)?e.map(t):eo[e]}(a);return Array.isArray(h)&&(u.easing=h),t.animate(u,{delay:i,duration:n,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:o+1,direction:"reverse"===s?"alternate":"normal"})}(t.owner.current,e,h,{...n,duration:c,ease:d,times:p}),f=()=>{l=!1,m.cancel()},g=()=>{l=!0,tj.update(f),o(),u()};return m.onfinish=()=>{l||(t.set(function(t,{repeat:e,repeatType:r="loop"}){let i=e&&"loop"!==r&&e%2==1?0:t.length-1;return t[i]}(h,n)),i&&i(),g())},{then:(t,e)=>s.then(t,e),attachTimeline:t=>(m.timeline=t,m.onfinish=null,tE),get time(){return ee(m.currentTime||0)},set time(newTime){m.currentTime=et(newTime)},get speed(){return m.playbackRate},set speed(newSpeed){m.playbackRate=newSpeed},get duration(){return ee(c)},play:()=>{a||(m.play(),tD(f))},pause:()=>m.pause(),stop:()=>{if(a=!0,"idle"===m.playState)return;let{currentTime:e}=m;if(e){let r=re({...n,autoplay:!1});t.setWithVelocity(r.sample(e-10).value,r.sample(e).value,10)}g()},complete:()=>{l||m.finish()},cancel:g}}(e,t,p);if(r)return r}return re(p)};function rP(t){return!!(L(t)&&t.add)}let rT=t=>/^\-?\d*\.?\d+$/.test(t);function rA(t,e){-1===t.indexOf(e)&&t.push(e)}function rS(t,e){let r=t.indexOf(e);r>-1&&t.splice(r,1)}class rk{constructor(){this.subscriptions=[]}add(t){return rA(this.subscriptions,t),()=>rS(this.subscriptions,t)}notify(t,e,r){let i=this.subscriptions.length;if(i){if(1===i)this.subscriptions[0](t,e,r);else for(let n=0;n<i;n++){let i=this.subscriptions[n];i&&i(t,e,r)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let rV=t=>!isNaN(parseFloat(t)),rE={current:void 0};class rC{constructor(t,e={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(t,e=!0)=>{this.prev=this.current,this.current=t;let{delta:r,timestamp:i}=tR;this.lastUpdated!==i&&(this.timeDelta=r,this.lastUpdated=i,tj.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>tj.postRender(this.velocityCheck),this.velocityCheck=({timestamp:t})=>{t!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=rV(this.current),this.owner=e.owner}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new rk);let r=this.events[t].add(e);return"change"===t?()=>{r(),tj.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,r){this.set(e),this.prev=t,this.timeDelta=r}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return rE.current&&rE.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t,e;return this.canTrackVelocity?(t=parseFloat(this.current)-parseFloat(this.prev),(e=this.timeDelta)?1e3/e*t:0):0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function rM(t,e){return new rC(t,e)}let rj=t=>e=>e.test(t),rD=[$,Q,J,K,te,tt,{test:t=>"auto"===t,parse:t=>t}],rR=t=>rD.find(rj(t)),rL=[...rD,eC,eG],rB=t=>rL.find(rj(t));function rF(t,e,{delay:r=0,transitionOverride:i,type:n}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:s,...a}=t.makeTargetAnimatable(e),l=t.getValue("willChange");i&&(o=i);let u=[],h=n&&t.animationState&&t.animationState.getState()[n];for(let e in a){let i=t.getValue(e),n=a[e];if(!i||void 0===n||h&&function({protectedKeys:t,needsAnimating:e},r){let i=t.hasOwnProperty(r)&&!0!==e[r];return e[r]=!1,i}(h,e))continue;let s={delay:r,elapsed:0,...rb(o||{},e)};if(window.HandoffAppearAnimations){let r=t.getProps()[m];if(r){let t=window.HandoffAppearAnimations(r,e,i,tj);null!==t&&(s.elapsed=t,s.isHandoff=!0)}}let c=!s.isHandoff&&!function(t,e){let r=t.get();if(!Array.isArray(e))return r!==e;for(let t=0;t<e.length;t++)if(e[t]!==r)return!0}(i,n);if("spring"===s.type&&(i.getVelocity()||s.velocity)&&(c=!1),i.animation&&(c=!1),c)continue;i.start(rw(e,i,n,t.shouldReduceMotion&&D.has(e)?{type:!1}:s));let d=i.animation;rP(l)&&(l.add(e),d.then(()=>l.remove(e))),u.push(d)}return s&&Promise.all(u).then(()=>{s&&function(t,e){let r=t8(t,e),{transitionEnd:i={},transition:n={},...o}=r?t.makeTargetAnimatable(r,!1):{};for(let e in o={...o,...i}){let r=tS(o[e]);t.hasValue(e)?t.getValue(e).set(r):t.addValue(e,rM(r))}}(t,s)}),u}function rO(t,e,r={}){let i=t8(t,e,r.custom),{transition:n=t.getDefaultTransition()||{}}=i||{};r.transitionOverride&&(n=r.transitionOverride);let o=i?()=>Promise.all(rF(t,i,r)):()=>Promise.resolve(),s=t.variantChildren&&t.variantChildren.size?(i=0)=>{let{delayChildren:o=0,staggerChildren:s,staggerDirection:a}=n;return function(t,e,r=0,i=0,n=1,o){let s=[],a=(t.variantChildren.size-1)*i,l=1===n?(t=0)=>t*i:(t=0)=>a-t*i;return Array.from(t.variantChildren).sort(rI).forEach((t,i)=>{t.notify("AnimationStart",e),s.push(rO(t,e,{...o,delay:r+l(i)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(s)}(t,e,o+i,s,a,r)}:()=>Promise.resolve(),{when:a}=n;if(!a)return Promise.all([o(),s(r.delay)]);{let[t,e]="beforeChildren"===a?[o,s]:[s,o];return t().then(()=>e())}}function rI(t,e){return t.sortNodePosition(e)}let rz=[...y].reverse(),rU=y.length;function rN(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}class r$ extends tq{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:r})=>(function(t,e,r={}){let i;if(t.notify("AnimationStart",e),Array.isArray(e))i=Promise.all(e.map(e=>rO(t,e,r)));else if("string"==typeof e)i=rO(t,e,r);else{let n="function"==typeof e?t8(t,e,r.custom):e;i=Promise.all(rF(t,n,r))}return i.then(()=>t.notify("AnimationComplete",e))})(t,e,r))),r={animate:rN(!0),whileInView:rN(),whileHover:rN(),whileTap:rN(),whileDrag:rN(),whileFocus:rN(),exit:rN()},i=!0,n=(e,r)=>{let i=t8(t,r);if(i){let{transition:t,transitionEnd:r,...n}=i;e={...e,...n,...r}}return e};function o(o,s){let a=t.getProps(),l=t.getVariantContext(!0)||{},u=[],h=new Set,c={},d=1/0;for(let e=0;e<rU;e++){var p;let m=rz[e],f=r[m],y=void 0!==a[m]?a[m]:l[m],b=g(y),x=m===s?f.isActive:null;!1===x&&(d=e);let w=y===l[m]&&y!==a[m]&&b;if(w&&i&&t.manuallyAnimateOnMount&&(w=!1),f.protectedKeys={...c},!f.isActive&&null===x||!y&&!f.prevProp||v(y)||"boolean"==typeof y)continue;let P=(p=f.prevProp,("string"==typeof y?y!==p:!!Array.isArray(y)&&!t7(y,p))||m===s&&f.isActive&&!w&&b||e>d&&b),T=!1,A=Array.isArray(y)?y:[y],S=A.reduce(n,{});!1===x&&(S={});let{prevResolvedValues:k={}}=f,V={...k,...S},E=t=>{P=!0,h.has(t)&&(T=!0,h.delete(t)),f.needsAnimating[t]=!0};for(let t in V){let e=S[t],r=k[t];if(!c.hasOwnProperty(t))(tT(e)&&tT(r)?t7(e,r):e===r)?void 0!==e&&h.has(t)?E(t):f.protectedKeys[t]=!0:void 0!==e?E(t):h.add(t)}f.prevProp=y,f.prevResolvedValues=S,f.isActive&&(c={...c,...S}),i&&t.blockInitialAnimation&&(P=!1),P&&(!w||T)&&u.push(...A.map(t=>({animation:t,options:{type:m,...o}})))}if(h.size){let e={};h.forEach(r=>{let i=t.getBaseTarget(r);void 0!==i&&(e[r]=i)}),u.push({animation:e})}let m=!!u.length;return i&&(!1===a.initial||a.initial===a.animate)&&!t.manuallyAnimateOnMount&&(m=!1),i=!1,m?e(u):Promise.resolve()}return{animateChanges:o,setActive:function(e,i,n){var s;if(r[e].isActive===i)return Promise.resolve();null===(s=t.variantChildren)||void 0===s||s.forEach(t=>{var r;return null===(r=t.animationState)||void 0===r?void 0:r.setActive(e,i)}),r[e].isActive=i;let a=o(n,e);for(let t in r)r[t].protectedKeys={};return a},setAnimateFunction:function(r){e=r(t)},getState:()=>r}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();this.unmount(),v(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){}}let rW=0;class rH extends tq{constructor(){super(...arguments),this.id=rW++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e,custom:r}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t,{custom:null!=r?r:this.node.getProps().custom});e&&!t&&n.then(()=>e(this.id))}mount(){let{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}let rG=(t,e)=>Math.abs(t-e);class rZ{constructor(t,e,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{var t,e;if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=rq(this.lastMoveEventInfo,this.history),i=null!==this.startEvent,n=(t=r.offset,e={x:0,y:0},Math.sqrt(rG(t.x,e.x)**2+rG(t.y,e.y)**2)>=3);if(!i&&!n)return;let{point:o}=r,{timestamp:s}=tR;this.history.push({...o,timestamp:s});let{onStart:a,onMove:l}=this.handlers;i||(a&&a(this.lastMoveEvent,r),this.startEvent=this.lastMoveEvent),l&&l(this.lastMoveEvent,r)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=rY(e,this.transformPagePoint),tj.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:r,onSessionEnd:i,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=rq("pointercancel"===t.type?this.lastMoveEventInfo:rY(e,this.transformPagePoint),this.history);this.startEvent&&r&&r(t,o),i&&i(t,o)},!tI(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=r,this.contextWindow=i||window;let o=rY(tz(t),this.transformPagePoint),{point:s}=o,{timestamp:a}=tR;this.history=[{...s,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,rq(o,this.history)),this.removeListeners=tW(tN(this.contextWindow,"pointermove",this.handlePointerMove),tN(this.contextWindow,"pointerup",this.handlePointerUp),tN(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),tD(this.updatePoint)}}function rY(t,e){return e?{point:e(t.point)}:t}function rX(t,e){return{x:t.x-e.x,y:t.y-e.y}}function rq({point:t},e){return{point:t,delta:rX(t,r_(e)),offset:rX(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let r=t.length-1,i=null,n=r_(t);for(;r>=0&&(i=t[r],!(n.timestamp-i.timestamp>et(.1)));)r--;if(!i)return{x:0,y:0};let o=ee(n.timestamp-i.timestamp);if(0===o)return{x:0,y:0};let s={x:(n.x-i.x)/o,y:(n.y-i.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}(e,0)}}function r_(t){return t[t.length-1]}function rK(t){return t.max-t.min}function rJ(t,e=0,r=.01){return Math.abs(t-e)<=r}function rQ(t,e,r,i=.5){t.origin=i,t.originPoint=eM(e.min,e.max,t.origin),t.scale=rK(r)/rK(e),(rJ(t.scale,1,1e-4)||isNaN(t.scale))&&(t.scale=1),t.translate=eM(r.min,r.max,t.origin)-t.originPoint,(rJ(t.translate)||isNaN(t.translate))&&(t.translate=0)}function r0(t,e,r,i){rQ(t.x,e.x,r.x,i?i.originX:void 0),rQ(t.y,e.y,r.y,i?i.originY:void 0)}function r1(t,e,r){t.min=r.min+e.min,t.max=t.min+rK(e)}function r5(t,e,r){t.min=e.min-r.min,t.max=t.min+rK(e)}function r2(t,e,r){r5(t.x,e.x,r.x),r5(t.y,e.y,r.y)}function r3(t,e,r){return{min:void 0!==e?t.min+e:void 0,max:void 0!==r?t.max+r-(t.max-t.min):void 0}}function r6(t,e){let r=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([r,i]=[i,r]),{min:r,max:i}}function r9(t,e,r){return{min:r4(t,e),max:r4(t,r)}}function r4(t,e){return"number"==typeof t?t:t[e]||0}let r7=()=>({translate:0,scale:1,origin:0,originPoint:0}),r8=()=>({x:r7(),y:r7()}),it=()=>({min:0,max:0}),ie=()=>({x:it(),y:it()});function ir(t){return[t("x"),t("y")]}function ii({top:t,left:e,right:r,bottom:i}){return{x:{min:e,max:r},y:{min:t,max:i}}}function io(t){return void 0===t||1===t}function is({scale:t,scaleX:e,scaleY:r}){return!io(t)||!io(e)||!io(r)}function ia(t){return is(t)||il(t)||t.z||t.rotate||t.rotateX||t.rotateY}function il(t){var e,r;return(e=t.x)&&"0%"!==e||(r=t.y)&&"0%"!==r}function iu(t,e,r,i,n){return void 0!==n&&(t=i+n*(t-i)),i+r*(t-i)+e}function ih(t,e=0,r=1,i,n){t.min=iu(t.min,e,r,i,n),t.max=iu(t.max,e,r,i,n)}function ic(t,{x:e,y:r}){ih(t.x,e.translate,e.scale,e.originPoint),ih(t.y,r.translate,r.scale,r.originPoint)}function id(t){return Number.isInteger(t)?t:t>1.0000000000001||t<.999999999999?t:1}function ip(t,e){t.min=t.min+e,t.max=t.max+e}function im(t,e,[r,i,n]){let o=void 0!==e[n]?e[n]:.5,s=eM(t.min,t.max,o);ih(t,e[r],e[i],s,e.scale)}let ig=["x","scaleX","originX"],iv=["y","scaleY","originY"];function iy(t,e){im(t.x,e,ig),im(t.y,e,iv)}function ib(t,e){return ii(function(t,e){if(!e)return t;let r=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:r.y,left:r.x,bottom:i.y,right:i.x}}(t.getBoundingClientRect(),e))}let ix=({current:t})=>t?t.ownerDocument.defaultView:null,iw=new WeakMap;class iP{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ie(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let{dragSnapToOrigin:i}=this.getProps();this.panSession=new rZ(t,{onSessionStart:t=>{let{dragSnapToOrigin:r}=this.getProps();r?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(tz(t,"page").point)},onStart:(t,e)=>{let{drag:r,dragPropagation:i,onDragStart:n}=this.getProps();if(r&&!i&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=tY(r),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),ir(t=>{let e=this.getAxisMotionValue(t).get()||0;if(J.test(e)){let{projection:r}=this.visualElement;if(r&&r.layout){let i=r.layout.layoutBox[t];if(i){let t=rK(i);e=parseFloat(e)/100*t}}}this.originPoint[t]=e}),n&&tj.update(()=>n(t,e),!1,!0);let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:r,dragDirectionLock:i,onDirectionLock:n,onDrag:o}=this.getProps();if(!r&&!this.openGlobalLock)return;let{offset:s}=e;if(i&&null===this.currentDirection){this.currentDirection=function(t,e=10){let r=null;return Math.abs(t.y)>e?r="y":Math.abs(t.x)>e&&(r="x"),r}(s),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,s),this.updateAxis("y",e.point,s),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>ir(t=>{var e;return"paused"===this.getAnimationState(t)&&(null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:ix(this.visualElement)})}stop(t,e){let r=this.isDragging;if(this.cancel(),!r)return;let{velocity:i}=e;this.startAnimation(i);let{onDragEnd:n}=this.getProps();n&&tj.update(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,r){let{drag:i}=this.getProps();if(!r||!iT(t,i,this.currentDirection))return;let n=this.getAxisMotionValue(t),o=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(o=function(t,{min:e,max:r},i){return void 0!==e&&t<e?t=i?eM(e,t,i.min):Math.max(t,e):void 0!==r&&t>r&&(t=i?eM(r,t,i.max):Math.min(t,r)),t}(o,this.constraints[t],this.elastic[t])),n.set(o)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:r}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(t=this.visualElement.projection)||void 0===t?void 0:t.layout,n=this.constraints;e&&f(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&i?this.constraints=function(t,{top:e,left:r,bottom:i,right:n}){return{x:r3(t.x,r,n),y:r3(t.y,e,i)}}(i.layoutBox,e):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:r9(t,"left","right"),y:r9(t,"top","bottom")}}(r),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&ir(t=>{this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let r={};return void 0!==e.min&&(r.min=e.min-t.min),void 0!==e.max&&(r.max=e.max-t.min),r}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:r}=this.getProps();if(!e||!f(e))return!1;let i=e.current;tE(null!==i,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let o=function(t,e,r){let i=ib(t,r),{scroll:n}=e;return n&&(ip(i.x,n.offset.x),ip(i.y,n.offset.y)),i}(i,n.root,this.visualElement.getTransformPagePoint()),s={x:r6((t=n.layout.layoutBox).x,o.x),y:r6(t.y,o.y)};if(r){let t=r(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(s));this.hasMutatedConstraints=!!t,t&&(s=ii(t))}return s}startAnimation(t){let{drag:e,dragMomentum:r,dragElastic:i,dragTransition:n,dragSnapToOrigin:o,onDragTransitionEnd:s}=this.getProps(),a=this.constraints||{};return Promise.all(ir(s=>{if(!iT(s,e,this.currentDirection))return;let l=a&&a[s]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:r?t[s]:0,bounceStiffness:i?200:1e6,bounceDamping:i?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(s,u)})).then(s)}startAxisValueAnimation(t,e){let r=this.getAxisMotionValue(t);return r.start(rw(t,r,0,e))}stopAnimation(){ir(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){ir(t=>{var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.pause()})}getAnimationState(t){var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.state}getAxisMotionValue(t){let e="_drag"+t.toUpperCase(),r=this.visualElement.getProps();return r[e]||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){ir(e=>{let{drag:r}=this.getProps();if(!iT(e,r,this.currentDirection))return;let{projection:i}=this.visualElement,n=this.getAxisMotionValue(e);if(i&&i.layout){let{min:r,max:o}=i.layout.layoutBox[e];n.set(t[e]-eM(r,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:r}=this.visualElement;if(!f(e)||!r||!this.constraints)return;this.stopAnimation();let i={x:0,y:0};ir(t=>{let e=this.getAxisMotionValue(t);if(e){let r=e.get();i[t]=function(t,e){let r=.5,i=rK(t),n=rK(e);return n>i?r=eK(e.min,e.max-i,t.min):i>n&&(r=eK(t.min,t.max-n,e.min)),N(0,1,r)}({min:r,max:r},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),ir(e=>{if(!iT(e,t,null))return;let r=this.getAxisMotionValue(e),{min:n,max:o}=this.constraints[e];r.set(eM(n,o,i[e]))})}addListeners(){if(!this.visualElement.current)return;iw.set(this.visualElement,this);let t=tN(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:r=!0}=this.getProps();e&&r&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();f(t)&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,i=r.addEventListener("measure",e);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),e();let n=tO(window,"resize",()=>this.scalePositionWithinConstraints()),o=r.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(ir(e=>{let r=this.getAxisMotionValue(e);r&&(this.originPoint[e]+=t[e].translate,r.set(r.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),i(),o&&o()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:n=!1,dragElastic:o=.35,dragMomentum:s=!0}=t;return{...t,drag:e,dragDirectionLock:r,dragPropagation:i,dragConstraints:n,dragElastic:o,dragMomentum:s}}}function iT(t,e,r){return(!0===e||e===t)&&(null===r||r===t)}class iA extends tq{constructor(t){super(t),this.removeGroupControls=tE,this.removeListeners=tE,this.controls=new iP(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||tE}unmount(){this.removeGroupControls(),this.removeListeners()}}let iS=t=>(e,r)=>{t&&tj.update(()=>t(e,r))};class ik extends tq{constructor(){super(...arguments),this.removePointerDownListener=tE}onPointerDown(t){this.session=new rZ(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:ix(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:iS(t),onStart:iS(e),onMove:r,onEnd:(t,e)=>{delete this.session,i&&tj.update(()=>i(t,e))}}}mount(){this.removePointerDownListener=tN(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let iV={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function iE(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let iC={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!Q.test(t))return t;t=parseFloat(t)}let r=iE(t,e.target.x),i=iE(t,e.target.y);return`${r}% ${i}%`}};class iM extends s.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:r,layoutId:i}=this.props,{projection:n}=t;Object.assign(M,iD),n&&(e.group&&e.group.add(n),r&&r.register&&i&&r.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),iV.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:r,drag:i,isPresent:n}=this.props,o=r.projection;return o&&(o.isPresent=n,i||t.layoutDependency!==e||void 0===e?o.willUpdate():this.safeToRemove(),t.isPresent===n||(n?o.promote():o.relegate()||tj.postRender(()=>{let t=o.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function ij(t){let[e,r]=function(){let t=(0,s.useContext)(u);if(null===t)return[!0,null];let{isPresent:e,onExitComplete:r,register:i}=t,n=(0,s.useId)();return(0,s.useEffect)(()=>i(n),[]),!e&&r?[!1,()=>r&&r(n)]:[!0]}(),i=(0,s.useContext)(S);return s.createElement(iM,{...t,layoutGroup:i,switchLayoutGroup:(0,s.useContext)(k),isPresent:e,safeToRemove:r})}let iD={borderRadius:{...iC,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:iC,borderTopRightRadius:iC,borderBottomLeftRadius:iC,borderBottomRightRadius:iC,boxShadow:{correct:(t,{treeScale:e,projectionDelta:r})=>{let i=eG.parse(t);if(i.length>5)return t;let n=eG.createTransformer(t),o="number"!=typeof i[0]?1:0,s=r.x.scale*e.x,a=r.y.scale*e.y;i[0+o]/=s,i[1+o]/=a;let l=eM(s,a,.5);return"number"==typeof i[2+o]&&(i[2+o]/=l),"number"==typeof i[3+o]&&(i[3+o]/=l),n(i)}}},iR=["TopLeft","TopRight","BottomLeft","BottomRight"],iL=iR.length,iB=t=>"string"==typeof t?parseFloat(t):t,iF=t=>"number"==typeof t||Q.test(t);function iO(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let iI=iU(0,.5,ef),iz=iU(.5,.95,tE);function iU(t,e,r){return i=>i<t?0:i>e?1:r(eK(t,e,i))}function iN(t,e){t.min=e.min,t.max=e.max}function i$(t,e){iN(t.x,e.x),iN(t.y,e.y)}function iW(t,e,r,i,n){return t-=e,t=i+1/r*(t-i),void 0!==n&&(t=i+1/n*(t-i)),t}function iH(t,e,[r,i,n],o,s){!function(t,e=0,r=1,i=.5,n,o=t,s=t){if(J.test(e)&&(e=parseFloat(e),e=eM(s.min,s.max,e/100)-s.min),"number"!=typeof e)return;let a=eM(o.min,o.max,i);t===o&&(a-=e),t.min=iW(t.min,e,r,a,n),t.max=iW(t.max,e,r,a,n)}(t,e[r],e[i],e[n],e.scale,o,s)}let iG=["x","scaleX","originX"],iZ=["y","scaleY","originY"];function iY(t,e,r,i){iH(t.x,e,iG,r?r.x:void 0,i?i.x:void 0),iH(t.y,e,iZ,r?r.y:void 0,i?i.y:void 0)}function iX(t){return 0===t.translate&&1===t.scale}function iq(t){return iX(t.x)&&iX(t.y)}function i_(t,e){return Math.round(t.x.min)===Math.round(e.x.min)&&Math.round(t.x.max)===Math.round(e.x.max)&&Math.round(t.y.min)===Math.round(e.y.min)&&Math.round(t.y.max)===Math.round(e.y.max)}function iK(t){return rK(t.x)/rK(t.y)}class iJ{constructor(){this.members=[]}add(t){rA(this.members,t),t.scheduleRender()}remove(t){if(rS(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e;let r=this.members.findIndex(e=>t===e);if(0===r)return!1;for(let t=r;t>=0;t--){let r=this.members[t];if(!1!==r.isPresent){e=r;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,e&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:i}=t.options;!1===i&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:r}=t;e.onExitComplete&&e.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function iQ(t,e,r){let i="",n=t.x.translate/e.x,o=t.y.translate/e.y;if((n||o)&&(i=`translate3d(${n}px, ${o}px, 0) `),(1!==e.x||1!==e.y)&&(i+=`scale(${1/e.x}, ${1/e.y}) `),r){let{rotate:t,rotateX:e,rotateY:n}=r;t&&(i+=`rotate(${t}deg) `),e&&(i+=`rotateX(${e}deg) `),n&&(i+=`rotateY(${n}deg) `)}let s=t.x.scale*e.x,a=t.y.scale*e.y;return(1!==s||1!==a)&&(i+=`scale(${s}, ${a})`),i||"none"}let i0=(t,e)=>t.depth-e.depth;class i1{constructor(){this.children=[],this.isDirty=!1}add(t){rA(this.children,t),this.isDirty=!0}remove(t){rS(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(i0),this.isDirty=!1,this.children.forEach(t)}}let i5=["","X","Y","Z"],i2={visibility:"hidden"},i3=0,i6={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function i9({attachResizeListener:t,defaultParent:e,measureScroll:r,checkIsScrollRoot:i,resetTransform:n}){return class{constructor(t={},r=null==e?void 0:e()){this.id=i3++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,i6.totalNodes=i6.resolvedTargetDeltas=i6.recalculatedProjection=0,this.nodes.forEach(i8),this.nodes.forEach(ns),this.nodes.forEach(na),this.nodes.forEach(nt),window.MotionDebug&&window.MotionDebug.record(i6)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=r?r.root||r:this,this.path=r?[...r.path,r]:[],this.parent=r,this.depth=r?r.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new i1)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new rk),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let r=this.eventHandlers.get(t);r&&r.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,r=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:i,layout:n,visualElement:o}=this.options;if(o&&!o.current&&o.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),r&&(n||i)&&(this.isLayoutDirty=!0),t){let r;let i=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,r&&r(),r=function(t,e){let r=performance.now(),i=({timestamp:e})=>{let n=e-r;n>=250&&(tD(i),t(n-250))};return tj.read(i,!0),()=>tD(i)}(i,0),iV.hasAnimatedSinceResize&&(iV.hasAnimatedSinceResize=!1,this.nodes.forEach(no))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&o&&(i||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeTargetChanged:r,layout:i})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let n=this.options.transition||o.getDefaultTransition()||np,{onLayoutAnimationStart:s,onLayoutAnimationComplete:a}=o.getProps(),l=!this.targetLayout||!i_(this.targetLayout,i)||r,u=!e&&r;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);let e={...rb(n,"layout"),onPlay:s,onComplete:a};(o.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||no(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,tD(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(nl),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:r}=this.options;if(void 0===e&&!r)return;let i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nr);return}this.isUpdating||this.nodes.forEach(ni),this.isUpdating=!1,this.nodes.forEach(nn),this.nodes.forEach(i4),this.nodes.forEach(i7),this.clearAllSnapshots();let t=performance.now();tR.delta=N(0,1e3/60,t-tR.timestamp),tR.timestamp=t,tR.isProcessing=!0,tL.update.process(tR),tL.preRender.process(tR),tL.render.process(tR),tR.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(ne),this.sharedNodes.forEach(nu)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,tj.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){tj.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ie(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&(this.scroll={animationId:this.root.animationId,phase:t,isRoot:i(this.instance),offset:r(this.instance)})}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform,e=this.projectionDelta&&!iq(this.projectionDelta),r=this.getTransformTemplate(),i=r?r(this.latestValues,""):void 0,o=i!==this.prevTransformTemplateValue;t&&(e||ia(this.latestValues)||o)&&(n(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let r=this.measurePageBox(),i=this.removeElementScroll(r);return t&&(i=this.removeTransform(i)),ng((e=i).x),ng(e.y),{animationId:this.root.animationId,measuredBox:r,layoutBox:i,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return ie();let e=t.measureViewportBox(),{scroll:r}=this.root;return r&&(ip(e.x,r.offset.x),ip(e.y,r.offset.y)),e}removeElementScroll(t){let e=ie();i$(e,t);for(let r=0;r<this.path.length;r++){let i=this.path[r],{scroll:n,options:o}=i;if(i!==this.root&&n&&o.layoutScroll){if(n.isRoot){i$(e,t);let{scroll:r}=this.root;r&&(ip(e.x,-r.offset.x),ip(e.y,-r.offset.y))}ip(e.x,n.offset.x),ip(e.y,n.offset.y)}}return e}applyTransform(t,e=!1){let r=ie();i$(r,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];!e&&i.options.layoutScroll&&i.scroll&&i!==i.root&&iy(r,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),ia(i.latestValues)&&iy(r,i.latestValues)}return ia(this.latestValues)&&iy(r,this.latestValues),r}removeTransform(t){let e=ie();i$(e,t);for(let t=0;t<this.path.length;t++){let r=this.path[t];if(!r.instance||!ia(r.latestValues))continue;is(r.latestValues)&&r.updateSnapshot();let i=ie();i$(i,r.measurePageBox()),iY(e,r.latestValues,r.snapshot?r.snapshot.layoutBox:void 0,i)}return ia(this.latestValues)&&iY(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==tR.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e,r,i,n;let o=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=o.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=o.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=o.isSharedProjectionDirty);let s=!!this.resumingFrom||this!==o;if(!(t||s&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=tR.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ie(),this.relativeTargetOrigin=ie(),r2(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),i$(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=ie(),this.targetWithTransforms=ie()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),r=this.target,i=this.relativeTarget,n=this.relativeParent.target,r1(r.x,i.x,n.x),r1(r.y,i.y,n.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):i$(this.target,this.layout.layoutBox),ic(this.target,this.targetDelta)):i$(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ie(),this.relativeTargetOrigin=ie(),r2(this.relativeTargetOrigin,this.target,t.target),i$(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}i6.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||is(this.parent.latestValues)||il(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),r=!!this.resumingFrom||this!==e,i=!0;if((this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty))&&(i=!1),r&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===tR.timestamp&&(i=!1),i)return;let{layout:n,layoutId:o}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||o))return;i$(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,a=this.treeScale.y;!function(t,e,r,i=!1){let n,o;let s=r.length;if(s){e.x=e.y=1;for(let a=0;a<s;a++){o=(n=r[a]).projectionDelta;let s=n.instance;(!s||!s.style||"contents"!==s.style.display)&&(i&&n.options.layoutScroll&&n.scroll&&n!==n.root&&iy(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),o&&(e.x*=o.x.scale,e.y*=o.y.scale,ic(t,o)),i&&ia(n.latestValues)&&iy(t,n.latestValues))}e.x=id(e.x),e.y=id(e.y)}}(this.layoutCorrected,this.treeScale,this.path,r),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox);let{target:l}=e;if(!l){this.projectionTransform&&(this.projectionDelta=r8(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=r8(),this.projectionDeltaWithTransform=r8());let u=this.projectionTransform;r0(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.projectionTransform=iQ(this.projectionDelta,this.treeScale),(this.projectionTransform!==u||this.treeScale.x!==s||this.treeScale.y!==a)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),i6.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(t,e=!1){let r;let i=this.snapshot,n=i?i.latestValues:{},o={...this.latestValues},s=r8();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=ie(),l=(i?i.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,c=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(nd));this.animationProgress=0,this.mixTargetDelta=e=>{let i=e/1e3;if(nh(s.x,t.x,i),nh(s.y,t.y,i),this.setTargetDelta(s),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,p,m;r2(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,nc(p.x,m.x,a.x,i),nc(p.y,m.y,a.y,i),r&&(u=this.relativeTarget,d=r,u.x.min===d.x.min&&u.x.max===d.x.max&&u.y.min===d.y.min&&u.y.max===d.y.max)&&(this.isProjectionDirty=!1),r||(r=ie()),i$(r,this.relativeTarget)}l&&(this.animationValues=o,function(t,e,r,i,n,o){n?(t.opacity=eM(0,void 0!==r.opacity?r.opacity:1,iI(i)),t.opacityExit=eM(void 0!==e.opacity?e.opacity:1,0,iz(i))):o&&(t.opacity=eM(void 0!==e.opacity?e.opacity:1,void 0!==r.opacity?r.opacity:1,i));for(let n=0;n<iL;n++){let o=`border${iR[n]}Radius`,s=iO(e,o),a=iO(r,o);(void 0!==s||void 0!==a)&&(s||(s=0),a||(a=0),0===s||0===a||iF(s)===iF(a)?(t[o]=Math.max(eM(iB(s),iB(a),i),0),(J.test(a)||J.test(s))&&(t[o]+="%")):t[o]=a)}(e.rotate||r.rotate)&&(t.rotate=eM(e.rotate||0,r.rotate||0,i))}(o,n,this.latestValues,i,c,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=i},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(tD(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=tj.update(()=>{iV.hasAnimatedSinceResize=!0,this.currentAnimation=function(t,e,r){let i=L(0)?0:rM(0);return i.start(rw("",i,1e3,r)),i.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:r,layout:i,latestValues:n}=t;if(e&&r&&i){if(this!==t&&this.layout&&i&&nv(this.options.animationType,this.layout.layoutBox,i.layoutBox)){r=this.target||ie();let e=rK(this.layout.layoutBox.x);r.x.min=t.target.x.min,r.x.max=r.x.min+e;let i=rK(this.layout.layoutBox.y);r.y.min=t.target.y.min,r.y.max=r.y.min+i}i$(e,r),iy(e,n),r0(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new iJ),this.sharedNodes.get(t).add(e);let r=e.options.initialPromotionConfig;e.promote({transition:r?r.transition:void 0,preserveFollowOpacity:r&&r.shouldPreserveFollowOpacity?r.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null===(t=this.getStack())||void 0===t?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null===(t=this.getStack())||void 0===t?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:r}={}){let i=this.getStack();i&&i.promote(this,r),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:r}=t;if((r.rotate||r.rotateX||r.rotateY||r.rotateZ)&&(e=!0),!e)return;let i={};for(let e=0;e<i5.length;e++){let n="rotate"+i5[e];r[n]&&(i[n]=r[n],t.setStaticValue(n,0))}for(let e in t.render(),i)t.setStaticValue(e,i[e]);t.scheduleRender()}getProjectionStyles(t){var e,r;if(!this.instance||this.isSVG)return;if(!this.isVisible)return i2;let i={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,i.opacity="",i.pointerEvents=tk(null==t?void 0:t.pointerEvents)||"",i.transform=n?n(this.latestValues,""):"none",i;let o=this.getLead();if(!this.projectionDelta||!this.layout||!o.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=tk(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!ia(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1),e}let s=o.animationValues||o.latestValues;this.applyTransformsToTarget(),i.transform=iQ(this.projectionDeltaWithTransform,this.treeScale,s),n&&(i.transform=n(s,i.transform));let{x:a,y:l}=this.projectionDelta;for(let t in i.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,o.animationValues?i.opacity=o===this?null!==(r=null!==(e=s.opacity)&&void 0!==e?e:this.latestValues.opacity)&&void 0!==r?r:1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:i.opacity=o===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0,M){if(void 0===s[t])continue;let{correct:e,applyTo:r}=M[t],n="none"===i.transform?s[t]:e(s[t],o);if(r){let t=r.length;for(let e=0;e<t;e++)i[r[e]]=n}else i[t]=n}return this.options.layoutId&&(i.pointerEvents=o===this?tk(null==t?void 0:t.pointerEvents)||"":"none"),i}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null===(e=t.currentAnimation)||void 0===e?void 0:e.stop()}),this.root.nodes.forEach(nr),this.root.sharedNodes.clear()}}}function i4(t){t.updateLayout()}function i7(t){var e;let r=(null===(e=t.resumeFrom)||void 0===e?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&r&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:i}=t.layout,{animationType:n}=t.options,o=r.source!==t.layout.source;"size"===n?ir(t=>{let i=o?r.measuredBox[t]:r.layoutBox[t],n=rK(i);i.min=e[t].min,i.max=i.min+n}):nv(n,r.layoutBox,e)&&ir(i=>{let n=o?r.measuredBox[i]:r.layoutBox[i],s=rK(e[i]);n.max=n.min+s,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[i].max=t.relativeTarget[i].min+s)});let s=r8();r0(s,e,r.layoutBox);let a=r8();o?r0(a,t.applyTransform(i,!0),r.measuredBox):r0(a,e,r.layoutBox);let l=!iq(s),u=!1;if(!t.resumeFrom){let i=t.getClosestProjectingParent();if(i&&!i.resumeFrom){let{snapshot:n,layout:o}=i;if(n&&o){let s=ie();r2(s,r.layoutBox,n.layoutBox);let a=ie();r2(a,e,o.layoutBox),i_(s,a)||(u=!0),i.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=s,t.relativeParent=i)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:r,delta:a,layoutDelta:s,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function i8(t){i6.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function nt(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function ne(t){t.clearSnapshot()}function nr(t){t.clearMeasurements()}function ni(t){t.isLayoutDirty=!1}function nn(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function no(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function ns(t){t.resolveTargetDelta()}function na(t){t.calcProjection()}function nl(t){t.resetRotation()}function nu(t){t.removeLeadSnapshot()}function nh(t,e,r){t.translate=eM(e.translate,0,r),t.scale=eM(e.scale,1,r),t.origin=e.origin,t.originPoint=e.originPoint}function nc(t,e,r,i){t.min=eM(e.min,r.min,i),t.max=eM(e.max,r.max,i)}function nd(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let np={duration:.45,ease:[.4,0,.1,1]},nm=t=>"undefined"!=typeof navigator&&navigator.userAgent.toLowerCase().includes(t),nf=nm("applewebkit/")&&!nm("chrome/")?Math.round:tE;function ng(t){t.min=nf(t.min),t.max=nf(t.max)}function nv(t,e,r){return"position"===t||"preserve-aspect"===t&&!rJ(iK(e),iK(r),.2)}let ny=i9({attachResizeListener:(t,e)=>tO(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),nb={current:void 0},nx=i9({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!nb.current){let t=new ny({});t.mount(window),t.setOptions({layoutScroll:!0}),nb.current=t}return nb.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position}),nw=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function nP(t,e,r=1){tE(r<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[i,n]=function(t){let e=nw.exec(t);if(!e)return[,];let[,r,i]=e;return[r,i]}(t);if(!i)return;let o=window.getComputedStyle(e).getPropertyValue(i);if(o){let t=o.trim();return rT(t)?parseFloat(t):t}return z(n)?nP(n,e,r+1):n}let nT=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),nA=t=>nT.has(t),nS=t=>Object.keys(t).some(nA),nk=t=>t===$||t===Q,nV=(t,e)=>parseFloat(t.split(", ")[e]),nE=(t,e)=>(r,{transform:i})=>{if("none"===i||!i)return 0;let n=i.match(/^matrix3d\((.+)\)$/);if(n)return nV(n[1],e);{let e=i.match(/^matrix\((.+)\)$/);return e?nV(e[1],t):0}},nC=new Set(["x","y","z"]),nM=j.filter(t=>!nC.has(t)),nj={width:({x:t},{paddingLeft:e="0",paddingRight:r="0"})=>t.max-t.min-parseFloat(e)-parseFloat(r),height:({y:t},{paddingTop:e="0",paddingBottom:r="0"})=>t.max-t.min-parseFloat(e)-parseFloat(r),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:nE(4,13),y:nE(5,14)};nj.translateX=nj.x,nj.translateY=nj.y;let nD=(t,e,r)=>{let i=e.measureViewportBox(),n=getComputedStyle(e.current),{display:o}=n,s={};"none"===o&&e.setStaticValue("display",t.display||"block"),r.forEach(t=>{s[t]=nj[t](i,n)}),e.render();let a=e.measureViewportBox();return r.forEach(r=>{let i=e.getValue(r);i&&i.jump(s[r]),t[r]=nj[r](a,n)}),t},nR=(t,e,r={},i={})=>{e={...e},i={...i};let n=Object.keys(e).filter(nA),o=[],s=!1,a=[];if(n.forEach(n=>{let l;let u=t.getValue(n);if(!t.hasValue(n))return;let h=r[n],c=rR(h),d=e[n];if(tT(d)){let t=d.length,e=null===d[0]?1:0;c=rR(h=d[e]);for(let r=e;r<t&&null!==d[r];r++)l?tE(rR(d[r])===l,"All keyframes must be of the same type"):tE((l=rR(d[r]))===c||nk(c)&&nk(l),"Keyframes must be of the same dimension as the current value")}else l=rR(d);if(c!==l){if(nk(c)&&nk(l)){let t=u.get();"string"==typeof t&&u.set(parseFloat(t)),"string"==typeof d?e[n]=parseFloat(d):Array.isArray(d)&&l===Q&&(e[n]=d.map(parseFloat))}else(null==c?void 0:c.transform)&&(null==l?void 0:l.transform)&&(0===h||0===d)?0===h?u.set(l.transform(h)):e[n]=c.transform(d):(s||(o=function(t){let e=[];return nM.forEach(r=>{let i=t.getValue(r);void 0!==i&&(e.push([r,i.get()]),i.set(r.startsWith("scale")?1:0))}),e.length&&t.render(),e}(t),s=!0),a.push(n),i[n]=void 0!==i[n]?i[n]:e[n],u.jump(d))}}),!a.length)return{target:e,transitionEnd:i};{let r=a.indexOf("height")>=0?window.pageYOffset:null,n=nD(e,t,a);return o.length&&o.forEach(([e,r])=>{t.getValue(e).set(r)}),t.render(),h&&null!==r&&window.scrollTo({top:r}),{target:n,transitionEnd:i}}},nL=(t,e,r,i)=>{var n,o;let s=function(t,{...e},r){let i=t.current;if(!(i instanceof Element))return{target:e,transitionEnd:r};for(let n in r&&(r={...r}),t.values.forEach(t=>{let e=t.get();if(!z(e))return;let r=nP(e,i);r&&t.set(r)}),e){let t=e[n];if(!z(t))continue;let o=nP(t,i);o&&(e[n]=o,r||(r={}),void 0===r[n]&&(r[n]=t))}return{target:e,transitionEnd:r}}(t,e,i);return e=s.target,i=s.transitionEnd,n=e,o=i,nS(n)?nR(t,n,r,o):{target:n,transitionEnd:o}},nB={current:null},nF={current:!1},nO=new WeakMap,nI=Object.keys(A),nz=nI.length,nU=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],nN=b.length;class n${constructor({parent:t,props:e,presenceContext:r,reducedMotionConfig:i,visualState:n},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>tj.render(this.render,!1,!0);let{latestValues:s,renderState:a}=n;this.latestValues=s,this.baseTarget={...s},this.initialValues=e.initial?{...s}:{},this.renderState=a,this.parent=t,this.props=e,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=o,this.isControllingVariants=x(e),this.isVariantNode=w(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:l,...u}=this.scrapeMotionValuesFromProps(e,{});for(let t in u){let e=u[t];void 0!==s[t]&&L(e)&&(e.set(s[t],!1),rP(l)&&l.add(t))}}scrapeMotionValuesFromProps(t,e){return{}}mount(t){this.current=t,nO.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),nF.current||function(){if(nF.current=!0,h){if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>nB.current=t.matches;t.addListener(e),e()}else nB.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||nB.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in nO.delete(this.current),this.projection&&this.projection.unmount(),tD(this.notifyUpdate),tD(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,e){let r=D.has(t),i=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&tj.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),n=e.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{i(),n()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}loadFeatures({children:t,...e},r,i,n){let o,s;for(let t=0;t<nz;t++){let r=nI[t],{isEnabled:i,Feature:n,ProjectionNode:a,MeasureLayout:l}=A[r];a&&(o=a),i(e)&&(!this.features[r]&&n&&(this.features[r]=new n(this)),l&&(s=l))}if(("html"===this.type||"svg"===this.type)&&!this.projection&&o){this.projection=new o(this.latestValues,this.parent&&this.parent.projection);let{layoutId:t,layout:r,drag:i,dragConstraints:s,layoutScroll:a,layoutRoot:l}=e;this.projection.setOptions({layoutId:t,layout:r,alwaysMeasureLayout:!!i||s&&f(s),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:"string"==typeof r?r:"both",initialPromotionConfig:n,layoutScroll:a,layoutRoot:l})}return s}updateFeatures(){for(let t in this.features){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ie()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}makeTargetAnimatable(t,e=!0){return this.makeTargetAnimatableFromInstance(t,this.props,e)}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<nU.length;e++){let r=nU[e];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);let i=t["on"+r];i&&(this.propEventSubscriptions[r]=this.on(r,i))}this.prevMotionValues=function(t,e,r){let{willChange:i}=e;for(let n in e){let o=e[n],s=r[n];if(L(o))t.addValue(n,o),rP(i)&&i.add(n);else if(L(s))t.addValue(n,rM(o,{owner:t})),rP(i)&&i.remove(n);else if(s!==o){if(t.hasValue(n)){let e=t.getValue(n);e.hasAnimated||e.set(o)}else{let e=t.getStaticValue(n);t.addValue(n,rM(void 0!==e?e:o,{owner:t}))}}}for(let i in r)void 0===e[i]&&t.removeValue(i);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){let t=this.parent&&this.parent.getVariantContext()||{};return void 0!==this.props.initial&&(t.initial=this.props.initial),t}let e={};for(let t=0;t<nN;t++){let r=b[t],i=this.props[r];(g(i)||!1===i)&&(e[r]=i)}return e}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){e!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,e)),this.values.set(t,e),this.latestValues[t]=e.get()}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return void 0===r&&void 0!==e&&(r=rM(e,{owner:this}),this.addValue(t,r)),r}readValue(t){var e;return void 0===this.latestValues[t]&&this.current?null!==(e=this.getBaseTargetFromProps(this.props,t))&&void 0!==e?e:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t]}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;let{initial:r}=this.props,i="string"==typeof r||"object"==typeof r?null===(e=tP(this.props,r))||void 0===e?void 0:e[t]:void 0;if(r&&void 0!==i)return i;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||L(n)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new rk),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class nW extends n${sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:r}){delete e[t],delete r[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:e,...r},{transformValues:i},n){let o=function(t,e,r){let i={};for(let n in t){let t=function(t,e){if(e)return(e[t]||e.default||e).from}(n,e);if(void 0!==t)i[n]=t;else{let t=r.getValue(n);t&&(i[n]=t.get())}}return i}(r,t||{},this);if(i&&(e&&(e=i(e)),r&&(r=i(r)),o&&(o=i(o))),n){!function(t,e,r){var i,n;let o=Object.keys(e).filter(e=>!t.hasValue(e)),s=o.length;if(s)for(let a=0;a<s;a++){let s=o[a],l=e[s],u=null;Array.isArray(l)&&(u=l[0]),null===u&&(u=null!==(n=null!==(i=r[s])&&void 0!==i?i:t.readValue(s))&&void 0!==n?n:e[s]),null!=u&&("string"==typeof u&&(rT(u)||ry(u))?u=parseFloat(u):!rB(u)&&eG.test(l)&&(u=rv(s,l)),t.addValue(s,rM(u,{owner:t})),void 0===r[s]&&(r[s]=u),null!==u&&t.setBaseTarget(s,u))}}(this,r,o);let t=nL(this,r,o,e);e=t.transitionEnd,r=t.target}return{transition:t,transitionEnd:e,...r}}}class nH extends nW{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,e){if(D.has(e)){let t=rg(e);return t&&t.default||0}{let r=window.getComputedStyle(t),i=(I(e)?r.getPropertyValue(e):r[e])||0;return"string"==typeof i?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:e}){return ib(t,e)}build(t,e,r,i){to(t,e,r,i.transformTemplate)}scrapeMotionValuesFromProps(t,e){return tx(t,e)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;L(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}renderInstance(t,e,r,i){tv(t,e,r,i)}}class nG extends nW{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(D.has(e)){let t=rg(e);return t&&t.default||0}return e=ty.has(e)?e:p(e),t.getAttribute(e)}measureInstanceViewportBox(){return ie()}scrapeMotionValuesFromProps(t,e){return tw(t,e)}build(t,e,r,i){tm(t,e,r,this.isSVGTag,i.transformTemplate)}renderInstance(t,e,r,i){tb(t,e,r,i)}mount(t){this.isSVGTag=tg(t.tagName),super.mount(t)}}let nZ=(t,e)=>C(t)?new nG(e,{enableHardwareAcceleration:!1}):new nH(e,{enableHardwareAcceleration:!0}),nY={animation:{Feature:r$},exit:{Feature:rH},inView:{Feature:t4},tap:{Feature:t1},focus:{Feature:tJ},hover:{Feature:tK},pan:{Feature:ik},drag:{Feature:iA,ProjectionNode:nx,MeasureLayout:ij},layout:{ProjectionNode:nx,MeasureLayout:ij}},nX=function(t){function e(e,r={}){return function({preloadedFeatures:t,createVisualElement:e,useRender:r,useVisualState:i,Component:n}){t&&function(t){for(let e in t)A[e]={...A[e],...t[e]}}(t);let o=(0,s.forwardRef)(function(o,p){var v;let y;let b={...(0,s.useContext)(a),...o,layoutId:function({layoutId:t}){let e=(0,s.useContext)(S).id;return e&&void 0!==t?e+"-"+t:t}(o)},{isStatic:w}=b,T=function(t){let{initial:e,animate:r}=function(t,e){if(x(t)){let{initial:e,animate:r}=t;return{initial:!1===e||g(e)?e:void 0,animate:g(r)?r:void 0}}return!1!==t.inherit?e:{}}(t,(0,s.useContext)(l));return(0,s.useMemo)(()=>({initial:e,animate:r}),[P(e),P(r)])}(o),A=i(o,w);if(!w&&h){T.visualElement=function(t,e,r,i){let{visualElement:n}=(0,s.useContext)(l),o=(0,s.useContext)(d),h=(0,s.useContext)(u),p=(0,s.useContext)(a).reducedMotion,f=(0,s.useRef)();i=i||o.renderer,!f.current&&i&&(f.current=i(t,{visualState:e,parent:n,props:r,presenceContext:h,blockInitialAnimation:!!h&&!1===h.initial,reducedMotionConfig:p}));let g=f.current;(0,s.useInsertionEffect)(()=>{g&&g.update(r,h)});let v=(0,s.useRef)(!!(r[m]&&!window.HandoffComplete));return c(()=>{g&&(g.render(),v.current&&g.animationState&&g.animationState.animateChanges())}),(0,s.useEffect)(()=>{g&&(g.updateFeatures(),!v.current&&g.animationState&&g.animationState.animateChanges(),v.current&&(v.current=!1,window.HandoffComplete=!0))}),g}(n,A,b,e);let r=(0,s.useContext)(k),i=(0,s.useContext)(d).strict;T.visualElement&&(y=T.visualElement.loadFeatures(b,i,t,r))}return s.createElement(l.Provider,{value:T},y&&T.visualElement?s.createElement(y,{visualElement:T.visualElement,...b}):null,r(n,o,(v=T.visualElement,(0,s.useCallback)(t=>{t&&A.mount&&A.mount(t),v&&(t?v.mount(t):v.unmount()),p&&("function"==typeof p?p(t):f(p)&&(p.current=t))},[v])),A,w,T.visualElement))});return o[V]=n,o}(t(e,r))}if("undefined"==typeof Proxy)return e;let r=new Map;return new Proxy(e,{get:(t,i)=>(r.has(i)||r.set(i,e(i)),r.get(i))})}((t,e)=>(function(t,{forwardMotionProps:e=!1},r,i){return{...C(t)?tB:tF,preloadedFeatures:r,useRender:function(t=!1){return(e,r,i,{latestValues:n},o)=>{let a=(C(e)?function(t,e,r,i){let n=(0,s.useMemo)(()=>{let r=tf();return tm(r,e,{enableHardwareAcceleration:!1},tg(i),t.transformTemplate),{...r.attrs,style:{...r.style}}},[e]);if(t.style){let e={};ta(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e,r){let i={},n=function(t,e,r){let i=t.style||{},n={};return ta(n,i,t),Object.assign(n,function({transformTemplate:t},e,r){return(0,s.useMemo)(()=>{let i=ts();return to(i,e,{enableHardwareAcceleration:!r},t),Object.assign({},i.vars,i.style)},[e])}(t,e,r)),t.transformValues?t.transformValues(n):n}(t,e,r);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(r,n,o,e),l={...function(t,e,r){let i={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(th(n)||!0===r&&tu(n)||!e&&!tu(n)||t.draggable&&n.startsWith("onDrag"))&&(i[n]=t[n]);return i}(r,"string"==typeof e,t),...a,ref:i},{children:u}=r,h=(0,s.useMemo)(()=>L(u)?u.get():u,[u]);return(0,s.createElement)(e,{...l,children:h})}}(e),createVisualElement:i,Component:t}})(t,e,nY,nZ))},53335:function(t,e,r){r.d(e,{m6:function(){return q}});let i=t=>{let e=a(t),{conflictingClassGroups:r,conflictingClassGroupModifiers:i}=t;return{getClassGroupId:t=>{let r=t.split("-");return""===r[0]&&1!==r.length&&r.shift(),n(r,e)||s(t)},getConflictingClassGroupIds:(t,e)=>{let n=r[t]||[];return e&&i[t]?[...n,...i[t]]:n}}},n=(t,e)=>{if(0===t.length)return e.classGroupId;let r=t[0],i=e.nextPart.get(r),o=i?n(t.slice(1),i):void 0;if(o)return o;if(0===e.validators.length)return;let s=t.join("-");return e.validators.find(({validator:t})=>t(s))?.classGroupId},o=/^\[(.+)\]$/,s=t=>{if(o.test(t)){let e=o.exec(t)[1],r=e?.substring(0,e.indexOf(":"));if(r)return"arbitrary.."+r}},a=t=>{let{theme:e,prefix:r}=t,i={nextPart:new Map,validators:[]};return c(Object.entries(t.classGroups),r).forEach(([t,r])=>{l(r,i,t,e)}),i},l=(t,e,r,i)=>{t.forEach(t=>{if("string"==typeof t){(""===t?e:u(e,t)).classGroupId=r;return}if("function"==typeof t){if(h(t)){l(t(i),e,r,i);return}e.validators.push({validator:t,classGroupId:r});return}Object.entries(t).forEach(([t,n])=>{l(n,u(e,t),r,i)})})},u=(t,e)=>{let r=t;return e.split("-").forEach(t=>{r.nextPart.has(t)||r.nextPart.set(t,{nextPart:new Map,validators:[]}),r=r.nextPart.get(t)}),r},h=t=>t.isThemeGetter,c=(t,e)=>e?t.map(([t,r])=>[t,r.map(t=>"string"==typeof t?e+t:"object"==typeof t?Object.fromEntries(Object.entries(t).map(([t,r])=>[e+t,r])):t)]):t,d=t=>{if(t<1)return{get:()=>void 0,set:()=>{}};let e=0,r=new Map,i=new Map,n=(n,o)=>{r.set(n,o),++e>t&&(e=0,i=r,r=new Map)};return{get(t){let e=r.get(t);return void 0!==e?e:void 0!==(e=i.get(t))?(n(t,e),e):void 0},set(t,e){r.has(t)?r.set(t,e):n(t,e)}}},p=t=>{let{separator:e,experimentalParseClassName:r}=t,i=1===e.length,n=e[0],o=e.length,s=t=>{let r;let s=[],a=0,l=0;for(let u=0;u<t.length;u++){let h=t[u];if(0===a){if(h===n&&(i||t.slice(u,u+o)===e)){s.push(t.slice(l,u)),l=u+o;continue}if("/"===h){r=u;continue}}"["===h?a++:"]"===h&&a--}let u=0===s.length?t:t.substring(l),h=u.startsWith("!"),c=h?u.substring(1):u;return{modifiers:s,hasImportantModifier:h,baseClassName:c,maybePostfixModifierPosition:r&&r>l?r-l:void 0}};return r?t=>r({className:t,parseClassName:s}):s},m=t=>{if(t.length<=1)return t;let e=[],r=[];return t.forEach(t=>{"["===t[0]?(e.push(...r.sort(),t),r=[]):r.push(t)}),e.push(...r.sort()),e},f=t=>({cache:d(t.cacheSize),parseClassName:p(t),...i(t)}),g=/\s+/,v=(t,e)=>{let{parseClassName:r,getClassGroupId:i,getConflictingClassGroupIds:n}=e,o=[],s=t.trim().split(g),a="";for(let t=s.length-1;t>=0;t-=1){let e=s[t],{modifiers:l,hasImportantModifier:u,baseClassName:h,maybePostfixModifierPosition:c}=r(e),d=!!c,p=i(d?h.substring(0,c):h);if(!p){if(!d||!(p=i(h))){a=e+(a.length>0?" "+a:a);continue}d=!1}let f=m(l).join(":"),g=u?f+"!":f,v=g+p;if(o.includes(v))continue;o.push(v);let y=n(p,d);for(let t=0;t<y.length;++t){let e=y[t];o.push(g+e)}a=e+(a.length>0?" "+a:a)}return a};function y(){let t,e,r=0,i="";for(;r<arguments.length;)(t=arguments[r++])&&(e=b(t))&&(i&&(i+=" "),i+=e);return i}let b=t=>{let e;if("string"==typeof t)return t;let r="";for(let i=0;i<t.length;i++)t[i]&&(e=b(t[i]))&&(r&&(r+=" "),r+=e);return r},x=t=>{let e=e=>e[t]||[];return e.isThemeGetter=!0,e},w=/^\[(?:([a-z-]+):)?(.+)\]$/i,P=/^\d+\/\d+$/,T=new Set(["px","full","screen"]),A=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,S=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,k=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,V=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,E=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,C=t=>j(t)||T.has(t)||P.test(t),M=t=>H(t,"length",G),j=t=>!!t&&!Number.isNaN(Number(t)),D=t=>H(t,"number",j),R=t=>!!t&&Number.isInteger(Number(t)),L=t=>t.endsWith("%")&&j(t.slice(0,-1)),B=t=>w.test(t),F=t=>A.test(t),O=new Set(["length","size","percentage"]),I=t=>H(t,O,Z),z=t=>H(t,"position",Z),U=new Set(["image","url"]),N=t=>H(t,U,X),$=t=>H(t,"",Y),W=()=>!0,H=(t,e,r)=>{let i=w.exec(t);return!!i&&(i[1]?"string"==typeof e?i[1]===e:e.has(i[1]):r(i[2]))},G=t=>S.test(t)&&!k.test(t),Z=()=>!1,Y=t=>V.test(t),X=t=>E.test(t),q=function(t,...e){let r,i,n;let o=function(a){return i=(r=f(e.reduce((t,e)=>e(t),t()))).cache.get,n=r.cache.set,o=s,s(a)};function s(t){let e=i(t);if(e)return e;let o=v(t,r);return n(t,o),o}return function(){return o(y.apply(null,arguments))}}(()=>{let t=x("colors"),e=x("spacing"),r=x("blur"),i=x("brightness"),n=x("borderColor"),o=x("borderRadius"),s=x("borderSpacing"),a=x("borderWidth"),l=x("contrast"),u=x("grayscale"),h=x("hueRotate"),c=x("invert"),d=x("gap"),p=x("gradientColorStops"),m=x("gradientColorStopPositions"),f=x("inset"),g=x("margin"),v=x("opacity"),y=x("padding"),b=x("saturate"),w=x("scale"),P=x("sepia"),T=x("skew"),A=x("space"),S=x("translate"),k=()=>["auto","contain","none"],V=()=>["auto","hidden","clip","visible","scroll"],E=()=>["auto",B,e],O=()=>[B,e],U=()=>["",C,M],H=()=>["auto",j,B],G=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],Z=()=>["solid","dashed","dotted","double","none"],Y=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],X=()=>["start","end","center","between","around","evenly","stretch"],q=()=>["","0",B],_=()=>["auto","avoid","all","avoid-page","page","left","right","column"],K=()=>[j,B];return{cacheSize:500,separator:":",theme:{colors:[W],spacing:[C,M],blur:["none","",F,B],brightness:K(),borderColor:[t],borderRadius:["none","","full",F,B],borderSpacing:O(),borderWidth:U(),contrast:K(),grayscale:q(),hueRotate:K(),invert:q(),gap:O(),gradientColorStops:[t],gradientColorStopPositions:[L,M],inset:E(),margin:E(),opacity:K(),padding:O(),saturate:K(),scale:K(),sepia:q(),skew:K(),space:O(),translate:O()},classGroups:{aspect:[{aspect:["auto","square","video",B]}],container:["container"],columns:[{columns:[F]}],"break-after":[{"break-after":_()}],"break-before":[{"break-before":_()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...G(),B]}],overflow:[{overflow:V()}],"overflow-x":[{"overflow-x":V()}],"overflow-y":[{"overflow-y":V()}],overscroll:[{overscroll:k()}],"overscroll-x":[{"overscroll-x":k()}],"overscroll-y":[{"overscroll-y":k()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[f]}],"inset-x":[{"inset-x":[f]}],"inset-y":[{"inset-y":[f]}],start:[{start:[f]}],end:[{end:[f]}],top:[{top:[f]}],right:[{right:[f]}],bottom:[{bottom:[f]}],left:[{left:[f]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",R,B]}],basis:[{basis:E()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",B]}],grow:[{grow:q()}],shrink:[{shrink:q()}],order:[{order:["first","last","none",R,B]}],"grid-cols":[{"grid-cols":[W]}],"col-start-end":[{col:["auto",{span:["full",R,B]},B]}],"col-start":[{"col-start":H()}],"col-end":[{"col-end":H()}],"grid-rows":[{"grid-rows":[W]}],"row-start-end":[{row:["auto",{span:[R,B]},B]}],"row-start":[{"row-start":H()}],"row-end":[{"row-end":H()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",B]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",B]}],gap:[{gap:[d]}],"gap-x":[{"gap-x":[d]}],"gap-y":[{"gap-y":[d]}],"justify-content":[{justify:["normal",...X()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...X(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...X(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[y]}],px:[{px:[y]}],py:[{py:[y]}],ps:[{ps:[y]}],pe:[{pe:[y]}],pt:[{pt:[y]}],pr:[{pr:[y]}],pb:[{pb:[y]}],pl:[{pl:[y]}],m:[{m:[g]}],mx:[{mx:[g]}],my:[{my:[g]}],ms:[{ms:[g]}],me:[{me:[g]}],mt:[{mt:[g]}],mr:[{mr:[g]}],mb:[{mb:[g]}],ml:[{ml:[g]}],"space-x":[{"space-x":[A]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[A]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",B,e]}],"min-w":[{"min-w":[B,e,"min","max","fit"]}],"max-w":[{"max-w":[B,e,"none","full","min","max","fit","prose",{screen:[F]},F]}],h:[{h:[B,e,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[B,e,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[B,e,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[B,e,"auto","min","max","fit"]}],"font-size":[{text:["base",F,M]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",D]}],"font-family":[{font:[W]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",B]}],"line-clamp":[{"line-clamp":["none",j,D]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",C,B]}],"list-image":[{"list-image":["none",B]}],"list-style-type":[{list:["none","disc","decimal",B]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[t]}],"placeholder-opacity":[{"placeholder-opacity":[v]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[t]}],"text-opacity":[{"text-opacity":[v]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...Z(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",C,M]}],"underline-offset":[{"underline-offset":["auto",C,B]}],"text-decoration-color":[{decoration:[t]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:O()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",B]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",B]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[v]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...G(),z]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",I]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},N]}],"bg-color":[{bg:[t]}],"gradient-from-pos":[{from:[m]}],"gradient-via-pos":[{via:[m]}],"gradient-to-pos":[{to:[m]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[o]}],"rounded-s":[{"rounded-s":[o]}],"rounded-e":[{"rounded-e":[o]}],"rounded-t":[{"rounded-t":[o]}],"rounded-r":[{"rounded-r":[o]}],"rounded-b":[{"rounded-b":[o]}],"rounded-l":[{"rounded-l":[o]}],"rounded-ss":[{"rounded-ss":[o]}],"rounded-se":[{"rounded-se":[o]}],"rounded-ee":[{"rounded-ee":[o]}],"rounded-es":[{"rounded-es":[o]}],"rounded-tl":[{"rounded-tl":[o]}],"rounded-tr":[{"rounded-tr":[o]}],"rounded-br":[{"rounded-br":[o]}],"rounded-bl":[{"rounded-bl":[o]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[v]}],"border-style":[{border:[...Z(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[v]}],"divide-style":[{divide:Z()}],"border-color":[{border:[n]}],"border-color-x":[{"border-x":[n]}],"border-color-y":[{"border-y":[n]}],"border-color-s":[{"border-s":[n]}],"border-color-e":[{"border-e":[n]}],"border-color-t":[{"border-t":[n]}],"border-color-r":[{"border-r":[n]}],"border-color-b":[{"border-b":[n]}],"border-color-l":[{"border-l":[n]}],"divide-color":[{divide:[n]}],"outline-style":[{outline:["",...Z()]}],"outline-offset":[{"outline-offset":[C,B]}],"outline-w":[{outline:[C,M]}],"outline-color":[{outline:[t]}],"ring-w":[{ring:U()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[t]}],"ring-opacity":[{"ring-opacity":[v]}],"ring-offset-w":[{"ring-offset":[C,M]}],"ring-offset-color":[{"ring-offset":[t]}],shadow:[{shadow:["","inner","none",F,$]}],"shadow-color":[{shadow:[W]}],opacity:[{opacity:[v]}],"mix-blend":[{"mix-blend":[...Y(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":Y()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[i]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",F,B]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[h]}],invert:[{invert:[c]}],saturate:[{saturate:[b]}],sepia:[{sepia:[P]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[i]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[h]}],"backdrop-invert":[{"backdrop-invert":[c]}],"backdrop-opacity":[{"backdrop-opacity":[v]}],"backdrop-saturate":[{"backdrop-saturate":[b]}],"backdrop-sepia":[{"backdrop-sepia":[P]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[s]}],"border-spacing-x":[{"border-spacing-x":[s]}],"border-spacing-y":[{"border-spacing-y":[s]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",B]}],duration:[{duration:K()}],ease:[{ease:["linear","in","out","in-out",B]}],delay:[{delay:K()}],animate:[{animate:["none","spin","ping","pulse","bounce",B]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[w]}],"scale-x":[{"scale-x":[w]}],"scale-y":[{"scale-y":[w]}],rotate:[{rotate:[R,B]}],"translate-x":[{"translate-x":[S]}],"translate-y":[{"translate-y":[S]}],"skew-x":[{"skew-x":[T]}],"skew-y":[{"skew-y":[T]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",B]}],accent:[{accent:["auto",t]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",B]}],"caret-color":[{caret:[t]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":O()}],"scroll-mx":[{"scroll-mx":O()}],"scroll-my":[{"scroll-my":O()}],"scroll-ms":[{"scroll-ms":O()}],"scroll-me":[{"scroll-me":O()}],"scroll-mt":[{"scroll-mt":O()}],"scroll-mr":[{"scroll-mr":O()}],"scroll-mb":[{"scroll-mb":O()}],"scroll-ml":[{"scroll-ml":O()}],"scroll-p":[{"scroll-p":O()}],"scroll-px":[{"scroll-px":O()}],"scroll-py":[{"scroll-py":O()}],"scroll-ps":[{"scroll-ps":O()}],"scroll-pe":[{"scroll-pe":O()}],"scroll-pt":[{"scroll-pt":O()}],"scroll-pr":[{"scroll-pr":O()}],"scroll-pb":[{"scroll-pb":O()}],"scroll-pl":[{"scroll-pl":O()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",B]}],fill:[{fill:[t,"none"]}],"stroke-w":[{stroke:[C,M,D]}],stroke:[{stroke:[t,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}})}}]);