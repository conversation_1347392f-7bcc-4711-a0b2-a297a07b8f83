import * as cdk from 'aws-cdk-lib';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as cloudfront from 'aws-cdk-lib/aws-cloudfront';
import * as origins from 'aws-cdk-lib/aws-cloudfront-origins';
import * as s3deploy from 'aws-cdk-lib/aws-s3-deployment';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as certificatemanager from 'aws-cdk-lib/aws-certificatemanager';
import * as route53 from 'aws-cdk-lib/aws-route53';
import * as route53targets from 'aws-cdk-lib/aws-route53-targets';
import { Construct } from 'constructs';

export interface FrontendStackProps extends cdk.StackProps {
  environment: string;
  config: any;
  apiGatewayUrl: string;
  userPoolId: string;
  userPoolClientId: string;
  identityPoolId: string;
}

export class FrontendStack extends cdk.Stack {
  public readonly distributionDomainName: string;
  public readonly distributionId: string;

  constructor(scope: Construct, id: string, props: FrontendStackProps) {
    super(scope, id, props);

    const { environment, config, apiGatewayUrl, userPoolId, userPoolClientId, identityPoolId } = props;

    // S3 Bucket for hosting static website
    const websiteBucket = new s3.Bucket(this, 'WebsiteBucket', {
      bucketName: `ai-scanner-frontend-${environment}-${this.account}`,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      encryption: s3.BucketEncryption.S3_MANAGED,
      versioned: true,
      removalPolicy: environment === 'prod' ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY
    });

    // Origin Access Identity for CloudFront
    const originAccessIdentity = new cloudfront.OriginAccessIdentity(this, 'OAI', {
      comment: `OAI for AI Scanner Frontend - ${environment}`
    });

    // Grant CloudFront access to S3 bucket
    websiteBucket.addToResourcePolicy(new iam.PolicyStatement({
      actions: ['s3:GetObject'],
      resources: [websiteBucket.arnForObjects('*')],
      principals: [new iam.CanonicalUserPrincipal(originAccessIdentity.cloudFrontOriginAccessIdentityS3CanonicalUserId)]
    }));

    // CloudFront Distribution
    let certificate: certificatemanager.ICertificate | undefined;
    let domainNames: string[] | undefined;

    if (config.domainName && config.certificateArn) {
      certificate = certificatemanager.Certificate.fromCertificateArn(
        this,
        'Certificate',
        config.certificateArn
      );
      domainNames = [config.domainName, `www.${config.domainName}`];
    }

    const distribution = new cloudfront.Distribution(this, 'Distribution', {
      defaultBehavior: {
        origin: new origins.S3Origin(websiteBucket, {
          originAccessIdentity
        }),
        viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
        allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD_OPTIONS,
        cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD_OPTIONS,
        compress: true,
        cachePolicy: cloudfront.CachePolicy.CACHING_OPTIMIZED
      },
      additionalBehaviors: {
        '/api/*': {
          origin: new origins.HttpOrigin(apiGatewayUrl.replace('https://', '').replace('http://', '')),
          viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
          allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
          cachePolicy: cloudfront.CachePolicy.CACHING_DISABLED,
          originRequestPolicy: cloudfront.OriginRequestPolicy.CORS_S3_ORIGIN
        }
      },
      defaultRootObject: 'index.html',
      errorResponses: [
        {
          httpStatus: 404,
          responseHttpStatus: 200,
          responsePagePath: '/index.html',
          ttl: cdk.Duration.minutes(5)
        },
        {
          httpStatus: 403,
          responseHttpStatus: 200,
          responsePagePath: '/index.html',
          ttl: cdk.Duration.minutes(5)
        }
      ],
      domainNames,
      certificate,
      minimumProtocolVersion: cloudfront.SecurityPolicyProtocol.TLS_V1_2_2021,
      httpVersion: cloudfront.HttpVersion.HTTP2,
      enableIpv6: true,
      comment: `AI Scanner Frontend Distribution - ${environment}`,
      enabled: true
    });

    // Route 53 Records (if domain is configured)
    if (config.domainName) {
      const hostedZone = route53.HostedZone.fromLookup(this, 'HostedZone', {
        domainName: config.domainName
      });

      new route53.ARecord(this, 'AliasRecord', {
        zone: hostedZone,
        recordName: config.domainName,
        target: route53.RecordTarget.fromAlias(new route53targets.CloudFrontTarget(distribution))
      });

      new route53.ARecord(this, 'WwwAliasRecord', {
        zone: hostedZone,
        recordName: `www.${config.domainName}`,
        target: route53.RecordTarget.fromAlias(new route53targets.CloudFrontTarget(distribution))
      });
    }

    // Create environment configuration file for frontend
    const envConfig = {
      REACT_APP_API_URL: apiGatewayUrl,
      REACT_APP_USER_POOL_ID: userPoolId,
      REACT_APP_USER_POOL_CLIENT_ID: userPoolClientId,
      REACT_APP_IDENTITY_POOL_ID: identityPoolId,
      REACT_APP_REGION: this.region,
      REACT_APP_ENVIRONMENT: environment
    };

    // Deploy frontend assets (placeholder - actual deployment will be done via CI/CD)
    new s3deploy.BucketDeployment(this, 'DeployWebsite', {
      sources: [
        s3deploy.Source.data('env.json', JSON.stringify(envConfig, null, 2)),
        s3deploy.Source.data('index.html', this.getPlaceholderHtml(environment))
      ],
      destinationBucket: websiteBucket,
      distribution,
      distributionPaths: ['/*']
    });

    // Store outputs
    this.distributionDomainName = distribution.distributionDomainName;
    this.distributionId = distribution.distributionId;

    // CloudFormation Outputs
    new cdk.CfnOutput(this, 'WebsiteUrl', {
      value: config.domainName 
        ? `https://${config.domainName}`
        : `https://${distribution.distributionDomainName}`,
      description: 'Website URL'
    });

    new cdk.CfnOutput(this, 'DistributionId', {
      value: distribution.distributionId,
      description: 'CloudFront Distribution ID'
    });

    new cdk.CfnOutput(this, 'WebsiteBucket', {
      value: websiteBucket.bucketName,
      description: 'S3 Website Bucket Name'
    });
  }

  private getPlaceholderHtml(environment: string): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Security Scanner - ${environment.toUpperCase()}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            text-align: center;
            max-width: 600px;
            padding: 2rem;
        }
        h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        .badge {
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            display: inline-block;
            margin-bottom: 2rem;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        .feature {
            background: rgba(255,255,255,0.1);
            padding: 1rem;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛡️ AI Security Scanner</h1>
        <div class="badge">Environment: ${environment.toUpperCase()}</div>
        <p>Production-ready AI-powered security and compliance scanning platform</p>
        
        <div class="features">
            <div class="feature">
                <h3>🔍 Security Scanning</h3>
                <p>Detect malware, vulnerabilities, and suspicious patterns</p>
            </div>
            <div class="feature">
                <h3>📋 Compliance Checking</h3>
                <p>GDPR, HIPAA, PCI-DSS compliance validation</p>
            </div>
            <div class="feature">
                <h3>🤖 AI Anomaly Detection</h3>
                <p>ML-powered behavioral analysis and outlier detection</p>
            </div>
            <div class="feature">
                <h3>📊 Smart Reporting</h3>
                <p>Executive summaries and detailed technical reports</p>
            </div>
        </div>
        
        <p style="margin-top: 2rem; font-size: 1rem; opacity: 0.7;">
            Infrastructure deployed successfully. Frontend application will be available after build deployment.
        </p>
    </div>
</body>
</html>`;
  }
}
