# Demo Data and Sample Files

This directory contains sample files for testing the AI Security & Compliance Scanner.

## Sample Files

### 1. Security Test Files

#### `malicious-sample.txt`
Contains various security threats for testing:
- API keys and secrets
- SQL injection patterns
- Suspicious URLs
- Potential malware signatures

#### `credentials-leak.json`
JSON file with exposed credentials:
```json
{
  "database": {
    "host": "db.example.com",
    "username": "admin",
    "password": "super_secret_password_123",
    "api_key": "sk-1234567890abcdef1234567890abcdef"
  },
  "aws": {
    "access_key": "AKIAIOSFODNN7EXAMPLE",
    "secret_key": "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
  }
}
```

### 2. PII Test Files

#### `customer-data.csv`
CSV file containing PII for compliance testing:
```csv
Name,Email,<PERSON>,SSN,Address
<PERSON>,<EMAIL>,************,***********,"123 Main St, Anytown, ST 12345"
<PERSON>,<EMAIL>,************,***********,"456 Oak Ave, Somewhere, ST 67890"
```

#### `medical-records.txt`
Text file with HIPAA-protected information:
```
Patient: John Doe
DOB: 01/15/1980
SSN: ***********
Medical Record #: MR123456
Diagnosis: Hypertension
Treatment: Lisinopril 10mg daily
```

### 3. Anomaly Test Files

#### `encrypted-data.txt`
Base64 encoded content to test anomaly detection:
```
VGhpcyBpcyBhIHRlc3QgZmlsZSB3aXRoIGVuY3J5cHRlZCBjb250ZW50IGZvciBhbm9tYWx5IGRldGVjdGlvbi4gSXQgY29udGFpbnMgcmVwZWF0ZWQgcGF0dGVybnMgYW5kIHVudXN1YWwgY2hhcmFjdGVyIGZyZXF1ZW5jaWVzLg==
```

#### `repetitive-pattern.txt`
File with unusual repetitive patterns:
```
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
```

### 4. Clean Test Files

#### `clean-document.txt`
Normal document without security issues:
```
This is a clean document for testing purposes.
It contains no sensitive information, credentials, or malicious content.
The document is used to verify that the scanner correctly identifies clean files.
```

#### `business-report.pdf`
Standard business report (PDF format) for testing document processing.

## Test Scenarios

### Security Scanning Tests
1. **Credential Detection**: Upload `credentials-leak.json` and verify API keys are detected
2. **Malware Signatures**: Upload `malicious-sample.txt` and check for threat detection
3. **Clean File**: Upload `clean-document.txt` and verify no false positives

### PII Compliance Tests
1. **GDPR Compliance**: Upload `customer-data.csv` and check for email/phone detection
2. **HIPAA Compliance**: Upload `medical-records.txt` and verify PHI detection
3. **PCI DSS**: Upload files with credit card numbers

### Anomaly Detection Tests
1. **Encrypted Content**: Upload `encrypted-data.txt` and check entropy analysis
2. **Pattern Anomalies**: Upload `repetitive-pattern.txt` and verify pattern detection
3. **Statistical Analysis**: Test various file types for statistical anomalies

## Usage Instructions

### 1. Manual Testing
1. Navigate to the scanner web interface
2. Upload sample files from this directory
3. Select appropriate scan type (Security, Compliance, Anomaly, or Full)
4. Review results and verify expected detections

### 2. Automated Testing
```bash
# Run integration tests with sample files
cd tests/integration
python test_sample_files.py

# Run specific test scenarios
python test_security_detection.py
python test_pii_compliance.py
python test_anomaly_detection.py
```

### 3. API Testing
```bash
# Upload file via API
curl -X POST "https://api.ai-scanner.com/upload" \
  -H "Authorization: Bearer $TOKEN" \
  -F "file=@sample-files/credentials-leak.json" \
  -F "scanType=security"

# Check scan status
curl -X GET "https://api.ai-scanner.com/scan/$SCAN_ID" \
  -H "Authorization: Bearer $TOKEN"
```

## Expected Results

### Security Scan Results
- **High Risk**: Files with credentials, malware signatures
- **Medium Risk**: Files with suspicious patterns
- **Low Risk**: Clean files with minor issues
- **Minimal Risk**: Completely clean files

### PII Compliance Results
- **GDPR Violations**: Email addresses, phone numbers, names
- **HIPAA Violations**: SSNs, medical record numbers, health information
- **PCI DSS Violations**: Credit card numbers, payment information

### Anomaly Detection Results
- **High Entropy**: Encrypted or compressed content
- **Pattern Anomalies**: Repetitive sequences, unusual character distributions
- **Statistical Outliers**: Unusual file characteristics

## File Formats Supported

- **Text Files**: .txt, .csv, .json, .xml
- **Documents**: .pdf, .docx, .doc
- **Spreadsheets**: .xlsx, .xls
- **Images**: .jpg, .png, .gif, .bmp, .tiff

## Security Considerations

⚠️ **Warning**: The sample files in this directory contain intentionally malicious or sensitive content for testing purposes. Do not use these files in production environments or share them outside of testing contexts.

### Safe Handling
1. Keep sample files in isolated test environments
2. Do not commit real credentials or sensitive data
3. Use placeholder data that resembles real patterns
4. Regularly rotate test credentials

### Test Data Privacy
- All PII in test files is fictional
- No real personal information is used
- Test data follows privacy-by-design principles

## Contributing Test Data

When adding new test files:

1. **Document the purpose**: Clearly explain what the file tests
2. **Use fictional data**: Never include real sensitive information
3. **Follow naming conventions**: Use descriptive filenames
4. **Update this README**: Add documentation for new test cases
5. **Include expected results**: Document what the scanner should detect

## Troubleshooting

### Common Issues
1. **File size limits**: Ensure test files are under 50MB
2. **Format support**: Verify file format is supported
3. **Network timeouts**: Large files may take longer to process
4. **Rate limiting**: Space out API requests during testing

### Debug Mode
Enable debug logging for detailed test output:
```bash
export DEBUG=true
export LOG_LEVEL=debug
python test_sample_files.py
```
