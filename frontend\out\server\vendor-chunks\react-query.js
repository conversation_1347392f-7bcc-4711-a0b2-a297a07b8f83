/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-query";
exports.ids = ["vendor-chunks/react-query"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-query/es/core/focusManager.js":
/*!**********************************************************!*\
  !*** ./node_modules/react-query/es/core/focusManager.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusManager: () => (/* binding */ FocusManager),\n/* harmony export */   focusManager: () => (/* binding */ focusManager)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js\");\n/* harmony import */ var _subscribable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./subscribable */ \"(ssr)/./node_modules/react-query/es/core/subscribable.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n\n\n\nvar FocusManager = /*#__PURE__*/function (_Subscribable) {\n  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(FocusManager, _Subscribable);\n\n  function FocusManager() {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n\n    _this.setup = function (onFocus) {\n      var _window;\n\n      if (!_utils__WEBPACK_IMPORTED_MODULE_1__.isServer && ((_window = window) == null ? void 0 : _window.addEventListener)) {\n        var listener = function listener() {\n          return onFocus();\n        }; // Listen to visibillitychange and focus\n\n\n        window.addEventListener('visibilitychange', listener, false);\n        window.addEventListener('focus', listener, false);\n        return function () {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('visibilitychange', listener);\n          window.removeEventListener('focus', listener);\n        };\n      }\n    };\n\n    return _this;\n  }\n\n  var _proto = FocusManager.prototype;\n\n  _proto.onSubscribe = function onSubscribe() {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup);\n    }\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.hasListeners()) {\n      var _this$cleanup;\n\n      (_this$cleanup = this.cleanup) == null ? void 0 : _this$cleanup.call(this);\n      this.cleanup = undefined;\n    }\n  };\n\n  _proto.setEventListener = function setEventListener(setup) {\n    var _this$cleanup2,\n        _this2 = this;\n\n    this.setup = setup;\n    (_this$cleanup2 = this.cleanup) == null ? void 0 : _this$cleanup2.call(this);\n    this.cleanup = setup(function (focused) {\n      if (typeof focused === 'boolean') {\n        _this2.setFocused(focused);\n      } else {\n        _this2.onFocus();\n      }\n    });\n  };\n\n  _proto.setFocused = function setFocused(focused) {\n    this.focused = focused;\n\n    if (focused) {\n      this.onFocus();\n    }\n  };\n\n  _proto.onFocus = function onFocus() {\n    this.listeners.forEach(function (listener) {\n      listener();\n    });\n  };\n\n  _proto.isFocused = function isFocused() {\n    if (typeof this.focused === 'boolean') {\n      return this.focused;\n    } // document global can be unavailable in react native\n\n\n    if (typeof document === 'undefined') {\n      return true;\n    }\n\n    return [undefined, 'visible', 'prerender'].includes(document.visibilityState);\n  };\n\n  return FocusManager;\n}(_subscribable__WEBPACK_IMPORTED_MODULE_2__.Subscribable);\nvar focusManager = new FocusManager();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/focusManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/hydration.js":
/*!*******************************************************!*\
  !*** ./node_modules/react-query/es/core/hydration.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dehydrate: () => (/* binding */ dehydrate),\n/* harmony export */   hydrate: () => (/* binding */ hydrate)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n\n\n// TYPES\n// FUNCTIONS\nfunction dehydrateMutation(mutation) {\n  return {\n    mutationKey: mutation.options.mutationKey,\n    state: mutation.state\n  };\n} // Most config is not dehydrated but instead meant to configure again when\n// consuming the de/rehydrated data, typically with useQuery on the client.\n// Sometimes it might make sense to prefetch data on the server and include\n// in the html-payload, but not consume it on the initial render.\n\n\nfunction dehydrateQuery(query) {\n  return {\n    state: query.state,\n    queryKey: query.queryKey,\n    queryHash: query.queryHash\n  };\n}\n\nfunction defaultShouldDehydrateMutation(mutation) {\n  return mutation.state.isPaused;\n}\n\nfunction defaultShouldDehydrateQuery(query) {\n  return query.state.status === 'success';\n}\n\nfunction dehydrate(client, options) {\n  var _options, _options2;\n\n  options = options || {};\n  var mutations = [];\n  var queries = [];\n\n  if (((_options = options) == null ? void 0 : _options.dehydrateMutations) !== false) {\n    var shouldDehydrateMutation = options.shouldDehydrateMutation || defaultShouldDehydrateMutation;\n    client.getMutationCache().getAll().forEach(function (mutation) {\n      if (shouldDehydrateMutation(mutation)) {\n        mutations.push(dehydrateMutation(mutation));\n      }\n    });\n  }\n\n  if (((_options2 = options) == null ? void 0 : _options2.dehydrateQueries) !== false) {\n    var shouldDehydrateQuery = options.shouldDehydrateQuery || defaultShouldDehydrateQuery;\n    client.getQueryCache().getAll().forEach(function (query) {\n      if (shouldDehydrateQuery(query)) {\n        queries.push(dehydrateQuery(query));\n      }\n    });\n  }\n\n  return {\n    mutations: mutations,\n    queries: queries\n  };\n}\nfunction hydrate(client, dehydratedState, options) {\n  if (typeof dehydratedState !== 'object' || dehydratedState === null) {\n    return;\n  }\n\n  var mutationCache = client.getMutationCache();\n  var queryCache = client.getQueryCache();\n  var mutations = dehydratedState.mutations || [];\n  var queries = dehydratedState.queries || [];\n  mutations.forEach(function (dehydratedMutation) {\n    var _options$defaultOptio;\n\n    mutationCache.build(client, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, options == null ? void 0 : (_options$defaultOptio = options.defaultOptions) == null ? void 0 : _options$defaultOptio.mutations, {\n      mutationKey: dehydratedMutation.mutationKey\n    }), dehydratedMutation.state);\n  });\n  queries.forEach(function (dehydratedQuery) {\n    var _options$defaultOptio2;\n\n    var query = queryCache.get(dehydratedQuery.queryHash); // Do not hydrate if an existing query exists with newer data\n\n    if (query) {\n      if (query.state.dataUpdatedAt < dehydratedQuery.state.dataUpdatedAt) {\n        query.setState(dehydratedQuery.state);\n      }\n\n      return;\n    } // Restore query\n\n\n    queryCache.build(client, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, options == null ? void 0 : (_options$defaultOptio2 = options.defaultOptions) == null ? void 0 : _options$defaultOptio2.queries, {\n      queryKey: dehydratedQuery.queryKey,\n      queryHash: dehydratedQuery.queryHash\n    }), dehydratedQuery.state);\n  });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/hydration.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/index.js":
/*!***************************************************!*\
  !*** ./node_modules/react-query/es/core/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CancelledError: () => (/* reexport safe */ _retryer__WEBPACK_IMPORTED_MODULE_0__.CancelledError),\n/* harmony export */   InfiniteQueryObserver: () => (/* reexport safe */ _infiniteQueryObserver__WEBPACK_IMPORTED_MODULE_5__.InfiniteQueryObserver),\n/* harmony export */   MutationCache: () => (/* reexport safe */ _mutationCache__WEBPACK_IMPORTED_MODULE_6__.MutationCache),\n/* harmony export */   MutationObserver: () => (/* reexport safe */ _mutationObserver__WEBPACK_IMPORTED_MODULE_7__.MutationObserver),\n/* harmony export */   QueriesObserver: () => (/* reexport safe */ _queriesObserver__WEBPACK_IMPORTED_MODULE_4__.QueriesObserver),\n/* harmony export */   QueryCache: () => (/* reexport safe */ _queryCache__WEBPACK_IMPORTED_MODULE_1__.QueryCache),\n/* harmony export */   QueryClient: () => (/* reexport safe */ _queryClient__WEBPACK_IMPORTED_MODULE_2__.QueryClient),\n/* harmony export */   QueryObserver: () => (/* reexport safe */ _queryObserver__WEBPACK_IMPORTED_MODULE_3__.QueryObserver),\n/* harmony export */   dehydrate: () => (/* reexport safe */ _hydration__WEBPACK_IMPORTED_MODULE_13__.dehydrate),\n/* harmony export */   focusManager: () => (/* reexport safe */ _focusManager__WEBPACK_IMPORTED_MODULE_10__.focusManager),\n/* harmony export */   hashQueryKey: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_12__.hashQueryKey),\n/* harmony export */   hydrate: () => (/* reexport safe */ _hydration__WEBPACK_IMPORTED_MODULE_13__.hydrate),\n/* harmony export */   isCancelledError: () => (/* reexport safe */ _retryer__WEBPACK_IMPORTED_MODULE_0__.isCancelledError),\n/* harmony export */   isError: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_12__.isError),\n/* harmony export */   notifyManager: () => (/* reexport safe */ _notifyManager__WEBPACK_IMPORTED_MODULE_9__.notifyManager),\n/* harmony export */   onlineManager: () => (/* reexport safe */ _onlineManager__WEBPACK_IMPORTED_MODULE_11__.onlineManager),\n/* harmony export */   setLogger: () => (/* reexport safe */ _logger__WEBPACK_IMPORTED_MODULE_8__.setLogger)\n/* harmony export */ });\n/* harmony import */ var _retryer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./retryer */ \"(ssr)/./node_modules/react-query/es/core/retryer.js\");\n/* harmony import */ var _queryCache__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./queryCache */ \"(ssr)/./node_modules/react-query/es/core/queryCache.js\");\n/* harmony import */ var _queryClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./queryClient */ \"(ssr)/./node_modules/react-query/es/core/queryClient.js\");\n/* harmony import */ var _queryObserver__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./queryObserver */ \"(ssr)/./node_modules/react-query/es/core/queryObserver.js\");\n/* harmony import */ var _queriesObserver__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./queriesObserver */ \"(ssr)/./node_modules/react-query/es/core/queriesObserver.js\");\n/* harmony import */ var _infiniteQueryObserver__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./infiniteQueryObserver */ \"(ssr)/./node_modules/react-query/es/core/infiniteQueryObserver.js\");\n/* harmony import */ var _mutationCache__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./mutationCache */ \"(ssr)/./node_modules/react-query/es/core/mutationCache.js\");\n/* harmony import */ var _mutationObserver__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./mutationObserver */ \"(ssr)/./node_modules/react-query/es/core/mutationObserver.js\");\n/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./logger */ \"(ssr)/./node_modules/react-query/es/core/logger.js\");\n/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _focusManager__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./focusManager */ \"(ssr)/./node_modules/react-query/es/core/focusManager.js\");\n/* harmony import */ var _onlineManager__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./onlineManager */ \"(ssr)/./node_modules/react-query/es/core/onlineManager.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _hydration__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./hydration */ \"(ssr)/./node_modules/react-query/es/core/hydration.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/react-query/es/core/types.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(_types__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _types__WEBPACK_IMPORTED_MODULE_14__) if([\"default\",\"CancelledError\",\"QueryCache\",\"QueryClient\",\"QueryObserver\",\"QueriesObserver\",\"InfiniteQueryObserver\",\"MutationCache\",\"MutationObserver\",\"setLogger\",\"notifyManager\",\"focusManager\",\"onlineManager\",\"hashQueryKey\",\"isError\",\"isCancelledError\",\"dehydrate\",\"hydrate\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _types__WEBPACK_IMPORTED_MODULE_14__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n // Types\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvY29yZS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBMkM7QUFDRDtBQUNFO0FBQ0k7QUFDSTtBQUNZO0FBQ2hCO0FBQ007QUFDakI7QUFDVztBQUNGO0FBQ0U7QUFDQTtBQUNIO0FBQ0ksQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXNlY3VyaXR5LXNjYW5uZXItZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvY29yZS9pbmRleC5qcz85MmYwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IENhbmNlbGxlZEVycm9yIH0gZnJvbSAnLi9yZXRyeWVyJztcbmV4cG9ydCB7IFF1ZXJ5Q2FjaGUgfSBmcm9tICcuL3F1ZXJ5Q2FjaGUnO1xuZXhwb3J0IHsgUXVlcnlDbGllbnQgfSBmcm9tICcuL3F1ZXJ5Q2xpZW50JztcbmV4cG9ydCB7IFF1ZXJ5T2JzZXJ2ZXIgfSBmcm9tICcuL3F1ZXJ5T2JzZXJ2ZXInO1xuZXhwb3J0IHsgUXVlcmllc09ic2VydmVyIH0gZnJvbSAnLi9xdWVyaWVzT2JzZXJ2ZXInO1xuZXhwb3J0IHsgSW5maW5pdGVRdWVyeU9ic2VydmVyIH0gZnJvbSAnLi9pbmZpbml0ZVF1ZXJ5T2JzZXJ2ZXInO1xuZXhwb3J0IHsgTXV0YXRpb25DYWNoZSB9IGZyb20gJy4vbXV0YXRpb25DYWNoZSc7XG5leHBvcnQgeyBNdXRhdGlvbk9ic2VydmVyIH0gZnJvbSAnLi9tdXRhdGlvbk9ic2VydmVyJztcbmV4cG9ydCB7IHNldExvZ2dlciB9IGZyb20gJy4vbG9nZ2VyJztcbmV4cG9ydCB7IG5vdGlmeU1hbmFnZXIgfSBmcm9tICcuL25vdGlmeU1hbmFnZXInO1xuZXhwb3J0IHsgZm9jdXNNYW5hZ2VyIH0gZnJvbSAnLi9mb2N1c01hbmFnZXInO1xuZXhwb3J0IHsgb25saW5lTWFuYWdlciB9IGZyb20gJy4vb25saW5lTWFuYWdlcic7XG5leHBvcnQgeyBoYXNoUXVlcnlLZXksIGlzRXJyb3IgfSBmcm9tICcuL3V0aWxzJztcbmV4cG9ydCB7IGlzQ2FuY2VsbGVkRXJyb3IgfSBmcm9tICcuL3JldHJ5ZXInO1xuZXhwb3J0IHsgZGVoeWRyYXRlLCBoeWRyYXRlIH0gZnJvbSAnLi9oeWRyYXRpb24nOyAvLyBUeXBlc1xuXG5leHBvcnQgKiBmcm9tICcuL3R5cGVzJzsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/infiniteQueryBehavior.js":
/*!*******************************************************************!*\
  !*** ./node_modules/react-query/es/core/infiniteQueryBehavior.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getNextPageParam: () => (/* binding */ getNextPageParam),\n/* harmony export */   getPreviousPageParam: () => (/* binding */ getPreviousPageParam),\n/* harmony export */   hasNextPage: () => (/* binding */ hasNextPage),\n/* harmony export */   hasPreviousPage: () => (/* binding */ hasPreviousPage),\n/* harmony export */   infiniteQueryBehavior: () => (/* binding */ infiniteQueryBehavior)\n/* harmony export */ });\n/* harmony import */ var _retryer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./retryer */ \"(ssr)/./node_modules/react-query/es/core/retryer.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n\n\nfunction infiniteQueryBehavior() {\n  return {\n    onFetch: function onFetch(context) {\n      context.fetchFn = function () {\n        var _context$fetchOptions, _context$fetchOptions2, _context$fetchOptions3, _context$fetchOptions4, _context$state$data, _context$state$data2;\n\n        var refetchPage = (_context$fetchOptions = context.fetchOptions) == null ? void 0 : (_context$fetchOptions2 = _context$fetchOptions.meta) == null ? void 0 : _context$fetchOptions2.refetchPage;\n        var fetchMore = (_context$fetchOptions3 = context.fetchOptions) == null ? void 0 : (_context$fetchOptions4 = _context$fetchOptions3.meta) == null ? void 0 : _context$fetchOptions4.fetchMore;\n        var pageParam = fetchMore == null ? void 0 : fetchMore.pageParam;\n        var isFetchingNextPage = (fetchMore == null ? void 0 : fetchMore.direction) === 'forward';\n        var isFetchingPreviousPage = (fetchMore == null ? void 0 : fetchMore.direction) === 'backward';\n        var oldPages = ((_context$state$data = context.state.data) == null ? void 0 : _context$state$data.pages) || [];\n        var oldPageParams = ((_context$state$data2 = context.state.data) == null ? void 0 : _context$state$data2.pageParams) || [];\n        var abortController = (0,_utils__WEBPACK_IMPORTED_MODULE_0__.getAbortController)();\n        var abortSignal = abortController == null ? void 0 : abortController.signal;\n        var newPageParams = oldPageParams;\n        var cancelled = false; // Get query function\n\n        var queryFn = context.options.queryFn || function () {\n          return Promise.reject('Missing queryFn');\n        };\n\n        var buildNewPages = function buildNewPages(pages, param, page, previous) {\n          newPageParams = previous ? [param].concat(newPageParams) : [].concat(newPageParams, [param]);\n          return previous ? [page].concat(pages) : [].concat(pages, [page]);\n        }; // Create function to fetch a page\n\n\n        var fetchPage = function fetchPage(pages, manual, param, previous) {\n          if (cancelled) {\n            return Promise.reject('Cancelled');\n          }\n\n          if (typeof param === 'undefined' && !manual && pages.length) {\n            return Promise.resolve(pages);\n          }\n\n          var queryFnContext = {\n            queryKey: context.queryKey,\n            signal: abortSignal,\n            pageParam: param,\n            meta: context.meta\n          };\n          var queryFnResult = queryFn(queryFnContext);\n          var promise = Promise.resolve(queryFnResult).then(function (page) {\n            return buildNewPages(pages, param, page, previous);\n          });\n\n          if ((0,_retryer__WEBPACK_IMPORTED_MODULE_1__.isCancelable)(queryFnResult)) {\n            var promiseAsAny = promise;\n            promiseAsAny.cancel = queryFnResult.cancel;\n          }\n\n          return promise;\n        };\n\n        var promise; // Fetch first page?\n\n        if (!oldPages.length) {\n          promise = fetchPage([]);\n        } // Fetch next page?\n        else if (isFetchingNextPage) {\n            var manual = typeof pageParam !== 'undefined';\n            var param = manual ? pageParam : getNextPageParam(context.options, oldPages);\n            promise = fetchPage(oldPages, manual, param);\n          } // Fetch previous page?\n          else if (isFetchingPreviousPage) {\n              var _manual = typeof pageParam !== 'undefined';\n\n              var _param = _manual ? pageParam : getPreviousPageParam(context.options, oldPages);\n\n              promise = fetchPage(oldPages, _manual, _param, true);\n            } // Refetch pages\n            else {\n                (function () {\n                  newPageParams = [];\n                  var manual = typeof context.options.getNextPageParam === 'undefined';\n                  var shouldFetchFirstPage = refetchPage && oldPages[0] ? refetchPage(oldPages[0], 0, oldPages) : true; // Fetch first page\n\n                  promise = shouldFetchFirstPage ? fetchPage([], manual, oldPageParams[0]) : Promise.resolve(buildNewPages([], oldPageParams[0], oldPages[0])); // Fetch remaining pages\n\n                  var _loop = function _loop(i) {\n                    promise = promise.then(function (pages) {\n                      var shouldFetchNextPage = refetchPage && oldPages[i] ? refetchPage(oldPages[i], i, oldPages) : true;\n\n                      if (shouldFetchNextPage) {\n                        var _param2 = manual ? oldPageParams[i] : getNextPageParam(context.options, pages);\n\n                        return fetchPage(pages, manual, _param2);\n                      }\n\n                      return Promise.resolve(buildNewPages(pages, oldPageParams[i], oldPages[i]));\n                    });\n                  };\n\n                  for (var i = 1; i < oldPages.length; i++) {\n                    _loop(i);\n                  }\n                })();\n              }\n\n        var finalPromise = promise.then(function (pages) {\n          return {\n            pages: pages,\n            pageParams: newPageParams\n          };\n        });\n        var finalPromiseAsAny = finalPromise;\n\n        finalPromiseAsAny.cancel = function () {\n          cancelled = true;\n          abortController == null ? void 0 : abortController.abort();\n\n          if ((0,_retryer__WEBPACK_IMPORTED_MODULE_1__.isCancelable)(promise)) {\n            promise.cancel();\n          }\n        };\n\n        return finalPromise;\n      };\n    }\n  };\n}\nfunction getNextPageParam(options, pages) {\n  return options.getNextPageParam == null ? void 0 : options.getNextPageParam(pages[pages.length - 1], pages);\n}\nfunction getPreviousPageParam(options, pages) {\n  return options.getPreviousPageParam == null ? void 0 : options.getPreviousPageParam(pages[0], pages);\n}\n/**\n * Checks if there is a next page.\n * Returns `undefined` if it cannot be determined.\n */\n\nfunction hasNextPage(options, pages) {\n  if (options.getNextPageParam && Array.isArray(pages)) {\n    var nextPageParam = getNextPageParam(options, pages);\n    return typeof nextPageParam !== 'undefined' && nextPageParam !== null && nextPageParam !== false;\n  }\n}\n/**\n * Checks if there is a previous page.\n * Returns `undefined` if it cannot be determined.\n */\n\nfunction hasPreviousPage(options, pages) {\n  if (options.getPreviousPageParam && Array.isArray(pages)) {\n    var previousPageParam = getPreviousPageParam(options, pages);\n    return typeof previousPageParam !== 'undefined' && previousPageParam !== null && previousPageParam !== false;\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/infiniteQueryBehavior.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/infiniteQueryObserver.js":
/*!*******************************************************************!*\
  !*** ./node_modules/react-query/es/core/infiniteQueryObserver.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InfiniteQueryObserver: () => (/* binding */ InfiniteQueryObserver)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js\");\n/* harmony import */ var _queryObserver__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./queryObserver */ \"(ssr)/./node_modules/react-query/es/core/queryObserver.js\");\n/* harmony import */ var _infiniteQueryBehavior__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./infiniteQueryBehavior */ \"(ssr)/./node_modules/react-query/es/core/infiniteQueryBehavior.js\");\n\n\n\n\nvar InfiniteQueryObserver = /*#__PURE__*/function (_QueryObserver) {\n  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(InfiniteQueryObserver, _QueryObserver);\n\n  // Type override\n  // Type override\n  // Type override\n  // eslint-disable-next-line @typescript-eslint/no-useless-constructor\n  function InfiniteQueryObserver(client, options) {\n    return _QueryObserver.call(this, client, options) || this;\n  }\n\n  var _proto = InfiniteQueryObserver.prototype;\n\n  _proto.bindMethods = function bindMethods() {\n    _QueryObserver.prototype.bindMethods.call(this);\n\n    this.fetchNextPage = this.fetchNextPage.bind(this);\n    this.fetchPreviousPage = this.fetchPreviousPage.bind(this);\n  };\n\n  _proto.setOptions = function setOptions(options, notifyOptions) {\n    _QueryObserver.prototype.setOptions.call(this, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, options, {\n      behavior: (0,_infiniteQueryBehavior__WEBPACK_IMPORTED_MODULE_2__.infiniteQueryBehavior)()\n    }), notifyOptions);\n  };\n\n  _proto.getOptimisticResult = function getOptimisticResult(options) {\n    options.behavior = (0,_infiniteQueryBehavior__WEBPACK_IMPORTED_MODULE_2__.infiniteQueryBehavior)();\n    return _QueryObserver.prototype.getOptimisticResult.call(this, options);\n  };\n\n  _proto.fetchNextPage = function fetchNextPage(options) {\n    var _options$cancelRefetc;\n\n    return this.fetch({\n      // TODO consider removing `?? true` in future breaking change, to be consistent with `refetch` API (see https://github.com/tannerlinsley/react-query/issues/2617)\n      cancelRefetch: (_options$cancelRefetc = options == null ? void 0 : options.cancelRefetch) != null ? _options$cancelRefetc : true,\n      throwOnError: options == null ? void 0 : options.throwOnError,\n      meta: {\n        fetchMore: {\n          direction: 'forward',\n          pageParam: options == null ? void 0 : options.pageParam\n        }\n      }\n    });\n  };\n\n  _proto.fetchPreviousPage = function fetchPreviousPage(options) {\n    var _options$cancelRefetc2;\n\n    return this.fetch({\n      // TODO consider removing `?? true` in future breaking change, to be consistent with `refetch` API (see https://github.com/tannerlinsley/react-query/issues/2617)\n      cancelRefetch: (_options$cancelRefetc2 = options == null ? void 0 : options.cancelRefetch) != null ? _options$cancelRefetc2 : true,\n      throwOnError: options == null ? void 0 : options.throwOnError,\n      meta: {\n        fetchMore: {\n          direction: 'backward',\n          pageParam: options == null ? void 0 : options.pageParam\n        }\n      }\n    });\n  };\n\n  _proto.createResult = function createResult(query, options) {\n    var _state$data, _state$data2, _state$fetchMeta, _state$fetchMeta$fetc, _state$fetchMeta2, _state$fetchMeta2$fet;\n\n    var state = query.state;\n\n    var result = _QueryObserver.prototype.createResult.call(this, query, options);\n\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, result, {\n      fetchNextPage: this.fetchNextPage,\n      fetchPreviousPage: this.fetchPreviousPage,\n      hasNextPage: (0,_infiniteQueryBehavior__WEBPACK_IMPORTED_MODULE_2__.hasNextPage)(options, (_state$data = state.data) == null ? void 0 : _state$data.pages),\n      hasPreviousPage: (0,_infiniteQueryBehavior__WEBPACK_IMPORTED_MODULE_2__.hasPreviousPage)(options, (_state$data2 = state.data) == null ? void 0 : _state$data2.pages),\n      isFetchingNextPage: state.isFetching && ((_state$fetchMeta = state.fetchMeta) == null ? void 0 : (_state$fetchMeta$fetc = _state$fetchMeta.fetchMore) == null ? void 0 : _state$fetchMeta$fetc.direction) === 'forward',\n      isFetchingPreviousPage: state.isFetching && ((_state$fetchMeta2 = state.fetchMeta) == null ? void 0 : (_state$fetchMeta2$fet = _state$fetchMeta2.fetchMore) == null ? void 0 : _state$fetchMeta2$fet.direction) === 'backward'\n    });\n  };\n\n  return InfiniteQueryObserver;\n}(_queryObserver__WEBPACK_IMPORTED_MODULE_3__.QueryObserver);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/infiniteQueryObserver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/logger.js":
/*!****************************************************!*\
  !*** ./node_modules/react-query/es/core/logger.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLogger: () => (/* binding */ getLogger),\n/* harmony export */   setLogger: () => (/* binding */ setLogger)\n/* harmony export */ });\n// TYPES\n// FUNCTIONS\nvar logger = console;\nfunction getLogger() {\n  return logger;\n}\nfunction setLogger(newLogger) {\n  logger = newLogger;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvY29yZS9sb2dnZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1zZWN1cml0eS1zY2FubmVyLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3JlYWN0LXF1ZXJ5L2VzL2NvcmUvbG9nZ2VyLmpzP2JlYTEiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gVFlQRVNcbi8vIEZVTkNUSU9OU1xudmFyIGxvZ2dlciA9IGNvbnNvbGU7XG5leHBvcnQgZnVuY3Rpb24gZ2V0TG9nZ2VyKCkge1xuICByZXR1cm4gbG9nZ2VyO1xufVxuZXhwb3J0IGZ1bmN0aW9uIHNldExvZ2dlcihuZXdMb2dnZXIpIHtcbiAgbG9nZ2VyID0gbmV3TG9nZ2VyO1xufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/logger.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/mutation.js":
/*!******************************************************!*\
  !*** ./node_modules/react-query/es/core/mutation.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Mutation: () => (/* binding */ Mutation),\n/* harmony export */   getDefaultState: () => (/* binding */ getDefaultState)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./logger */ \"(ssr)/./node_modules/react-query/es/core/logger.js\");\n/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _retryer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./retryer */ \"(ssr)/./node_modules/react-query/es/core/retryer.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n\n\n\n\n // TYPES\n\n// CLASS\nvar Mutation = /*#__PURE__*/function () {\n  function Mutation(config) {\n    this.options = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, config.defaultOptions, config.options);\n    this.mutationId = config.mutationId;\n    this.mutationCache = config.mutationCache;\n    this.observers = [];\n    this.state = config.state || getDefaultState();\n    this.meta = config.meta;\n  }\n\n  var _proto = Mutation.prototype;\n\n  _proto.setState = function setState(state) {\n    this.dispatch({\n      type: 'setState',\n      state: state\n    });\n  };\n\n  _proto.addObserver = function addObserver(observer) {\n    if (this.observers.indexOf(observer) === -1) {\n      this.observers.push(observer);\n    }\n  };\n\n  _proto.removeObserver = function removeObserver(observer) {\n    this.observers = this.observers.filter(function (x) {\n      return x !== observer;\n    });\n  };\n\n  _proto.cancel = function cancel() {\n    if (this.retryer) {\n      this.retryer.cancel();\n      return this.retryer.promise.then(_utils__WEBPACK_IMPORTED_MODULE_1__.noop).catch(_utils__WEBPACK_IMPORTED_MODULE_1__.noop);\n    }\n\n    return Promise.resolve();\n  };\n\n  _proto.continue = function _continue() {\n    if (this.retryer) {\n      this.retryer.continue();\n      return this.retryer.promise;\n    }\n\n    return this.execute();\n  };\n\n  _proto.execute = function execute() {\n    var _this = this;\n\n    var data;\n    var restored = this.state.status === 'loading';\n    var promise = Promise.resolve();\n\n    if (!restored) {\n      this.dispatch({\n        type: 'loading',\n        variables: this.options.variables\n      });\n      promise = promise.then(function () {\n        // Notify cache callback\n        _this.mutationCache.config.onMutate == null ? void 0 : _this.mutationCache.config.onMutate(_this.state.variables, _this);\n      }).then(function () {\n        return _this.options.onMutate == null ? void 0 : _this.options.onMutate(_this.state.variables);\n      }).then(function (context) {\n        if (context !== _this.state.context) {\n          _this.dispatch({\n            type: 'loading',\n            context: context,\n            variables: _this.state.variables\n          });\n        }\n      });\n    }\n\n    return promise.then(function () {\n      return _this.executeMutation();\n    }).then(function (result) {\n      data = result; // Notify cache callback\n\n      _this.mutationCache.config.onSuccess == null ? void 0 : _this.mutationCache.config.onSuccess(data, _this.state.variables, _this.state.context, _this);\n    }).then(function () {\n      return _this.options.onSuccess == null ? void 0 : _this.options.onSuccess(data, _this.state.variables, _this.state.context);\n    }).then(function () {\n      return _this.options.onSettled == null ? void 0 : _this.options.onSettled(data, null, _this.state.variables, _this.state.context);\n    }).then(function () {\n      _this.dispatch({\n        type: 'success',\n        data: data\n      });\n\n      return data;\n    }).catch(function (error) {\n      // Notify cache callback\n      _this.mutationCache.config.onError == null ? void 0 : _this.mutationCache.config.onError(error, _this.state.variables, _this.state.context, _this); // Log error\n\n      (0,_logger__WEBPACK_IMPORTED_MODULE_2__.getLogger)().error(error);\n      return Promise.resolve().then(function () {\n        return _this.options.onError == null ? void 0 : _this.options.onError(error, _this.state.variables, _this.state.context);\n      }).then(function () {\n        return _this.options.onSettled == null ? void 0 : _this.options.onSettled(undefined, error, _this.state.variables, _this.state.context);\n      }).then(function () {\n        _this.dispatch({\n          type: 'error',\n          error: error\n        });\n\n        throw error;\n      });\n    });\n  };\n\n  _proto.executeMutation = function executeMutation() {\n    var _this2 = this,\n        _this$options$retry;\n\n    this.retryer = new _retryer__WEBPACK_IMPORTED_MODULE_3__.Retryer({\n      fn: function fn() {\n        if (!_this2.options.mutationFn) {\n          return Promise.reject('No mutationFn found');\n        }\n\n        return _this2.options.mutationFn(_this2.state.variables);\n      },\n      onFail: function onFail() {\n        _this2.dispatch({\n          type: 'failed'\n        });\n      },\n      onPause: function onPause() {\n        _this2.dispatch({\n          type: 'pause'\n        });\n      },\n      onContinue: function onContinue() {\n        _this2.dispatch({\n          type: 'continue'\n        });\n      },\n      retry: (_this$options$retry = this.options.retry) != null ? _this$options$retry : 0,\n      retryDelay: this.options.retryDelay\n    });\n    return this.retryer.promise;\n  };\n\n  _proto.dispatch = function dispatch(action) {\n    var _this3 = this;\n\n    this.state = reducer(this.state, action);\n    _notifyManager__WEBPACK_IMPORTED_MODULE_4__.notifyManager.batch(function () {\n      _this3.observers.forEach(function (observer) {\n        observer.onMutationUpdate(action);\n      });\n\n      _this3.mutationCache.notify(_this3);\n    });\n  };\n\n  return Mutation;\n}();\nfunction getDefaultState() {\n  return {\n    context: undefined,\n    data: undefined,\n    error: null,\n    failureCount: 0,\n    isPaused: false,\n    status: 'idle',\n    variables: undefined\n  };\n}\n\nfunction reducer(state, action) {\n  switch (action.type) {\n    case 'failed':\n      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n        failureCount: state.failureCount + 1\n      });\n\n    case 'pause':\n      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n        isPaused: true\n      });\n\n    case 'continue':\n      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n        isPaused: false\n      });\n\n    case 'loading':\n      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n        context: action.context,\n        data: undefined,\n        error: null,\n        isPaused: false,\n        status: 'loading',\n        variables: action.variables\n      });\n\n    case 'success':\n      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n        data: action.data,\n        error: null,\n        status: 'success',\n        isPaused: false\n      });\n\n    case 'error':\n      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n        data: undefined,\n        error: action.error,\n        failureCount: state.failureCount + 1,\n        isPaused: false,\n        status: 'error'\n      });\n\n    case 'setState':\n      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, action.state);\n\n    default:\n      return state;\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/mutation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/mutationCache.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-query/es/core/mutationCache.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MutationCache: () => (/* binding */ MutationCache)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js\");\n/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _mutation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mutation */ \"(ssr)/./node_modules/react-query/es/core/mutation.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _subscribable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./subscribable */ \"(ssr)/./node_modules/react-query/es/core/subscribable.js\");\n\n\n\n\n // TYPES\n\n// CLASS\nvar MutationCache = /*#__PURE__*/function (_Subscribable) {\n  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(MutationCache, _Subscribable);\n\n  function MutationCache(config) {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n    _this.config = config || {};\n    _this.mutations = [];\n    _this.mutationId = 0;\n    return _this;\n  }\n\n  var _proto = MutationCache.prototype;\n\n  _proto.build = function build(client, options, state) {\n    var mutation = new _mutation__WEBPACK_IMPORTED_MODULE_1__.Mutation({\n      mutationCache: this,\n      mutationId: ++this.mutationId,\n      options: client.defaultMutationOptions(options),\n      state: state,\n      defaultOptions: options.mutationKey ? client.getMutationDefaults(options.mutationKey) : undefined,\n      meta: options.meta\n    });\n    this.add(mutation);\n    return mutation;\n  };\n\n  _proto.add = function add(mutation) {\n    this.mutations.push(mutation);\n    this.notify(mutation);\n  };\n\n  _proto.remove = function remove(mutation) {\n    this.mutations = this.mutations.filter(function (x) {\n      return x !== mutation;\n    });\n    mutation.cancel();\n    this.notify(mutation);\n  };\n\n  _proto.clear = function clear() {\n    var _this2 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(function () {\n      _this2.mutations.forEach(function (mutation) {\n        _this2.remove(mutation);\n      });\n    });\n  };\n\n  _proto.getAll = function getAll() {\n    return this.mutations;\n  };\n\n  _proto.find = function find(filters) {\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true;\n    }\n\n    return this.mutations.find(function (mutation) {\n      return (0,_utils__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(filters, mutation);\n    });\n  };\n\n  _proto.findAll = function findAll(filters) {\n    return this.mutations.filter(function (mutation) {\n      return (0,_utils__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(filters, mutation);\n    });\n  };\n\n  _proto.notify = function notify(mutation) {\n    var _this3 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(function () {\n      _this3.listeners.forEach(function (listener) {\n        listener(mutation);\n      });\n    });\n  };\n\n  _proto.onFocus = function onFocus() {\n    this.resumePausedMutations();\n  };\n\n  _proto.onOnline = function onOnline() {\n    this.resumePausedMutations();\n  };\n\n  _proto.resumePausedMutations = function resumePausedMutations() {\n    var pausedMutations = this.mutations.filter(function (x) {\n      return x.state.isPaused;\n    });\n    return _notifyManager__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(function () {\n      return pausedMutations.reduce(function (promise, mutation) {\n        return promise.then(function () {\n          return mutation.continue().catch(_utils__WEBPACK_IMPORTED_MODULE_3__.noop);\n        });\n      }, Promise.resolve());\n    });\n  };\n\n  return MutationCache;\n}(_subscribable__WEBPACK_IMPORTED_MODULE_4__.Subscribable);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/mutationCache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/mutationObserver.js":
/*!**************************************************************!*\
  !*** ./node_modules/react-query/es/core/mutationObserver.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MutationObserver: () => (/* binding */ MutationObserver)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js\");\n/* harmony import */ var _mutation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mutation */ \"(ssr)/./node_modules/react-query/es/core/mutation.js\");\n/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _subscribable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./subscribable */ \"(ssr)/./node_modules/react-query/es/core/subscribable.js\");\n\n\n\n\n\n// CLASS\nvar MutationObserver = /*#__PURE__*/function (_Subscribable) {\n  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(MutationObserver, _Subscribable);\n\n  function MutationObserver(client, options) {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n    _this.client = client;\n\n    _this.setOptions(options);\n\n    _this.bindMethods();\n\n    _this.updateResult();\n\n    return _this;\n  }\n\n  var _proto = MutationObserver.prototype;\n\n  _proto.bindMethods = function bindMethods() {\n    this.mutate = this.mutate.bind(this);\n    this.reset = this.reset.bind(this);\n  };\n\n  _proto.setOptions = function setOptions(options) {\n    this.options = this.client.defaultMutationOptions(options);\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.listeners.length) {\n      var _this$currentMutation;\n\n      (_this$currentMutation = this.currentMutation) == null ? void 0 : _this$currentMutation.removeObserver(this);\n    }\n  };\n\n  _proto.onMutationUpdate = function onMutationUpdate(action) {\n    this.updateResult(); // Determine which callbacks to trigger\n\n    var notifyOptions = {\n      listeners: true\n    };\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = true;\n    } else if (action.type === 'error') {\n      notifyOptions.onError = true;\n    }\n\n    this.notify(notifyOptions);\n  };\n\n  _proto.getCurrentResult = function getCurrentResult() {\n    return this.currentResult;\n  };\n\n  _proto.reset = function reset() {\n    this.currentMutation = undefined;\n    this.updateResult();\n    this.notify({\n      listeners: true\n    });\n  };\n\n  _proto.mutate = function mutate(variables, options) {\n    this.mutateOptions = options;\n\n    if (this.currentMutation) {\n      this.currentMutation.removeObserver(this);\n    }\n\n    this.currentMutation = this.client.getMutationCache().build(this.client, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, this.options, {\n      variables: typeof variables !== 'undefined' ? variables : this.options.variables\n    }));\n    this.currentMutation.addObserver(this);\n    return this.currentMutation.execute();\n  };\n\n  _proto.updateResult = function updateResult() {\n    var state = this.currentMutation ? this.currentMutation.state : (0,_mutation__WEBPACK_IMPORTED_MODULE_2__.getDefaultState)();\n\n    var result = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n      isLoading: state.status === 'loading',\n      isSuccess: state.status === 'success',\n      isError: state.status === 'error',\n      isIdle: state.status === 'idle',\n      mutate: this.mutate,\n      reset: this.reset\n    });\n\n    this.currentResult = result;\n  };\n\n  _proto.notify = function notify(options) {\n    var _this2 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(function () {\n      // First trigger the mutate callbacks\n      if (_this2.mutateOptions) {\n        if (options.onSuccess) {\n          _this2.mutateOptions.onSuccess == null ? void 0 : _this2.mutateOptions.onSuccess(_this2.currentResult.data, _this2.currentResult.variables, _this2.currentResult.context);\n          _this2.mutateOptions.onSettled == null ? void 0 : _this2.mutateOptions.onSettled(_this2.currentResult.data, null, _this2.currentResult.variables, _this2.currentResult.context);\n        } else if (options.onError) {\n          _this2.mutateOptions.onError == null ? void 0 : _this2.mutateOptions.onError(_this2.currentResult.error, _this2.currentResult.variables, _this2.currentResult.context);\n          _this2.mutateOptions.onSettled == null ? void 0 : _this2.mutateOptions.onSettled(undefined, _this2.currentResult.error, _this2.currentResult.variables, _this2.currentResult.context);\n        }\n      } // Then trigger the listeners\n\n\n      if (options.listeners) {\n        _this2.listeners.forEach(function (listener) {\n          listener(_this2.currentResult);\n        });\n      }\n    });\n  };\n\n  return MutationObserver;\n}(_subscribable__WEBPACK_IMPORTED_MODULE_4__.Subscribable);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/mutationObserver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/notifyManager.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-query/es/core/notifyManager.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotifyManager: () => (/* binding */ NotifyManager),\n/* harmony export */   notifyManager: () => (/* binding */ notifyManager)\n/* harmony export */ });\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n // TYPES\n\n// CLASS\nvar NotifyManager = /*#__PURE__*/function () {\n  function NotifyManager() {\n    this.queue = [];\n    this.transactions = 0;\n\n    this.notifyFn = function (callback) {\n      callback();\n    };\n\n    this.batchNotifyFn = function (callback) {\n      callback();\n    };\n  }\n\n  var _proto = NotifyManager.prototype;\n\n  _proto.batch = function batch(callback) {\n    var result;\n    this.transactions++;\n\n    try {\n      result = callback();\n    } finally {\n      this.transactions--;\n\n      if (!this.transactions) {\n        this.flush();\n      }\n    }\n\n    return result;\n  };\n\n  _proto.schedule = function schedule(callback) {\n    var _this = this;\n\n    if (this.transactions) {\n      this.queue.push(callback);\n    } else {\n      (0,_utils__WEBPACK_IMPORTED_MODULE_0__.scheduleMicrotask)(function () {\n        _this.notifyFn(callback);\n      });\n    }\n  }\n  /**\n   * All calls to the wrapped function will be batched.\n   */\n  ;\n\n  _proto.batchCalls = function batchCalls(callback) {\n    var _this2 = this;\n\n    return function () {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n\n      _this2.schedule(function () {\n        callback.apply(void 0, args);\n      });\n    };\n  };\n\n  _proto.flush = function flush() {\n    var _this3 = this;\n\n    var queue = this.queue;\n    this.queue = [];\n\n    if (queue.length) {\n      (0,_utils__WEBPACK_IMPORTED_MODULE_0__.scheduleMicrotask)(function () {\n        _this3.batchNotifyFn(function () {\n          queue.forEach(function (callback) {\n            _this3.notifyFn(callback);\n          });\n        });\n      });\n    }\n  }\n  /**\n   * Use this method to set a custom notify function.\n   * This can be used to for example wrap notifications with `React.act` while running tests.\n   */\n  ;\n\n  _proto.setNotifyFunction = function setNotifyFunction(fn) {\n    this.notifyFn = fn;\n  }\n  /**\n   * Use this method to set a custom function to batch notifications together into a single tick.\n   * By default React Query will use the batch function provided by ReactDOM or React Native.\n   */\n  ;\n\n  _proto.setBatchNotifyFunction = function setBatchNotifyFunction(fn) {\n    this.batchNotifyFn = fn;\n  };\n\n  return NotifyManager;\n}(); // SINGLETON\n\nvar notifyManager = new NotifyManager();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/notifyManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/onlineManager.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-query/es/core/onlineManager.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OnlineManager: () => (/* binding */ OnlineManager),\n/* harmony export */   onlineManager: () => (/* binding */ onlineManager)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js\");\n/* harmony import */ var _subscribable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./subscribable */ \"(ssr)/./node_modules/react-query/es/core/subscribable.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n\n\n\nvar OnlineManager = /*#__PURE__*/function (_Subscribable) {\n  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(OnlineManager, _Subscribable);\n\n  function OnlineManager() {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n\n    _this.setup = function (onOnline) {\n      var _window;\n\n      if (!_utils__WEBPACK_IMPORTED_MODULE_1__.isServer && ((_window = window) == null ? void 0 : _window.addEventListener)) {\n        var listener = function listener() {\n          return onOnline();\n        }; // Listen to online\n\n\n        window.addEventListener('online', listener, false);\n        window.addEventListener('offline', listener, false);\n        return function () {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('online', listener);\n          window.removeEventListener('offline', listener);\n        };\n      }\n    };\n\n    return _this;\n  }\n\n  var _proto = OnlineManager.prototype;\n\n  _proto.onSubscribe = function onSubscribe() {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup);\n    }\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.hasListeners()) {\n      var _this$cleanup;\n\n      (_this$cleanup = this.cleanup) == null ? void 0 : _this$cleanup.call(this);\n      this.cleanup = undefined;\n    }\n  };\n\n  _proto.setEventListener = function setEventListener(setup) {\n    var _this$cleanup2,\n        _this2 = this;\n\n    this.setup = setup;\n    (_this$cleanup2 = this.cleanup) == null ? void 0 : _this$cleanup2.call(this);\n    this.cleanup = setup(function (online) {\n      if (typeof online === 'boolean') {\n        _this2.setOnline(online);\n      } else {\n        _this2.onOnline();\n      }\n    });\n  };\n\n  _proto.setOnline = function setOnline(online) {\n    this.online = online;\n\n    if (online) {\n      this.onOnline();\n    }\n  };\n\n  _proto.onOnline = function onOnline() {\n    this.listeners.forEach(function (listener) {\n      listener();\n    });\n  };\n\n  _proto.isOnline = function isOnline() {\n    if (typeof this.online === 'boolean') {\n      return this.online;\n    }\n\n    if (typeof navigator === 'undefined' || typeof navigator.onLine === 'undefined') {\n      return true;\n    }\n\n    return navigator.onLine;\n  };\n\n  return OnlineManager;\n}(_subscribable__WEBPACK_IMPORTED_MODULE_2__.Subscribable);\nvar onlineManager = new OnlineManager();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/onlineManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/queriesObserver.js":
/*!*************************************************************!*\
  !*** ./node_modules/react-query/es/core/queriesObserver.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueriesObserver: () => (/* binding */ QueriesObserver)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _queryObserver__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./queryObserver */ \"(ssr)/./node_modules/react-query/es/core/queryObserver.js\");\n/* harmony import */ var _subscribable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./subscribable */ \"(ssr)/./node_modules/react-query/es/core/subscribable.js\");\n\n\n\n\n\nvar QueriesObserver = /*#__PURE__*/function (_Subscribable) {\n  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(QueriesObserver, _Subscribable);\n\n  function QueriesObserver(client, queries) {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n    _this.client = client;\n    _this.queries = [];\n    _this.result = [];\n    _this.observers = [];\n    _this.observersMap = {};\n\n    if (queries) {\n      _this.setQueries(queries);\n    }\n\n    return _this;\n  }\n\n  var _proto = QueriesObserver.prototype;\n\n  _proto.onSubscribe = function onSubscribe() {\n    var _this2 = this;\n\n    if (this.listeners.length === 1) {\n      this.observers.forEach(function (observer) {\n        observer.subscribe(function (result) {\n          _this2.onUpdate(observer, result);\n        });\n      });\n    }\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.listeners.length) {\n      this.destroy();\n    }\n  };\n\n  _proto.destroy = function destroy() {\n    this.listeners = [];\n    this.observers.forEach(function (observer) {\n      observer.destroy();\n    });\n  };\n\n  _proto.setQueries = function setQueries(queries, notifyOptions) {\n    this.queries = queries;\n    this.updateObservers(notifyOptions);\n  };\n\n  _proto.getCurrentResult = function getCurrentResult() {\n    return this.result;\n  };\n\n  _proto.getOptimisticResult = function getOptimisticResult(queries) {\n    return this.findMatchingObservers(queries).map(function (match) {\n      return match.observer.getOptimisticResult(match.defaultedQueryOptions);\n    });\n  };\n\n  _proto.findMatchingObservers = function findMatchingObservers(queries) {\n    var _this3 = this;\n\n    var prevObservers = this.observers;\n    var defaultedQueryOptions = queries.map(function (options) {\n      return _this3.client.defaultQueryObserverOptions(options);\n    });\n    var matchingObservers = defaultedQueryOptions.flatMap(function (defaultedOptions) {\n      var match = prevObservers.find(function (observer) {\n        return observer.options.queryHash === defaultedOptions.queryHash;\n      });\n\n      if (match != null) {\n        return [{\n          defaultedQueryOptions: defaultedOptions,\n          observer: match\n        }];\n      }\n\n      return [];\n    });\n    var matchedQueryHashes = matchingObservers.map(function (match) {\n      return match.defaultedQueryOptions.queryHash;\n    });\n    var unmatchedQueries = defaultedQueryOptions.filter(function (defaultedOptions) {\n      return !matchedQueryHashes.includes(defaultedOptions.queryHash);\n    });\n    var unmatchedObservers = prevObservers.filter(function (prevObserver) {\n      return !matchingObservers.some(function (match) {\n        return match.observer === prevObserver;\n      });\n    });\n    var newOrReusedObservers = unmatchedQueries.map(function (options, index) {\n      if (options.keepPreviousData) {\n        // return previous data from one of the observers that no longer match\n        var previouslyUsedObserver = unmatchedObservers[index];\n\n        if (previouslyUsedObserver !== undefined) {\n          return {\n            defaultedQueryOptions: options,\n            observer: previouslyUsedObserver\n          };\n        }\n      }\n\n      return {\n        defaultedQueryOptions: options,\n        observer: _this3.getObserver(options)\n      };\n    });\n\n    var sortMatchesByOrderOfQueries = function sortMatchesByOrderOfQueries(a, b) {\n      return defaultedQueryOptions.indexOf(a.defaultedQueryOptions) - defaultedQueryOptions.indexOf(b.defaultedQueryOptions);\n    };\n\n    return matchingObservers.concat(newOrReusedObservers).sort(sortMatchesByOrderOfQueries);\n  };\n\n  _proto.getObserver = function getObserver(options) {\n    var defaultedOptions = this.client.defaultQueryObserverOptions(options);\n    var currentObserver = this.observersMap[defaultedOptions.queryHash];\n    return currentObserver != null ? currentObserver : new _queryObserver__WEBPACK_IMPORTED_MODULE_1__.QueryObserver(this.client, defaultedOptions);\n  };\n\n  _proto.updateObservers = function updateObservers(notifyOptions) {\n    var _this4 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(function () {\n      var prevObservers = _this4.observers;\n\n      var newObserverMatches = _this4.findMatchingObservers(_this4.queries); // set options for the new observers to notify of changes\n\n\n      newObserverMatches.forEach(function (match) {\n        return match.observer.setOptions(match.defaultedQueryOptions, notifyOptions);\n      });\n      var newObservers = newObserverMatches.map(function (match) {\n        return match.observer;\n      });\n      var newObserversMap = Object.fromEntries(newObservers.map(function (observer) {\n        return [observer.options.queryHash, observer];\n      }));\n      var newResult = newObservers.map(function (observer) {\n        return observer.getCurrentResult();\n      });\n      var hasIndexChange = newObservers.some(function (observer, index) {\n        return observer !== prevObservers[index];\n      });\n\n      if (prevObservers.length === newObservers.length && !hasIndexChange) {\n        return;\n      }\n\n      _this4.observers = newObservers;\n      _this4.observersMap = newObserversMap;\n      _this4.result = newResult;\n\n      if (!_this4.hasListeners()) {\n        return;\n      }\n\n      (0,_utils__WEBPACK_IMPORTED_MODULE_3__.difference)(prevObservers, newObservers).forEach(function (observer) {\n        observer.destroy();\n      });\n      (0,_utils__WEBPACK_IMPORTED_MODULE_3__.difference)(newObservers, prevObservers).forEach(function (observer) {\n        observer.subscribe(function (result) {\n          _this4.onUpdate(observer, result);\n        });\n      });\n\n      _this4.notify();\n    });\n  };\n\n  _proto.onUpdate = function onUpdate(observer, result) {\n    var index = this.observers.indexOf(observer);\n\n    if (index !== -1) {\n      this.result = (0,_utils__WEBPACK_IMPORTED_MODULE_3__.replaceAt)(this.result, index, result);\n      this.notify();\n    }\n  };\n\n  _proto.notify = function notify() {\n    var _this5 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(function () {\n      _this5.listeners.forEach(function (listener) {\n        listener(_this5.result);\n      });\n    });\n  };\n\n  return QueriesObserver;\n}(_subscribable__WEBPACK_IMPORTED_MODULE_4__.Subscribable);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/queriesObserver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/query.js":
/*!***************************************************!*\
  !*** ./node_modules/react-query/es/core/query.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Query: () => (/* binding */ Query)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./logger */ \"(ssr)/./node_modules/react-query/es/core/logger.js\");\n/* harmony import */ var _retryer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./retryer */ \"(ssr)/./node_modules/react-query/es/core/retryer.js\");\n\n\n\n\n // TYPES\n\n// CLASS\nvar Query = /*#__PURE__*/function () {\n  function Query(config) {\n    this.abortSignalConsumed = false;\n    this.hadObservers = false;\n    this.defaultOptions = config.defaultOptions;\n    this.setOptions(config.options);\n    this.observers = [];\n    this.cache = config.cache;\n    this.queryKey = config.queryKey;\n    this.queryHash = config.queryHash;\n    this.initialState = config.state || this.getDefaultState(this.options);\n    this.state = this.initialState;\n    this.meta = config.meta;\n    this.scheduleGc();\n  }\n\n  var _proto = Query.prototype;\n\n  _proto.setOptions = function setOptions(options) {\n    var _this$options$cacheTi;\n\n    this.options = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, this.defaultOptions, options);\n    this.meta = options == null ? void 0 : options.meta; // Default to 5 minutes if not cache time is set\n\n    this.cacheTime = Math.max(this.cacheTime || 0, (_this$options$cacheTi = this.options.cacheTime) != null ? _this$options$cacheTi : 5 * 60 * 1000);\n  };\n\n  _proto.setDefaultOptions = function setDefaultOptions(options) {\n    this.defaultOptions = options;\n  };\n\n  _proto.scheduleGc = function scheduleGc() {\n    var _this = this;\n\n    this.clearGcTimeout();\n\n    if ((0,_utils__WEBPACK_IMPORTED_MODULE_1__.isValidTimeout)(this.cacheTime)) {\n      this.gcTimeout = setTimeout(function () {\n        _this.optionalRemove();\n      }, this.cacheTime);\n    }\n  };\n\n  _proto.clearGcTimeout = function clearGcTimeout() {\n    if (this.gcTimeout) {\n      clearTimeout(this.gcTimeout);\n      this.gcTimeout = undefined;\n    }\n  };\n\n  _proto.optionalRemove = function optionalRemove() {\n    if (!this.observers.length) {\n      if (this.state.isFetching) {\n        if (this.hadObservers) {\n          this.scheduleGc();\n        }\n      } else {\n        this.cache.remove(this);\n      }\n    }\n  };\n\n  _proto.setData = function setData(updater, options) {\n    var _this$options$isDataE, _this$options;\n\n    var prevData = this.state.data; // Get the new data\n\n    var data = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.functionalUpdate)(updater, prevData); // Use prev data if an isDataEqual function is defined and returns `true`\n\n    if ((_this$options$isDataE = (_this$options = this.options).isDataEqual) == null ? void 0 : _this$options$isDataE.call(_this$options, prevData, data)) {\n      data = prevData;\n    } else if (this.options.structuralSharing !== false) {\n      // Structurally share data between prev and new data if needed\n      data = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.replaceEqualDeep)(prevData, data);\n    } // Set data and mark it as cached\n\n\n    this.dispatch({\n      data: data,\n      type: 'success',\n      dataUpdatedAt: options == null ? void 0 : options.updatedAt\n    });\n    return data;\n  };\n\n  _proto.setState = function setState(state, setStateOptions) {\n    this.dispatch({\n      type: 'setState',\n      state: state,\n      setStateOptions: setStateOptions\n    });\n  };\n\n  _proto.cancel = function cancel(options) {\n    var _this$retryer;\n\n    var promise = this.promise;\n    (_this$retryer = this.retryer) == null ? void 0 : _this$retryer.cancel(options);\n    return promise ? promise.then(_utils__WEBPACK_IMPORTED_MODULE_1__.noop).catch(_utils__WEBPACK_IMPORTED_MODULE_1__.noop) : Promise.resolve();\n  };\n\n  _proto.destroy = function destroy() {\n    this.clearGcTimeout();\n    this.cancel({\n      silent: true\n    });\n  };\n\n  _proto.reset = function reset() {\n    this.destroy();\n    this.setState(this.initialState);\n  };\n\n  _proto.isActive = function isActive() {\n    return this.observers.some(function (observer) {\n      return observer.options.enabled !== false;\n    });\n  };\n\n  _proto.isFetching = function isFetching() {\n    return this.state.isFetching;\n  };\n\n  _proto.isStale = function isStale() {\n    return this.state.isInvalidated || !this.state.dataUpdatedAt || this.observers.some(function (observer) {\n      return observer.getCurrentResult().isStale;\n    });\n  };\n\n  _proto.isStaleByTime = function isStaleByTime(staleTime) {\n    if (staleTime === void 0) {\n      staleTime = 0;\n    }\n\n    return this.state.isInvalidated || !this.state.dataUpdatedAt || !(0,_utils__WEBPACK_IMPORTED_MODULE_1__.timeUntilStale)(this.state.dataUpdatedAt, staleTime);\n  };\n\n  _proto.onFocus = function onFocus() {\n    var _this$retryer2;\n\n    var observer = this.observers.find(function (x) {\n      return x.shouldFetchOnWindowFocus();\n    });\n\n    if (observer) {\n      observer.refetch();\n    } // Continue fetch if currently paused\n\n\n    (_this$retryer2 = this.retryer) == null ? void 0 : _this$retryer2.continue();\n  };\n\n  _proto.onOnline = function onOnline() {\n    var _this$retryer3;\n\n    var observer = this.observers.find(function (x) {\n      return x.shouldFetchOnReconnect();\n    });\n\n    if (observer) {\n      observer.refetch();\n    } // Continue fetch if currently paused\n\n\n    (_this$retryer3 = this.retryer) == null ? void 0 : _this$retryer3.continue();\n  };\n\n  _proto.addObserver = function addObserver(observer) {\n    if (this.observers.indexOf(observer) === -1) {\n      this.observers.push(observer);\n      this.hadObservers = true; // Stop the query from being garbage collected\n\n      this.clearGcTimeout();\n      this.cache.notify({\n        type: 'observerAdded',\n        query: this,\n        observer: observer\n      });\n    }\n  };\n\n  _proto.removeObserver = function removeObserver(observer) {\n    if (this.observers.indexOf(observer) !== -1) {\n      this.observers = this.observers.filter(function (x) {\n        return x !== observer;\n      });\n\n      if (!this.observers.length) {\n        // If the transport layer does not support cancellation\n        // we'll let the query continue so the result can be cached\n        if (this.retryer) {\n          if (this.retryer.isTransportCancelable || this.abortSignalConsumed) {\n            this.retryer.cancel({\n              revert: true\n            });\n          } else {\n            this.retryer.cancelRetry();\n          }\n        }\n\n        if (this.cacheTime) {\n          this.scheduleGc();\n        } else {\n          this.cache.remove(this);\n        }\n      }\n\n      this.cache.notify({\n        type: 'observerRemoved',\n        query: this,\n        observer: observer\n      });\n    }\n  };\n\n  _proto.getObserversCount = function getObserversCount() {\n    return this.observers.length;\n  };\n\n  _proto.invalidate = function invalidate() {\n    if (!this.state.isInvalidated) {\n      this.dispatch({\n        type: 'invalidate'\n      });\n    }\n  };\n\n  _proto.fetch = function fetch(options, fetchOptions) {\n    var _this2 = this,\n        _this$options$behavio,\n        _context$fetchOptions,\n        _abortController$abor;\n\n    if (this.state.isFetching) {\n      if (this.state.dataUpdatedAt && (fetchOptions == null ? void 0 : fetchOptions.cancelRefetch)) {\n        // Silently cancel current fetch if the user wants to cancel refetches\n        this.cancel({\n          silent: true\n        });\n      } else if (this.promise) {\n        var _this$retryer4;\n\n        // make sure that retries that were potentially cancelled due to unmounts can continue\n        (_this$retryer4 = this.retryer) == null ? void 0 : _this$retryer4.continueRetry(); // Return current promise if we are already fetching\n\n        return this.promise;\n      }\n    } // Update config if passed, otherwise the config from the last execution is used\n\n\n    if (options) {\n      this.setOptions(options);\n    } // Use the options from the first observer with a query function if no function is found.\n    // This can happen when the query is hydrated or created with setQueryData.\n\n\n    if (!this.options.queryFn) {\n      var observer = this.observers.find(function (x) {\n        return x.options.queryFn;\n      });\n\n      if (observer) {\n        this.setOptions(observer.options);\n      }\n    }\n\n    var queryKey = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.ensureQueryKeyArray)(this.queryKey);\n    var abortController = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getAbortController)(); // Create query function context\n\n    var queryFnContext = {\n      queryKey: queryKey,\n      pageParam: undefined,\n      meta: this.meta\n    };\n    Object.defineProperty(queryFnContext, 'signal', {\n      enumerable: true,\n      get: function get() {\n        if (abortController) {\n          _this2.abortSignalConsumed = true;\n          return abortController.signal;\n        }\n\n        return undefined;\n      }\n    }); // Create fetch function\n\n    var fetchFn = function fetchFn() {\n      if (!_this2.options.queryFn) {\n        return Promise.reject('Missing queryFn');\n      }\n\n      _this2.abortSignalConsumed = false;\n      return _this2.options.queryFn(queryFnContext);\n    }; // Trigger behavior hook\n\n\n    var context = {\n      fetchOptions: fetchOptions,\n      options: this.options,\n      queryKey: queryKey,\n      state: this.state,\n      fetchFn: fetchFn,\n      meta: this.meta\n    };\n\n    if ((_this$options$behavio = this.options.behavior) == null ? void 0 : _this$options$behavio.onFetch) {\n      var _this$options$behavio2;\n\n      (_this$options$behavio2 = this.options.behavior) == null ? void 0 : _this$options$behavio2.onFetch(context);\n    } // Store state in case the current fetch needs to be reverted\n\n\n    this.revertState = this.state; // Set to fetching state if not already in it\n\n    if (!this.state.isFetching || this.state.fetchMeta !== ((_context$fetchOptions = context.fetchOptions) == null ? void 0 : _context$fetchOptions.meta)) {\n      var _context$fetchOptions2;\n\n      this.dispatch({\n        type: 'fetch',\n        meta: (_context$fetchOptions2 = context.fetchOptions) == null ? void 0 : _context$fetchOptions2.meta\n      });\n    } // Try to fetch the data\n\n\n    this.retryer = new _retryer__WEBPACK_IMPORTED_MODULE_2__.Retryer({\n      fn: context.fetchFn,\n      abort: abortController == null ? void 0 : (_abortController$abor = abortController.abort) == null ? void 0 : _abortController$abor.bind(abortController),\n      onSuccess: function onSuccess(data) {\n        _this2.setData(data); // Notify cache callback\n\n\n        _this2.cache.config.onSuccess == null ? void 0 : _this2.cache.config.onSuccess(data, _this2); // Remove query after fetching if cache time is 0\n\n        if (_this2.cacheTime === 0) {\n          _this2.optionalRemove();\n        }\n      },\n      onError: function onError(error) {\n        // Optimistically update state if needed\n        if (!((0,_retryer__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error) && error.silent)) {\n          _this2.dispatch({\n            type: 'error',\n            error: error\n          });\n        }\n\n        if (!(0,_retryer__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error)) {\n          // Notify cache callback\n          _this2.cache.config.onError == null ? void 0 : _this2.cache.config.onError(error, _this2); // Log error\n\n          (0,_logger__WEBPACK_IMPORTED_MODULE_3__.getLogger)().error(error);\n        } // Remove query after fetching if cache time is 0\n\n\n        if (_this2.cacheTime === 0) {\n          _this2.optionalRemove();\n        }\n      },\n      onFail: function onFail() {\n        _this2.dispatch({\n          type: 'failed'\n        });\n      },\n      onPause: function onPause() {\n        _this2.dispatch({\n          type: 'pause'\n        });\n      },\n      onContinue: function onContinue() {\n        _this2.dispatch({\n          type: 'continue'\n        });\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay\n    });\n    this.promise = this.retryer.promise;\n    return this.promise;\n  };\n\n  _proto.dispatch = function dispatch(action) {\n    var _this3 = this;\n\n    this.state = this.reducer(this.state, action);\n    _notifyManager__WEBPACK_IMPORTED_MODULE_4__.notifyManager.batch(function () {\n      _this3.observers.forEach(function (observer) {\n        observer.onQueryUpdate(action);\n      });\n\n      _this3.cache.notify({\n        query: _this3,\n        type: 'queryUpdated',\n        action: action\n      });\n    });\n  };\n\n  _proto.getDefaultState = function getDefaultState(options) {\n    var data = typeof options.initialData === 'function' ? options.initialData() : options.initialData;\n    var hasInitialData = typeof options.initialData !== 'undefined';\n    var initialDataUpdatedAt = hasInitialData ? typeof options.initialDataUpdatedAt === 'function' ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n    var hasData = typeof data !== 'undefined';\n    return {\n      data: data,\n      dataUpdateCount: 0,\n      dataUpdatedAt: hasData ? initialDataUpdatedAt != null ? initialDataUpdatedAt : Date.now() : 0,\n      error: null,\n      errorUpdateCount: 0,\n      errorUpdatedAt: 0,\n      fetchFailureCount: 0,\n      fetchMeta: null,\n      isFetching: false,\n      isInvalidated: false,\n      isPaused: false,\n      status: hasData ? 'success' : 'idle'\n    };\n  };\n\n  _proto.reducer = function reducer(state, action) {\n    var _action$meta, _action$dataUpdatedAt;\n\n    switch (action.type) {\n      case 'failed':\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n          fetchFailureCount: state.fetchFailureCount + 1\n        });\n\n      case 'pause':\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n          isPaused: true\n        });\n\n      case 'continue':\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n          isPaused: false\n        });\n\n      case 'fetch':\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n          fetchFailureCount: 0,\n          fetchMeta: (_action$meta = action.meta) != null ? _action$meta : null,\n          isFetching: true,\n          isPaused: false\n        }, !state.dataUpdatedAt && {\n          error: null,\n          status: 'loading'\n        });\n\n      case 'success':\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n          data: action.data,\n          dataUpdateCount: state.dataUpdateCount + 1,\n          dataUpdatedAt: (_action$dataUpdatedAt = action.dataUpdatedAt) != null ? _action$dataUpdatedAt : Date.now(),\n          error: null,\n          fetchFailureCount: 0,\n          isFetching: false,\n          isInvalidated: false,\n          isPaused: false,\n          status: 'success'\n        });\n\n      case 'error':\n        var error = action.error;\n\n        if ((0,_retryer__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error) && error.revert && this.revertState) {\n          return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, this.revertState);\n        }\n\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n          error: error,\n          errorUpdateCount: state.errorUpdateCount + 1,\n          errorUpdatedAt: Date.now(),\n          fetchFailureCount: state.fetchFailureCount + 1,\n          isFetching: false,\n          isPaused: false,\n          status: 'error'\n        });\n\n      case 'invalidate':\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, {\n          isInvalidated: true\n        });\n\n      case 'setState':\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, state, action.state);\n\n      default:\n        return state;\n    }\n  };\n\n  return Query;\n}();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/query.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/queryCache.js":
/*!********************************************************!*\
  !*** ./node_modules/react-query/es/core/queryCache.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryCache: () => (/* binding */ QueryCache)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./query */ \"(ssr)/./node_modules/react-query/es/core/query.js\");\n/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _subscribable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./subscribable */ \"(ssr)/./node_modules/react-query/es/core/subscribable.js\");\n\n\n\n\n\n// CLASS\nvar QueryCache = /*#__PURE__*/function (_Subscribable) {\n  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(QueryCache, _Subscribable);\n\n  function QueryCache(config) {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n    _this.config = config || {};\n    _this.queries = [];\n    _this.queriesMap = {};\n    return _this;\n  }\n\n  var _proto = QueryCache.prototype;\n\n  _proto.build = function build(client, options, state) {\n    var _options$queryHash;\n\n    var queryKey = options.queryKey;\n    var queryHash = (_options$queryHash = options.queryHash) != null ? _options$queryHash : (0,_utils__WEBPACK_IMPORTED_MODULE_1__.hashQueryKeyByOptions)(queryKey, options);\n    var query = this.get(queryHash);\n\n    if (!query) {\n      query = new _query__WEBPACK_IMPORTED_MODULE_2__.Query({\n        cache: this,\n        queryKey: queryKey,\n        queryHash: queryHash,\n        options: client.defaultQueryOptions(options),\n        state: state,\n        defaultOptions: client.getQueryDefaults(queryKey),\n        meta: options.meta\n      });\n      this.add(query);\n    }\n\n    return query;\n  };\n\n  _proto.add = function add(query) {\n    if (!this.queriesMap[query.queryHash]) {\n      this.queriesMap[query.queryHash] = query;\n      this.queries.push(query);\n      this.notify({\n        type: 'queryAdded',\n        query: query\n      });\n    }\n  };\n\n  _proto.remove = function remove(query) {\n    var queryInMap = this.queriesMap[query.queryHash];\n\n    if (queryInMap) {\n      query.destroy();\n      this.queries = this.queries.filter(function (x) {\n        return x !== query;\n      });\n\n      if (queryInMap === query) {\n        delete this.queriesMap[query.queryHash];\n      }\n\n      this.notify({\n        type: 'queryRemoved',\n        query: query\n      });\n    }\n  };\n\n  _proto.clear = function clear() {\n    var _this2 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(function () {\n      _this2.queries.forEach(function (query) {\n        _this2.remove(query);\n      });\n    });\n  };\n\n  _proto.get = function get(queryHash) {\n    return this.queriesMap[queryHash];\n  };\n\n  _proto.getAll = function getAll() {\n    return this.queries;\n  };\n\n  _proto.find = function find(arg1, arg2) {\n    var _parseFilterArgs = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.parseFilterArgs)(arg1, arg2),\n        filters = _parseFilterArgs[0];\n\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true;\n    }\n\n    return this.queries.find(function (query) {\n      return (0,_utils__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(filters, query);\n    });\n  };\n\n  _proto.findAll = function findAll(arg1, arg2) {\n    var _parseFilterArgs2 = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.parseFilterArgs)(arg1, arg2),\n        filters = _parseFilterArgs2[0];\n\n    return Object.keys(filters).length > 0 ? this.queries.filter(function (query) {\n      return (0,_utils__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(filters, query);\n    }) : this.queries;\n  };\n\n  _proto.notify = function notify(event) {\n    var _this3 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(function () {\n      _this3.listeners.forEach(function (listener) {\n        listener(event);\n      });\n    });\n  };\n\n  _proto.onFocus = function onFocus() {\n    var _this4 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(function () {\n      _this4.queries.forEach(function (query) {\n        query.onFocus();\n      });\n    });\n  };\n\n  _proto.onOnline = function onOnline() {\n    var _this5 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(function () {\n      _this5.queries.forEach(function (query) {\n        query.onOnline();\n      });\n    });\n  };\n\n  return QueryCache;\n}(_subscribable__WEBPACK_IMPORTED_MODULE_4__.Subscribable);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/queryCache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/queryClient.js":
/*!*********************************************************!*\
  !*** ./node_modules/react-query/es/core/queryClient.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClient: () => (/* binding */ QueryClient)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _queryCache__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./queryCache */ \"(ssr)/./node_modules/react-query/es/core/queryCache.js\");\n/* harmony import */ var _mutationCache__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mutationCache */ \"(ssr)/./node_modules/react-query/es/core/mutationCache.js\");\n/* harmony import */ var _focusManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./focusManager */ \"(ssr)/./node_modules/react-query/es/core/focusManager.js\");\n/* harmony import */ var _onlineManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./onlineManager */ \"(ssr)/./node_modules/react-query/es/core/onlineManager.js\");\n/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _infiniteQueryBehavior__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./infiniteQueryBehavior */ \"(ssr)/./node_modules/react-query/es/core/infiniteQueryBehavior.js\");\n\n\n\n\n\n\n\n\n// CLASS\nvar QueryClient = /*#__PURE__*/function () {\n  function QueryClient(config) {\n    if (config === void 0) {\n      config = {};\n    }\n\n    this.queryCache = config.queryCache || new _queryCache__WEBPACK_IMPORTED_MODULE_1__.QueryCache();\n    this.mutationCache = config.mutationCache || new _mutationCache__WEBPACK_IMPORTED_MODULE_2__.MutationCache();\n    this.defaultOptions = config.defaultOptions || {};\n    this.queryDefaults = [];\n    this.mutationDefaults = [];\n  }\n\n  var _proto = QueryClient.prototype;\n\n  _proto.mount = function mount() {\n    var _this = this;\n\n    this.unsubscribeFocus = _focusManager__WEBPACK_IMPORTED_MODULE_3__.focusManager.subscribe(function () {\n      if (_focusManager__WEBPACK_IMPORTED_MODULE_3__.focusManager.isFocused() && _onlineManager__WEBPACK_IMPORTED_MODULE_4__.onlineManager.isOnline()) {\n        _this.mutationCache.onFocus();\n\n        _this.queryCache.onFocus();\n      }\n    });\n    this.unsubscribeOnline = _onlineManager__WEBPACK_IMPORTED_MODULE_4__.onlineManager.subscribe(function () {\n      if (_focusManager__WEBPACK_IMPORTED_MODULE_3__.focusManager.isFocused() && _onlineManager__WEBPACK_IMPORTED_MODULE_4__.onlineManager.isOnline()) {\n        _this.mutationCache.onOnline();\n\n        _this.queryCache.onOnline();\n      }\n    });\n  };\n\n  _proto.unmount = function unmount() {\n    var _this$unsubscribeFocu, _this$unsubscribeOnli;\n\n    (_this$unsubscribeFocu = this.unsubscribeFocus) == null ? void 0 : _this$unsubscribeFocu.call(this);\n    (_this$unsubscribeOnli = this.unsubscribeOnline) == null ? void 0 : _this$unsubscribeOnli.call(this);\n  };\n\n  _proto.isFetching = function isFetching(arg1, arg2) {\n    var _parseFilterArgs = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2),\n        filters = _parseFilterArgs[0];\n\n    filters.fetching = true;\n    return this.queryCache.findAll(filters).length;\n  };\n\n  _proto.isMutating = function isMutating(filters) {\n    return this.mutationCache.findAll((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, filters, {\n      fetching: true\n    })).length;\n  };\n\n  _proto.getQueryData = function getQueryData(queryKey, filters) {\n    var _this$queryCache$find;\n\n    return (_this$queryCache$find = this.queryCache.find(queryKey, filters)) == null ? void 0 : _this$queryCache$find.state.data;\n  };\n\n  _proto.getQueriesData = function getQueriesData(queryKeyOrFilters) {\n    return this.getQueryCache().findAll(queryKeyOrFilters).map(function (_ref) {\n      var queryKey = _ref.queryKey,\n          state = _ref.state;\n      var data = state.data;\n      return [queryKey, data];\n    });\n  };\n\n  _proto.setQueryData = function setQueryData(queryKey, updater, options) {\n    var parsedOptions = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseQueryArgs)(queryKey);\n    var defaultedOptions = this.defaultQueryOptions(parsedOptions);\n    return this.queryCache.build(this, defaultedOptions).setData(updater, options);\n  };\n\n  _proto.setQueriesData = function setQueriesData(queryKeyOrFilters, updater, options) {\n    var _this2 = this;\n\n    return _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {\n      return _this2.getQueryCache().findAll(queryKeyOrFilters).map(function (_ref2) {\n        var queryKey = _ref2.queryKey;\n        return [queryKey, _this2.setQueryData(queryKey, updater, options)];\n      });\n    });\n  };\n\n  _proto.getQueryState = function getQueryState(queryKey, filters) {\n    var _this$queryCache$find2;\n\n    return (_this$queryCache$find2 = this.queryCache.find(queryKey, filters)) == null ? void 0 : _this$queryCache$find2.state;\n  };\n\n  _proto.removeQueries = function removeQueries(arg1, arg2) {\n    var _parseFilterArgs2 = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2),\n        filters = _parseFilterArgs2[0];\n\n    var queryCache = this.queryCache;\n    _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {\n      queryCache.findAll(filters).forEach(function (query) {\n        queryCache.remove(query);\n      });\n    });\n  };\n\n  _proto.resetQueries = function resetQueries(arg1, arg2, arg3) {\n    var _this3 = this;\n\n    var _parseFilterArgs3 = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2, arg3),\n        filters = _parseFilterArgs3[0],\n        options = _parseFilterArgs3[1];\n\n    var queryCache = this.queryCache;\n\n    var refetchFilters = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, filters, {\n      active: true\n    });\n\n    return _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {\n      queryCache.findAll(filters).forEach(function (query) {\n        query.reset();\n      });\n      return _this3.refetchQueries(refetchFilters, options);\n    });\n  };\n\n  _proto.cancelQueries = function cancelQueries(arg1, arg2, arg3) {\n    var _this4 = this;\n\n    var _parseFilterArgs4 = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2, arg3),\n        filters = _parseFilterArgs4[0],\n        _parseFilterArgs4$ = _parseFilterArgs4[1],\n        cancelOptions = _parseFilterArgs4$ === void 0 ? {} : _parseFilterArgs4$;\n\n    if (typeof cancelOptions.revert === 'undefined') {\n      cancelOptions.revert = true;\n    }\n\n    var promises = _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {\n      return _this4.queryCache.findAll(filters).map(function (query) {\n        return query.cancel(cancelOptions);\n      });\n    });\n    return Promise.all(promises).then(_utils__WEBPACK_IMPORTED_MODULE_5__.noop).catch(_utils__WEBPACK_IMPORTED_MODULE_5__.noop);\n  };\n\n  _proto.invalidateQueries = function invalidateQueries(arg1, arg2, arg3) {\n    var _ref3,\n        _filters$refetchActiv,\n        _filters$refetchInact,\n        _this5 = this;\n\n    var _parseFilterArgs5 = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2, arg3),\n        filters = _parseFilterArgs5[0],\n        options = _parseFilterArgs5[1];\n\n    var refetchFilters = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, filters, {\n      // if filters.refetchActive is not provided and filters.active is explicitly false,\n      // e.g. invalidateQueries({ active: false }), we don't want to refetch active queries\n      active: (_ref3 = (_filters$refetchActiv = filters.refetchActive) != null ? _filters$refetchActiv : filters.active) != null ? _ref3 : true,\n      inactive: (_filters$refetchInact = filters.refetchInactive) != null ? _filters$refetchInact : false\n    });\n\n    return _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {\n      _this5.queryCache.findAll(filters).forEach(function (query) {\n        query.invalidate();\n      });\n\n      return _this5.refetchQueries(refetchFilters, options);\n    });\n  };\n\n  _proto.refetchQueries = function refetchQueries(arg1, arg2, arg3) {\n    var _this6 = this;\n\n    var _parseFilterArgs6 = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2, arg3),\n        filters = _parseFilterArgs6[0],\n        options = _parseFilterArgs6[1];\n\n    var promises = _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {\n      return _this6.queryCache.findAll(filters).map(function (query) {\n        return query.fetch(undefined, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, options, {\n          meta: {\n            refetchPage: filters == null ? void 0 : filters.refetchPage\n          }\n        }));\n      });\n    });\n    var promise = Promise.all(promises).then(_utils__WEBPACK_IMPORTED_MODULE_5__.noop);\n\n    if (!(options == null ? void 0 : options.throwOnError)) {\n      promise = promise.catch(_utils__WEBPACK_IMPORTED_MODULE_5__.noop);\n    }\n\n    return promise;\n  };\n\n  _proto.fetchQuery = function fetchQuery(arg1, arg2, arg3) {\n    var parsedOptions = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseQueryArgs)(arg1, arg2, arg3);\n    var defaultedOptions = this.defaultQueryOptions(parsedOptions); // https://github.com/tannerlinsley/react-query/issues/652\n\n    if (typeof defaultedOptions.retry === 'undefined') {\n      defaultedOptions.retry = false;\n    }\n\n    var query = this.queryCache.build(this, defaultedOptions);\n    return query.isStaleByTime(defaultedOptions.staleTime) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n  };\n\n  _proto.prefetchQuery = function prefetchQuery(arg1, arg2, arg3) {\n    return this.fetchQuery(arg1, arg2, arg3).then(_utils__WEBPACK_IMPORTED_MODULE_5__.noop).catch(_utils__WEBPACK_IMPORTED_MODULE_5__.noop);\n  };\n\n  _proto.fetchInfiniteQuery = function fetchInfiniteQuery(arg1, arg2, arg3) {\n    var parsedOptions = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseQueryArgs)(arg1, arg2, arg3);\n    parsedOptions.behavior = (0,_infiniteQueryBehavior__WEBPACK_IMPORTED_MODULE_7__.infiniteQueryBehavior)();\n    return this.fetchQuery(parsedOptions);\n  };\n\n  _proto.prefetchInfiniteQuery = function prefetchInfiniteQuery(arg1, arg2, arg3) {\n    return this.fetchInfiniteQuery(arg1, arg2, arg3).then(_utils__WEBPACK_IMPORTED_MODULE_5__.noop).catch(_utils__WEBPACK_IMPORTED_MODULE_5__.noop);\n  };\n\n  _proto.cancelMutations = function cancelMutations() {\n    var _this7 = this;\n\n    var promises = _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {\n      return _this7.mutationCache.getAll().map(function (mutation) {\n        return mutation.cancel();\n      });\n    });\n    return Promise.all(promises).then(_utils__WEBPACK_IMPORTED_MODULE_5__.noop).catch(_utils__WEBPACK_IMPORTED_MODULE_5__.noop);\n  };\n\n  _proto.resumePausedMutations = function resumePausedMutations() {\n    return this.getMutationCache().resumePausedMutations();\n  };\n\n  _proto.executeMutation = function executeMutation(options) {\n    return this.mutationCache.build(this, options).execute();\n  };\n\n  _proto.getQueryCache = function getQueryCache() {\n    return this.queryCache;\n  };\n\n  _proto.getMutationCache = function getMutationCache() {\n    return this.mutationCache;\n  };\n\n  _proto.getDefaultOptions = function getDefaultOptions() {\n    return this.defaultOptions;\n  };\n\n  _proto.setDefaultOptions = function setDefaultOptions(options) {\n    this.defaultOptions = options;\n  };\n\n  _proto.setQueryDefaults = function setQueryDefaults(queryKey, options) {\n    var result = this.queryDefaults.find(function (x) {\n      return (0,_utils__WEBPACK_IMPORTED_MODULE_5__.hashQueryKey)(queryKey) === (0,_utils__WEBPACK_IMPORTED_MODULE_5__.hashQueryKey)(x.queryKey);\n    });\n\n    if (result) {\n      result.defaultOptions = options;\n    } else {\n      this.queryDefaults.push({\n        queryKey: queryKey,\n        defaultOptions: options\n      });\n    }\n  };\n\n  _proto.getQueryDefaults = function getQueryDefaults(queryKey) {\n    var _this$queryDefaults$f;\n\n    return queryKey ? (_this$queryDefaults$f = this.queryDefaults.find(function (x) {\n      return (0,_utils__WEBPACK_IMPORTED_MODULE_5__.partialMatchKey)(queryKey, x.queryKey);\n    })) == null ? void 0 : _this$queryDefaults$f.defaultOptions : undefined;\n  };\n\n  _proto.setMutationDefaults = function setMutationDefaults(mutationKey, options) {\n    var result = this.mutationDefaults.find(function (x) {\n      return (0,_utils__WEBPACK_IMPORTED_MODULE_5__.hashQueryKey)(mutationKey) === (0,_utils__WEBPACK_IMPORTED_MODULE_5__.hashQueryKey)(x.mutationKey);\n    });\n\n    if (result) {\n      result.defaultOptions = options;\n    } else {\n      this.mutationDefaults.push({\n        mutationKey: mutationKey,\n        defaultOptions: options\n      });\n    }\n  };\n\n  _proto.getMutationDefaults = function getMutationDefaults(mutationKey) {\n    var _this$mutationDefault;\n\n    return mutationKey ? (_this$mutationDefault = this.mutationDefaults.find(function (x) {\n      return (0,_utils__WEBPACK_IMPORTED_MODULE_5__.partialMatchKey)(mutationKey, x.mutationKey);\n    })) == null ? void 0 : _this$mutationDefault.defaultOptions : undefined;\n  };\n\n  _proto.defaultQueryOptions = function defaultQueryOptions(options) {\n    if (options == null ? void 0 : options._defaulted) {\n      return options;\n    }\n\n    var defaultedOptions = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, this.defaultOptions.queries, this.getQueryDefaults(options == null ? void 0 : options.queryKey), options, {\n      _defaulted: true\n    });\n\n    if (!defaultedOptions.queryHash && defaultedOptions.queryKey) {\n      defaultedOptions.queryHash = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.hashQueryKeyByOptions)(defaultedOptions.queryKey, defaultedOptions);\n    }\n\n    return defaultedOptions;\n  };\n\n  _proto.defaultQueryObserverOptions = function defaultQueryObserverOptions(options) {\n    return this.defaultQueryOptions(options);\n  };\n\n  _proto.defaultMutationOptions = function defaultMutationOptions(options) {\n    if (options == null ? void 0 : options._defaulted) {\n      return options;\n    }\n\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, this.defaultOptions.mutations, this.getMutationDefaults(options == null ? void 0 : options.mutationKey), options, {\n      _defaulted: true\n    });\n  };\n\n  _proto.clear = function clear() {\n    this.queryCache.clear();\n    this.mutationCache.clear();\n  };\n\n  return QueryClient;\n}();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/queryClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/queryObserver.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-query/es/core/queryObserver.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryObserver: () => (/* binding */ QueryObserver)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _focusManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./focusManager */ \"(ssr)/./node_modules/react-query/es/core/focusManager.js\");\n/* harmony import */ var _subscribable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./subscribable */ \"(ssr)/./node_modules/react-query/es/core/subscribable.js\");\n/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./logger */ \"(ssr)/./node_modules/react-query/es/core/logger.js\");\n/* harmony import */ var _retryer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./retryer */ \"(ssr)/./node_modules/react-query/es/core/retryer.js\");\n\n\n\n\n\n\n\n\nvar QueryObserver = /*#__PURE__*/function (_Subscribable) {\n  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(QueryObserver, _Subscribable);\n\n  function QueryObserver(client, options) {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n    _this.client = client;\n    _this.options = options;\n    _this.trackedProps = [];\n    _this.selectError = null;\n\n    _this.bindMethods();\n\n    _this.setOptions(options);\n\n    return _this;\n  }\n\n  var _proto = QueryObserver.prototype;\n\n  _proto.bindMethods = function bindMethods() {\n    this.remove = this.remove.bind(this);\n    this.refetch = this.refetch.bind(this);\n  };\n\n  _proto.onSubscribe = function onSubscribe() {\n    if (this.listeners.length === 1) {\n      this.currentQuery.addObserver(this);\n\n      if (shouldFetchOnMount(this.currentQuery, this.options)) {\n        this.executeFetch();\n      }\n\n      this.updateTimers();\n    }\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.listeners.length) {\n      this.destroy();\n    }\n  };\n\n  _proto.shouldFetchOnReconnect = function shouldFetchOnReconnect() {\n    return shouldFetchOn(this.currentQuery, this.options, this.options.refetchOnReconnect);\n  };\n\n  _proto.shouldFetchOnWindowFocus = function shouldFetchOnWindowFocus() {\n    return shouldFetchOn(this.currentQuery, this.options, this.options.refetchOnWindowFocus);\n  };\n\n  _proto.destroy = function destroy() {\n    this.listeners = [];\n    this.clearTimers();\n    this.currentQuery.removeObserver(this);\n  };\n\n  _proto.setOptions = function setOptions(options, notifyOptions) {\n    var prevOptions = this.options;\n    var prevQuery = this.currentQuery;\n    this.options = this.client.defaultQueryObserverOptions(options);\n\n    if (typeof this.options.enabled !== 'undefined' && typeof this.options.enabled !== 'boolean') {\n      throw new Error('Expected enabled to be a boolean');\n    } // Keep previous query key if the user does not supply one\n\n\n    if (!this.options.queryKey) {\n      this.options.queryKey = prevOptions.queryKey;\n    }\n\n    this.updateQuery();\n    var mounted = this.hasListeners(); // Fetch if there are subscribers\n\n    if (mounted && shouldFetchOptionally(this.currentQuery, prevQuery, this.options, prevOptions)) {\n      this.executeFetch();\n    } // Update result\n\n\n    this.updateResult(notifyOptions); // Update stale interval if needed\n\n    if (mounted && (this.currentQuery !== prevQuery || this.options.enabled !== prevOptions.enabled || this.options.staleTime !== prevOptions.staleTime)) {\n      this.updateStaleTimeout();\n    }\n\n    var nextRefetchInterval = this.computeRefetchInterval(); // Update refetch interval if needed\n\n    if (mounted && (this.currentQuery !== prevQuery || this.options.enabled !== prevOptions.enabled || nextRefetchInterval !== this.currentRefetchInterval)) {\n      this.updateRefetchInterval(nextRefetchInterval);\n    }\n  };\n\n  _proto.getOptimisticResult = function getOptimisticResult(options) {\n    var defaultedOptions = this.client.defaultQueryObserverOptions(options);\n    var query = this.client.getQueryCache().build(this.client, defaultedOptions);\n    return this.createResult(query, defaultedOptions);\n  };\n\n  _proto.getCurrentResult = function getCurrentResult() {\n    return this.currentResult;\n  };\n\n  _proto.trackResult = function trackResult(result, defaultedOptions) {\n    var _this2 = this;\n\n    var trackedResult = {};\n\n    var trackProp = function trackProp(key) {\n      if (!_this2.trackedProps.includes(key)) {\n        _this2.trackedProps.push(key);\n      }\n    };\n\n    Object.keys(result).forEach(function (key) {\n      Object.defineProperty(trackedResult, key, {\n        configurable: false,\n        enumerable: true,\n        get: function get() {\n          trackProp(key);\n          return result[key];\n        }\n      });\n    });\n\n    if (defaultedOptions.useErrorBoundary || defaultedOptions.suspense) {\n      trackProp('error');\n    }\n\n    return trackedResult;\n  };\n\n  _proto.getNextResult = function getNextResult(options) {\n    var _this3 = this;\n\n    return new Promise(function (resolve, reject) {\n      var unsubscribe = _this3.subscribe(function (result) {\n        if (!result.isFetching) {\n          unsubscribe();\n\n          if (result.isError && (options == null ? void 0 : options.throwOnError)) {\n            reject(result.error);\n          } else {\n            resolve(result);\n          }\n        }\n      });\n    });\n  };\n\n  _proto.getCurrentQuery = function getCurrentQuery() {\n    return this.currentQuery;\n  };\n\n  _proto.remove = function remove() {\n    this.client.getQueryCache().remove(this.currentQuery);\n  };\n\n  _proto.refetch = function refetch(options) {\n    return this.fetch((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, options, {\n      meta: {\n        refetchPage: options == null ? void 0 : options.refetchPage\n      }\n    }));\n  };\n\n  _proto.fetchOptimistic = function fetchOptimistic(options) {\n    var _this4 = this;\n\n    var defaultedOptions = this.client.defaultQueryObserverOptions(options);\n    var query = this.client.getQueryCache().build(this.client, defaultedOptions);\n    return query.fetch().then(function () {\n      return _this4.createResult(query, defaultedOptions);\n    });\n  };\n\n  _proto.fetch = function fetch(fetchOptions) {\n    var _this5 = this;\n\n    return this.executeFetch(fetchOptions).then(function () {\n      _this5.updateResult();\n\n      return _this5.currentResult;\n    });\n  };\n\n  _proto.executeFetch = function executeFetch(fetchOptions) {\n    // Make sure we reference the latest query as the current one might have been removed\n    this.updateQuery(); // Fetch\n\n    var promise = this.currentQuery.fetch(this.options, fetchOptions);\n\n    if (!(fetchOptions == null ? void 0 : fetchOptions.throwOnError)) {\n      promise = promise.catch(_utils__WEBPACK_IMPORTED_MODULE_2__.noop);\n    }\n\n    return promise;\n  };\n\n  _proto.updateStaleTimeout = function updateStaleTimeout() {\n    var _this6 = this;\n\n    this.clearStaleTimeout();\n\n    if (_utils__WEBPACK_IMPORTED_MODULE_2__.isServer || this.currentResult.isStale || !(0,_utils__WEBPACK_IMPORTED_MODULE_2__.isValidTimeout)(this.options.staleTime)) {\n      return;\n    }\n\n    var time = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.timeUntilStale)(this.currentResult.dataUpdatedAt, this.options.staleTime); // The timeout is sometimes triggered 1 ms before the stale time expiration.\n    // To mitigate this issue we always add 1 ms to the timeout.\n\n    var timeout = time + 1;\n    this.staleTimeoutId = setTimeout(function () {\n      if (!_this6.currentResult.isStale) {\n        _this6.updateResult();\n      }\n    }, timeout);\n  };\n\n  _proto.computeRefetchInterval = function computeRefetchInterval() {\n    var _this$options$refetch;\n\n    return typeof this.options.refetchInterval === 'function' ? this.options.refetchInterval(this.currentResult.data, this.currentQuery) : (_this$options$refetch = this.options.refetchInterval) != null ? _this$options$refetch : false;\n  };\n\n  _proto.updateRefetchInterval = function updateRefetchInterval(nextInterval) {\n    var _this7 = this;\n\n    this.clearRefetchInterval();\n    this.currentRefetchInterval = nextInterval;\n\n    if (_utils__WEBPACK_IMPORTED_MODULE_2__.isServer || this.options.enabled === false || !(0,_utils__WEBPACK_IMPORTED_MODULE_2__.isValidTimeout)(this.currentRefetchInterval) || this.currentRefetchInterval === 0) {\n      return;\n    }\n\n    this.refetchIntervalId = setInterval(function () {\n      if (_this7.options.refetchIntervalInBackground || _focusManager__WEBPACK_IMPORTED_MODULE_3__.focusManager.isFocused()) {\n        _this7.executeFetch();\n      }\n    }, this.currentRefetchInterval);\n  };\n\n  _proto.updateTimers = function updateTimers() {\n    this.updateStaleTimeout();\n    this.updateRefetchInterval(this.computeRefetchInterval());\n  };\n\n  _proto.clearTimers = function clearTimers() {\n    this.clearStaleTimeout();\n    this.clearRefetchInterval();\n  };\n\n  _proto.clearStaleTimeout = function clearStaleTimeout() {\n    if (this.staleTimeoutId) {\n      clearTimeout(this.staleTimeoutId);\n      this.staleTimeoutId = undefined;\n    }\n  };\n\n  _proto.clearRefetchInterval = function clearRefetchInterval() {\n    if (this.refetchIntervalId) {\n      clearInterval(this.refetchIntervalId);\n      this.refetchIntervalId = undefined;\n    }\n  };\n\n  _proto.createResult = function createResult(query, options) {\n    var prevQuery = this.currentQuery;\n    var prevOptions = this.options;\n    var prevResult = this.currentResult;\n    var prevResultState = this.currentResultState;\n    var prevResultOptions = this.currentResultOptions;\n    var queryChange = query !== prevQuery;\n    var queryInitialState = queryChange ? query.state : this.currentQueryInitialState;\n    var prevQueryResult = queryChange ? this.currentResult : this.previousQueryResult;\n    var state = query.state;\n    var dataUpdatedAt = state.dataUpdatedAt,\n        error = state.error,\n        errorUpdatedAt = state.errorUpdatedAt,\n        isFetching = state.isFetching,\n        status = state.status;\n    var isPreviousData = false;\n    var isPlaceholderData = false;\n    var data; // Optimistically set result in fetching state if needed\n\n    if (options.optimisticResults) {\n      var mounted = this.hasListeners();\n      var fetchOnMount = !mounted && shouldFetchOnMount(query, options);\n      var fetchOptionally = mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions);\n\n      if (fetchOnMount || fetchOptionally) {\n        isFetching = true;\n\n        if (!dataUpdatedAt) {\n          status = 'loading';\n        }\n      }\n    } // Keep previous data if needed\n\n\n    if (options.keepPreviousData && !state.dataUpdateCount && (prevQueryResult == null ? void 0 : prevQueryResult.isSuccess) && status !== 'error') {\n      data = prevQueryResult.data;\n      dataUpdatedAt = prevQueryResult.dataUpdatedAt;\n      status = prevQueryResult.status;\n      isPreviousData = true;\n    } // Select data if needed\n    else if (options.select && typeof state.data !== 'undefined') {\n        // Memoize select result\n        if (prevResult && state.data === (prevResultState == null ? void 0 : prevResultState.data) && options.select === this.selectFn) {\n          data = this.selectResult;\n        } else {\n          try {\n            this.selectFn = options.select;\n            data = options.select(state.data);\n\n            if (options.structuralSharing !== false) {\n              data = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.replaceEqualDeep)(prevResult == null ? void 0 : prevResult.data, data);\n            }\n\n            this.selectResult = data;\n            this.selectError = null;\n          } catch (selectError) {\n            (0,_logger__WEBPACK_IMPORTED_MODULE_4__.getLogger)().error(selectError);\n            this.selectError = selectError;\n          }\n        }\n      } // Use query data\n      else {\n          data = state.data;\n        } // Show placeholder data if needed\n\n\n    if (typeof options.placeholderData !== 'undefined' && typeof data === 'undefined' && (status === 'loading' || status === 'idle')) {\n      var placeholderData; // Memoize placeholder data\n\n      if ((prevResult == null ? void 0 : prevResult.isPlaceholderData) && options.placeholderData === (prevResultOptions == null ? void 0 : prevResultOptions.placeholderData)) {\n        placeholderData = prevResult.data;\n      } else {\n        placeholderData = typeof options.placeholderData === 'function' ? options.placeholderData() : options.placeholderData;\n\n        if (options.select && typeof placeholderData !== 'undefined') {\n          try {\n            placeholderData = options.select(placeholderData);\n\n            if (options.structuralSharing !== false) {\n              placeholderData = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.replaceEqualDeep)(prevResult == null ? void 0 : prevResult.data, placeholderData);\n            }\n\n            this.selectError = null;\n          } catch (selectError) {\n            (0,_logger__WEBPACK_IMPORTED_MODULE_4__.getLogger)().error(selectError);\n            this.selectError = selectError;\n          }\n        }\n      }\n\n      if (typeof placeholderData !== 'undefined') {\n        status = 'success';\n        data = placeholderData;\n        isPlaceholderData = true;\n      }\n    }\n\n    if (this.selectError) {\n      error = this.selectError;\n      data = this.selectResult;\n      errorUpdatedAt = Date.now();\n      status = 'error';\n    }\n\n    var result = {\n      status: status,\n      isLoading: status === 'loading',\n      isSuccess: status === 'success',\n      isError: status === 'error',\n      isIdle: status === 'idle',\n      data: data,\n      dataUpdatedAt: dataUpdatedAt,\n      error: error,\n      errorUpdatedAt: errorUpdatedAt,\n      failureCount: state.fetchFailureCount,\n      errorUpdateCount: state.errorUpdateCount,\n      isFetched: state.dataUpdateCount > 0 || state.errorUpdateCount > 0,\n      isFetchedAfterMount: state.dataUpdateCount > queryInitialState.dataUpdateCount || state.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching: isFetching,\n      isRefetching: isFetching && status !== 'loading',\n      isLoadingError: status === 'error' && state.dataUpdatedAt === 0,\n      isPlaceholderData: isPlaceholderData,\n      isPreviousData: isPreviousData,\n      isRefetchError: status === 'error' && state.dataUpdatedAt !== 0,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      remove: this.remove\n    };\n    return result;\n  };\n\n  _proto.shouldNotifyListeners = function shouldNotifyListeners(result, prevResult) {\n    if (!prevResult) {\n      return true;\n    }\n\n    var _this$options = this.options,\n        notifyOnChangeProps = _this$options.notifyOnChangeProps,\n        notifyOnChangePropsExclusions = _this$options.notifyOnChangePropsExclusions;\n\n    if (!notifyOnChangeProps && !notifyOnChangePropsExclusions) {\n      return true;\n    }\n\n    if (notifyOnChangeProps === 'tracked' && !this.trackedProps.length) {\n      return true;\n    }\n\n    var includedProps = notifyOnChangeProps === 'tracked' ? this.trackedProps : notifyOnChangeProps;\n    return Object.keys(result).some(function (key) {\n      var typedKey = key;\n      var changed = result[typedKey] !== prevResult[typedKey];\n      var isIncluded = includedProps == null ? void 0 : includedProps.some(function (x) {\n        return x === key;\n      });\n      var isExcluded = notifyOnChangePropsExclusions == null ? void 0 : notifyOnChangePropsExclusions.some(function (x) {\n        return x === key;\n      });\n      return changed && !isExcluded && (!includedProps || isIncluded);\n    });\n  };\n\n  _proto.updateResult = function updateResult(notifyOptions) {\n    var prevResult = this.currentResult;\n    this.currentResult = this.createResult(this.currentQuery, this.options);\n    this.currentResultState = this.currentQuery.state;\n    this.currentResultOptions = this.options; // Only notify if something has changed\n\n    if ((0,_utils__WEBPACK_IMPORTED_MODULE_2__.shallowEqualObjects)(this.currentResult, prevResult)) {\n      return;\n    } // Determine which callbacks to trigger\n\n\n    var defaultNotifyOptions = {\n      cache: true\n    };\n\n    if ((notifyOptions == null ? void 0 : notifyOptions.listeners) !== false && this.shouldNotifyListeners(this.currentResult, prevResult)) {\n      defaultNotifyOptions.listeners = true;\n    }\n\n    this.notify((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, defaultNotifyOptions, notifyOptions));\n  };\n\n  _proto.updateQuery = function updateQuery() {\n    var query = this.client.getQueryCache().build(this.client, this.options);\n\n    if (query === this.currentQuery) {\n      return;\n    }\n\n    var prevQuery = this.currentQuery;\n    this.currentQuery = query;\n    this.currentQueryInitialState = query.state;\n    this.previousQueryResult = this.currentResult;\n\n    if (this.hasListeners()) {\n      prevQuery == null ? void 0 : prevQuery.removeObserver(this);\n      query.addObserver(this);\n    }\n  };\n\n  _proto.onQueryUpdate = function onQueryUpdate(action) {\n    var notifyOptions = {};\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = true;\n    } else if (action.type === 'error' && !(0,_retryer__WEBPACK_IMPORTED_MODULE_5__.isCancelledError)(action.error)) {\n      notifyOptions.onError = true;\n    }\n\n    this.updateResult(notifyOptions);\n\n    if (this.hasListeners()) {\n      this.updateTimers();\n    }\n  };\n\n  _proto.notify = function notify(notifyOptions) {\n    var _this8 = this;\n\n    _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {\n      // First trigger the configuration callbacks\n      if (notifyOptions.onSuccess) {\n        _this8.options.onSuccess == null ? void 0 : _this8.options.onSuccess(_this8.currentResult.data);\n        _this8.options.onSettled == null ? void 0 : _this8.options.onSettled(_this8.currentResult.data, null);\n      } else if (notifyOptions.onError) {\n        _this8.options.onError == null ? void 0 : _this8.options.onError(_this8.currentResult.error);\n        _this8.options.onSettled == null ? void 0 : _this8.options.onSettled(undefined, _this8.currentResult.error);\n      } // Then trigger the listeners\n\n\n      if (notifyOptions.listeners) {\n        _this8.listeners.forEach(function (listener) {\n          listener(_this8.currentResult);\n        });\n      } // Then the cache listeners\n\n\n      if (notifyOptions.cache) {\n        _this8.client.getQueryCache().notify({\n          query: _this8.currentQuery,\n          type: 'observerResultsUpdated'\n        });\n      }\n    });\n  };\n\n  return QueryObserver;\n}(_subscribable__WEBPACK_IMPORTED_MODULE_7__.Subscribable);\n\nfunction shouldLoadOnMount(query, options) {\n  return options.enabled !== false && !query.state.dataUpdatedAt && !(query.state.status === 'error' && options.retryOnMount === false);\n}\n\nfunction shouldFetchOnMount(query, options) {\n  return shouldLoadOnMount(query, options) || query.state.dataUpdatedAt > 0 && shouldFetchOn(query, options, options.refetchOnMount);\n}\n\nfunction shouldFetchOn(query, options, field) {\n  if (options.enabled !== false) {\n    var value = typeof field === 'function' ? field(query) : field;\n    return value === 'always' || value !== false && isStale(query, options);\n  }\n\n  return false;\n}\n\nfunction shouldFetchOptionally(query, prevQuery, options, prevOptions) {\n  return options.enabled !== false && (query !== prevQuery || prevOptions.enabled === false) && (!options.suspense || query.state.status !== 'error') && isStale(query, options);\n}\n\nfunction isStale(query, options) {\n  return query.isStaleByTime(options.staleTime);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/queryObserver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/retryer.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-query/es/core/retryer.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CancelledError: () => (/* binding */ CancelledError),\n/* harmony export */   Retryer: () => (/* binding */ Retryer),\n/* harmony export */   isCancelable: () => (/* binding */ isCancelable),\n/* harmony export */   isCancelledError: () => (/* binding */ isCancelledError)\n/* harmony export */ });\n/* harmony import */ var _focusManager__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./focusManager */ \"(ssr)/./node_modules/react-query/es/core/focusManager.js\");\n/* harmony import */ var _onlineManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./onlineManager */ \"(ssr)/./node_modules/react-query/es/core/onlineManager.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n\n\n\n\nfunction defaultRetryDelay(failureCount) {\n  return Math.min(1000 * Math.pow(2, failureCount), 30000);\n}\n\nfunction isCancelable(value) {\n  return typeof (value == null ? void 0 : value.cancel) === 'function';\n}\nvar CancelledError = function CancelledError(options) {\n  this.revert = options == null ? void 0 : options.revert;\n  this.silent = options == null ? void 0 : options.silent;\n};\nfunction isCancelledError(value) {\n  return value instanceof CancelledError;\n} // CLASS\n\nvar Retryer = function Retryer(config) {\n  var _this = this;\n\n  var cancelRetry = false;\n  var cancelFn;\n  var continueFn;\n  var promiseResolve;\n  var promiseReject;\n  this.abort = config.abort;\n\n  this.cancel = function (cancelOptions) {\n    return cancelFn == null ? void 0 : cancelFn(cancelOptions);\n  };\n\n  this.cancelRetry = function () {\n    cancelRetry = true;\n  };\n\n  this.continueRetry = function () {\n    cancelRetry = false;\n  };\n\n  this.continue = function () {\n    return continueFn == null ? void 0 : continueFn();\n  };\n\n  this.failureCount = 0;\n  this.isPaused = false;\n  this.isResolved = false;\n  this.isTransportCancelable = false;\n  this.promise = new Promise(function (outerResolve, outerReject) {\n    promiseResolve = outerResolve;\n    promiseReject = outerReject;\n  });\n\n  var resolve = function resolve(value) {\n    if (!_this.isResolved) {\n      _this.isResolved = true;\n      config.onSuccess == null ? void 0 : config.onSuccess(value);\n      continueFn == null ? void 0 : continueFn();\n      promiseResolve(value);\n    }\n  };\n\n  var reject = function reject(value) {\n    if (!_this.isResolved) {\n      _this.isResolved = true;\n      config.onError == null ? void 0 : config.onError(value);\n      continueFn == null ? void 0 : continueFn();\n      promiseReject(value);\n    }\n  };\n\n  var pause = function pause() {\n    return new Promise(function (continueResolve) {\n      continueFn = continueResolve;\n      _this.isPaused = true;\n      config.onPause == null ? void 0 : config.onPause();\n    }).then(function () {\n      continueFn = undefined;\n      _this.isPaused = false;\n      config.onContinue == null ? void 0 : config.onContinue();\n    });\n  }; // Create loop function\n\n\n  var run = function run() {\n    // Do nothing if already resolved\n    if (_this.isResolved) {\n      return;\n    }\n\n    var promiseOrValue; // Execute query\n\n    try {\n      promiseOrValue = config.fn();\n    } catch (error) {\n      promiseOrValue = Promise.reject(error);\n    } // Create callback to cancel this fetch\n\n\n    cancelFn = function cancelFn(cancelOptions) {\n      if (!_this.isResolved) {\n        reject(new CancelledError(cancelOptions));\n        _this.abort == null ? void 0 : _this.abort(); // Cancel transport if supported\n\n        if (isCancelable(promiseOrValue)) {\n          try {\n            promiseOrValue.cancel();\n          } catch (_unused) {}\n        }\n      }\n    }; // Check if the transport layer support cancellation\n\n\n    _this.isTransportCancelable = isCancelable(promiseOrValue);\n    Promise.resolve(promiseOrValue).then(resolve).catch(function (error) {\n      var _config$retry, _config$retryDelay;\n\n      // Stop if the fetch is already resolved\n      if (_this.isResolved) {\n        return;\n      } // Do we need to retry the request?\n\n\n      var retry = (_config$retry = config.retry) != null ? _config$retry : 3;\n      var retryDelay = (_config$retryDelay = config.retryDelay) != null ? _config$retryDelay : defaultRetryDelay;\n      var delay = typeof retryDelay === 'function' ? retryDelay(_this.failureCount, error) : retryDelay;\n      var shouldRetry = retry === true || typeof retry === 'number' && _this.failureCount < retry || typeof retry === 'function' && retry(_this.failureCount, error);\n\n      if (cancelRetry || !shouldRetry) {\n        // We are done if the query does not need to be retried\n        reject(error);\n        return;\n      }\n\n      _this.failureCount++; // Notify on fail\n\n      config.onFail == null ? void 0 : config.onFail(_this.failureCount, error); // Delay\n\n      (0,_utils__WEBPACK_IMPORTED_MODULE_0__.sleep)(delay) // Pause if the document is not visible or when the device is offline\n      .then(function () {\n        if (!_focusManager__WEBPACK_IMPORTED_MODULE_1__.focusManager.isFocused() || !_onlineManager__WEBPACK_IMPORTED_MODULE_2__.onlineManager.isOnline()) {\n          return pause();\n        }\n      }).then(function () {\n        if (cancelRetry) {\n          reject(error);\n        } else {\n          run();\n        }\n      });\n    });\n  }; // Start loop\n\n\n  run();\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/retryer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/subscribable.js":
/*!**********************************************************!*\
  !*** ./node_modules/react-query/es/core/subscribable.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Subscribable: () => (/* binding */ Subscribable)\n/* harmony export */ });\nvar Subscribable = /*#__PURE__*/function () {\n  function Subscribable() {\n    this.listeners = [];\n  }\n\n  var _proto = Subscribable.prototype;\n\n  _proto.subscribe = function subscribe(listener) {\n    var _this = this;\n\n    var callback = listener || function () {\n      return undefined;\n    };\n\n    this.listeners.push(callback);\n    this.onSubscribe();\n    return function () {\n      _this.listeners = _this.listeners.filter(function (x) {\n        return x !== callback;\n      });\n\n      _this.onUnsubscribe();\n    };\n  };\n\n  _proto.hasListeners = function hasListeners() {\n    return this.listeners.length > 0;\n  };\n\n  _proto.onSubscribe = function onSubscribe() {// Do nothing\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {// Do nothing\n  };\n\n  return Subscribable;\n}();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvY29yZS9zdWJzY3JpYmFibGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPOztBQUVQO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEsK0NBQStDO0FBQy9DOztBQUVBLG1EQUFtRDtBQUNuRDs7QUFFQTtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1zZWN1cml0eS1zY2FubmVyLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3JlYWN0LXF1ZXJ5L2VzL2NvcmUvc3Vic2NyaWJhYmxlLmpzP2M1MTQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHZhciBTdWJzY3JpYmFibGUgPSAvKiNfX1BVUkVfXyovZnVuY3Rpb24gKCkge1xuICBmdW5jdGlvbiBTdWJzY3JpYmFibGUoKSB7XG4gICAgdGhpcy5saXN0ZW5lcnMgPSBbXTtcbiAgfVxuXG4gIHZhciBfcHJvdG8gPSBTdWJzY3JpYmFibGUucHJvdG90eXBlO1xuXG4gIF9wcm90by5zdWJzY3JpYmUgPSBmdW5jdGlvbiBzdWJzY3JpYmUobGlzdGVuZXIpIHtcbiAgICB2YXIgX3RoaXMgPSB0aGlzO1xuXG4gICAgdmFyIGNhbGxiYWNrID0gbGlzdGVuZXIgfHwgZnVuY3Rpb24gKCkge1xuICAgICAgcmV0dXJuIHVuZGVmaW5lZDtcbiAgICB9O1xuXG4gICAgdGhpcy5saXN0ZW5lcnMucHVzaChjYWxsYmFjayk7XG4gICAgdGhpcy5vblN1YnNjcmliZSgpO1xuICAgIHJldHVybiBmdW5jdGlvbiAoKSB7XG4gICAgICBfdGhpcy5saXN0ZW5lcnMgPSBfdGhpcy5saXN0ZW5lcnMuZmlsdGVyKGZ1bmN0aW9uICh4KSB7XG4gICAgICAgIHJldHVybiB4ICE9PSBjYWxsYmFjaztcbiAgICAgIH0pO1xuXG4gICAgICBfdGhpcy5vblVuc3Vic2NyaWJlKCk7XG4gICAgfTtcbiAgfTtcblxuICBfcHJvdG8uaGFzTGlzdGVuZXJzID0gZnVuY3Rpb24gaGFzTGlzdGVuZXJzKCkge1xuICAgIHJldHVybiB0aGlzLmxpc3RlbmVycy5sZW5ndGggPiAwO1xuICB9O1xuXG4gIF9wcm90by5vblN1YnNjcmliZSA9IGZ1bmN0aW9uIG9uU3Vic2NyaWJlKCkgey8vIERvIG5vdGhpbmdcbiAgfTtcblxuICBfcHJvdG8ub25VbnN1YnNjcmliZSA9IGZ1bmN0aW9uIG9uVW5zdWJzY3JpYmUoKSB7Ly8gRG8gbm90aGluZ1xuICB9O1xuXG4gIHJldHVybiBTdWJzY3JpYmFibGU7XG59KCk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/subscribable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/types.js":
/*!***************************************************!*\
  !*** ./node_modules/react-query/es/core/types.js ***!
  \***************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/core/utils.js":
/*!***************************************************!*\
  !*** ./node_modules/react-query/es/core/utils.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   difference: () => (/* binding */ difference),\n/* harmony export */   ensureQueryKeyArray: () => (/* binding */ ensureQueryKeyArray),\n/* harmony export */   functionalUpdate: () => (/* binding */ functionalUpdate),\n/* harmony export */   getAbortController: () => (/* binding */ getAbortController),\n/* harmony export */   hashQueryKey: () => (/* binding */ hashQueryKey),\n/* harmony export */   hashQueryKeyByOptions: () => (/* binding */ hashQueryKeyByOptions),\n/* harmony export */   isError: () => (/* binding */ isError),\n/* harmony export */   isPlainObject: () => (/* binding */ isPlainObject),\n/* harmony export */   isQueryKey: () => (/* binding */ isQueryKey),\n/* harmony export */   isServer: () => (/* binding */ isServer),\n/* harmony export */   isValidTimeout: () => (/* binding */ isValidTimeout),\n/* harmony export */   mapQueryStatusFilter: () => (/* binding */ mapQueryStatusFilter),\n/* harmony export */   matchMutation: () => (/* binding */ matchMutation),\n/* harmony export */   matchQuery: () => (/* binding */ matchQuery),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   parseFilterArgs: () => (/* binding */ parseFilterArgs),\n/* harmony export */   parseMutationArgs: () => (/* binding */ parseMutationArgs),\n/* harmony export */   parseMutationFilterArgs: () => (/* binding */ parseMutationFilterArgs),\n/* harmony export */   parseQueryArgs: () => (/* binding */ parseQueryArgs),\n/* harmony export */   partialDeepEqual: () => (/* binding */ partialDeepEqual),\n/* harmony export */   partialMatchKey: () => (/* binding */ partialMatchKey),\n/* harmony export */   replaceAt: () => (/* binding */ replaceAt),\n/* harmony export */   replaceEqualDeep: () => (/* binding */ replaceEqualDeep),\n/* harmony export */   scheduleMicrotask: () => (/* binding */ scheduleMicrotask),\n/* harmony export */   shallowEqualObjects: () => (/* binding */ shallowEqualObjects),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   stableValueHash: () => (/* binding */ stableValueHash),\n/* harmony export */   timeUntilStale: () => (/* binding */ timeUntilStale)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n\n// TYPES\n// UTILS\nvar isServer = typeof window === 'undefined';\nfunction noop() {\n  return undefined;\n}\nfunction functionalUpdate(updater, input) {\n  return typeof updater === 'function' ? updater(input) : updater;\n}\nfunction isValidTimeout(value) {\n  return typeof value === 'number' && value >= 0 && value !== Infinity;\n}\nfunction ensureQueryKeyArray(value) {\n  return Array.isArray(value) ? value : [value];\n}\nfunction difference(array1, array2) {\n  return array1.filter(function (x) {\n    return array2.indexOf(x) === -1;\n  });\n}\nfunction replaceAt(array, index, value) {\n  var copy = array.slice(0);\n  copy[index] = value;\n  return copy;\n}\nfunction timeUntilStale(updatedAt, staleTime) {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);\n}\nfunction parseQueryArgs(arg1, arg2, arg3) {\n  if (!isQueryKey(arg1)) {\n    return arg1;\n  }\n\n  if (typeof arg2 === 'function') {\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, arg3, {\n      queryKey: arg1,\n      queryFn: arg2\n    });\n  }\n\n  return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, arg2, {\n    queryKey: arg1\n  });\n}\nfunction parseMutationArgs(arg1, arg2, arg3) {\n  if (isQueryKey(arg1)) {\n    if (typeof arg2 === 'function') {\n      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, arg3, {\n        mutationKey: arg1,\n        mutationFn: arg2\n      });\n    }\n\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, arg2, {\n      mutationKey: arg1\n    });\n  }\n\n  if (typeof arg1 === 'function') {\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, arg2, {\n      mutationFn: arg1\n    });\n  }\n\n  return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, arg1);\n}\nfunction parseFilterArgs(arg1, arg2, arg3) {\n  return isQueryKey(arg1) ? [(0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, arg2, {\n    queryKey: arg1\n  }), arg3] : [arg1 || {}, arg2];\n}\nfunction parseMutationFilterArgs(arg1, arg2) {\n  return isQueryKey(arg1) ? (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, arg2, {\n    mutationKey: arg1\n  }) : arg1;\n}\nfunction mapQueryStatusFilter(active, inactive) {\n  if (active === true && inactive === true || active == null && inactive == null) {\n    return 'all';\n  } else if (active === false && inactive === false) {\n    return 'none';\n  } else {\n    // At this point, active|inactive can only be true|false or false|true\n    // so, when only one value is provided, the missing one has to be the negated value\n    var isActive = active != null ? active : !inactive;\n    return isActive ? 'active' : 'inactive';\n  }\n}\nfunction matchQuery(filters, query) {\n  var active = filters.active,\n      exact = filters.exact,\n      fetching = filters.fetching,\n      inactive = filters.inactive,\n      predicate = filters.predicate,\n      queryKey = filters.queryKey,\n      stale = filters.stale;\n\n  if (isQueryKey(queryKey)) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false;\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false;\n    }\n  }\n\n  var queryStatusFilter = mapQueryStatusFilter(active, inactive);\n\n  if (queryStatusFilter === 'none') {\n    return false;\n  } else if (queryStatusFilter !== 'all') {\n    var isActive = query.isActive();\n\n    if (queryStatusFilter === 'active' && !isActive) {\n      return false;\n    }\n\n    if (queryStatusFilter === 'inactive' && isActive) {\n      return false;\n    }\n  }\n\n  if (typeof stale === 'boolean' && query.isStale() !== stale) {\n    return false;\n  }\n\n  if (typeof fetching === 'boolean' && query.isFetching() !== fetching) {\n    return false;\n  }\n\n  if (predicate && !predicate(query)) {\n    return false;\n  }\n\n  return true;\n}\nfunction matchMutation(filters, mutation) {\n  var exact = filters.exact,\n      fetching = filters.fetching,\n      predicate = filters.predicate,\n      mutationKey = filters.mutationKey;\n\n  if (isQueryKey(mutationKey)) {\n    if (!mutation.options.mutationKey) {\n      return false;\n    }\n\n    if (exact) {\n      if (hashQueryKey(mutation.options.mutationKey) !== hashQueryKey(mutationKey)) {\n        return false;\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false;\n    }\n  }\n\n  if (typeof fetching === 'boolean' && mutation.state.status === 'loading' !== fetching) {\n    return false;\n  }\n\n  if (predicate && !predicate(mutation)) {\n    return false;\n  }\n\n  return true;\n}\nfunction hashQueryKeyByOptions(queryKey, options) {\n  var hashFn = (options == null ? void 0 : options.queryKeyHashFn) || hashQueryKey;\n  return hashFn(queryKey);\n}\n/**\n * Default query keys hash function.\n */\n\nfunction hashQueryKey(queryKey) {\n  var asArray = ensureQueryKeyArray(queryKey);\n  return stableValueHash(asArray);\n}\n/**\n * Hashes the value into a stable hash.\n */\n\nfunction stableValueHash(value) {\n  return JSON.stringify(value, function (_, val) {\n    return isPlainObject(val) ? Object.keys(val).sort().reduce(function (result, key) {\n      result[key] = val[key];\n      return result;\n    }, {}) : val;\n  });\n}\n/**\n * Checks if key `b` partially matches with key `a`.\n */\n\nfunction partialMatchKey(a, b) {\n  return partialDeepEqual(ensureQueryKeyArray(a), ensureQueryKeyArray(b));\n}\n/**\n * Checks if `b` partially matches with `a`.\n */\n\nfunction partialDeepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n\n  if (typeof a !== typeof b) {\n    return false;\n  }\n\n  if (a && b && typeof a === 'object' && typeof b === 'object') {\n    return !Object.keys(b).some(function (key) {\n      return !partialDeepEqual(a[key], b[key]);\n    });\n  }\n\n  return false;\n}\n/**\n * This function returns `a` if `b` is deeply equal.\n * If not, it will replace any deeply equal children of `b` with those of `a`.\n * This can be used for structural sharing between JSON values for example.\n */\n\nfunction replaceEqualDeep(a, b) {\n  if (a === b) {\n    return a;\n  }\n\n  var array = Array.isArray(a) && Array.isArray(b);\n\n  if (array || isPlainObject(a) && isPlainObject(b)) {\n    var aSize = array ? a.length : Object.keys(a).length;\n    var bItems = array ? b : Object.keys(b);\n    var bSize = bItems.length;\n    var copy = array ? [] : {};\n    var equalItems = 0;\n\n    for (var i = 0; i < bSize; i++) {\n      var key = array ? i : bItems[i];\n      copy[key] = replaceEqualDeep(a[key], b[key]);\n\n      if (copy[key] === a[key]) {\n        equalItems++;\n      }\n    }\n\n    return aSize === bSize && equalItems === aSize ? a : copy;\n  }\n\n  return b;\n}\n/**\n * Shallow compare objects. Only works with objects that always have the same properties.\n */\n\nfunction shallowEqualObjects(a, b) {\n  if (a && !b || b && !a) {\n    return false;\n  }\n\n  for (var key in a) {\n    if (a[key] !== b[key]) {\n      return false;\n    }\n  }\n\n  return true;\n} // Copied from: https://github.com/jonschlinkert/is-plain-object\n\nfunction isPlainObject(o) {\n  if (!hasObjectPrototype(o)) {\n    return false;\n  } // If has modified constructor\n\n\n  var ctor = o.constructor;\n\n  if (typeof ctor === 'undefined') {\n    return true;\n  } // If has modified prototype\n\n\n  var prot = ctor.prototype;\n\n  if (!hasObjectPrototype(prot)) {\n    return false;\n  } // If constructor does not have an Object-specific method\n\n\n  if (!prot.hasOwnProperty('isPrototypeOf')) {\n    return false;\n  } // Most likely a plain Object\n\n\n  return true;\n}\n\nfunction hasObjectPrototype(o) {\n  return Object.prototype.toString.call(o) === '[object Object]';\n}\n\nfunction isQueryKey(value) {\n  return typeof value === 'string' || Array.isArray(value);\n}\nfunction isError(value) {\n  return value instanceof Error;\n}\nfunction sleep(timeout) {\n  return new Promise(function (resolve) {\n    setTimeout(resolve, timeout);\n  });\n}\n/**\n * Schedules a microtask.\n * This can be useful to schedule state updates after rendering.\n */\n\nfunction scheduleMicrotask(callback) {\n  Promise.resolve().then(callback).catch(function (error) {\n    return setTimeout(function () {\n      throw error;\n    });\n  });\n}\nfunction getAbortController() {\n  if (typeof AbortController === 'function') {\n    return new AbortController();\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/core/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/index.js":
/*!**********************************************!*\
  !*** ./node_modules/react-query/es/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core */ \"(ssr)/./node_modules/react-query/es/core/index.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _core__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _core__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./react */ \"(ssr)/./node_modules/react-query/es/react/index.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _react__WEBPACK_IMPORTED_MODULE_1__) if([\"default\",\"CancelledError\",\"QueryCache\",\"QueryClient\",\"QueryObserver\",\"QueriesObserver\",\"InfiniteQueryObserver\",\"MutationCache\",\"MutationObserver\",\"setLogger\",\"notifyManager\",\"focusManager\",\"onlineManager\",\"hashQueryKey\",\"isError\",\"isCancelledError\",\"dehydrate\",\"hydrate\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _react__WEBPACK_IMPORTED_MODULE_1__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktc2VjdXJpdHktc2Nhbm5lci1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9yZWFjdC1xdWVyeS9lcy9pbmRleC5qcz9kYTk5Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4vY29yZSc7XG5leHBvcnQgKiBmcm9tICcuL3JlYWN0JzsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/Hydrate.js":
/*!******************************************************!*\
  !*** ./node_modules/react-query/es/react/Hydrate.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hydrate: () => (/* binding */ Hydrate),\n/* harmony export */   useHydrate: () => (/* binding */ useHydrate)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../core */ \"(ssr)/./node_modules/react-query/es/core/hydration.js\");\n/* harmony import */ var _QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider */ \"(ssr)/./node_modules/react-query/es/react/QueryClientProvider.js\");\n\n\n\nfunction useHydrate(state, options) {\n  var queryClient = (0,_QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n  var optionsRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(options);\n  optionsRef.current = options; // Running hydrate again with the same queries is safe,\n  // it wont overwrite or initialize existing queries,\n  // relying on useMemo here is only a performance optimization.\n  // hydrate can and should be run *during* render here for SSR to work properly\n\n  react__WEBPACK_IMPORTED_MODULE_0___default().useMemo(function () {\n    if (state) {\n      (0,_core__WEBPACK_IMPORTED_MODULE_2__.hydrate)(queryClient, state, optionsRef.current);\n    }\n  }, [queryClient, state]);\n}\nvar Hydrate = function Hydrate(_ref) {\n  var children = _ref.children,\n      options = _ref.options,\n      state = _ref.state;\n  useHydrate(state, options);\n  return children;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvSHlkcmF0ZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEI7QUFDUTtBQUNxQjtBQUNoRDtBQUNQLG9CQUFvQixvRUFBYztBQUNsQyxtQkFBbUIsbURBQVk7QUFDL0IsZ0NBQWdDO0FBQ2hDO0FBQ0E7QUFDQTs7QUFFQSxFQUFFLG9EQUFhO0FBQ2Y7QUFDQSxNQUFNLDhDQUFPO0FBQ2I7QUFDQSxHQUFHO0FBQ0g7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXNlY3VyaXR5LXNjYW5uZXItZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvSHlkcmF0ZS5qcz9iYjk5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBoeWRyYXRlIH0gZnJvbSAnLi4vY29yZSc7XG5pbXBvcnQgeyB1c2VRdWVyeUNsaWVudCB9IGZyb20gJy4vUXVlcnlDbGllbnRQcm92aWRlcic7XG5leHBvcnQgZnVuY3Rpb24gdXNlSHlkcmF0ZShzdGF0ZSwgb3B0aW9ucykge1xuICB2YXIgcXVlcnlDbGllbnQgPSB1c2VRdWVyeUNsaWVudCgpO1xuICB2YXIgb3B0aW9uc1JlZiA9IFJlYWN0LnVzZVJlZihvcHRpb25zKTtcbiAgb3B0aW9uc1JlZi5jdXJyZW50ID0gb3B0aW9uczsgLy8gUnVubmluZyBoeWRyYXRlIGFnYWluIHdpdGggdGhlIHNhbWUgcXVlcmllcyBpcyBzYWZlLFxuICAvLyBpdCB3b250IG92ZXJ3cml0ZSBvciBpbml0aWFsaXplIGV4aXN0aW5nIHF1ZXJpZXMsXG4gIC8vIHJlbHlpbmcgb24gdXNlTWVtbyBoZXJlIGlzIG9ubHkgYSBwZXJmb3JtYW5jZSBvcHRpbWl6YXRpb24uXG4gIC8vIGh5ZHJhdGUgY2FuIGFuZCBzaG91bGQgYmUgcnVuICpkdXJpbmcqIHJlbmRlciBoZXJlIGZvciBTU1IgdG8gd29yayBwcm9wZXJseVxuXG4gIFJlYWN0LnVzZU1lbW8oZnVuY3Rpb24gKCkge1xuICAgIGlmIChzdGF0ZSkge1xuICAgICAgaHlkcmF0ZShxdWVyeUNsaWVudCwgc3RhdGUsIG9wdGlvbnNSZWYuY3VycmVudCk7XG4gICAgfVxuICB9LCBbcXVlcnlDbGllbnQsIHN0YXRlXSk7XG59XG5leHBvcnQgdmFyIEh5ZHJhdGUgPSBmdW5jdGlvbiBIeWRyYXRlKF9yZWYpIHtcbiAgdmFyIGNoaWxkcmVuID0gX3JlZi5jaGlsZHJlbixcbiAgICAgIG9wdGlvbnMgPSBfcmVmLm9wdGlvbnMsXG4gICAgICBzdGF0ZSA9IF9yZWYuc3RhdGU7XG4gIHVzZUh5ZHJhdGUoc3RhdGUsIG9wdGlvbnMpO1xuICByZXR1cm4gY2hpbGRyZW47XG59OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/Hydrate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/QueryClientProvider.js":
/*!******************************************************************!*\
  !*** ./node_modules/react-query/es/react/QueryClientProvider.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClientProvider: () => (/* binding */ QueryClientProvider),\n/* harmony export */   useQueryClient: () => (/* binding */ useQueryClient)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar defaultContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createContext(undefined);\nvar QueryClientSharingContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createContext(false); // if contextSharing is on, we share the first and at least one\n// instance of the context across the window\n// to ensure that if React Query is used across\n// different bundles or microfrontends they will\n// all use the same **instance** of context, regardless\n// of module scoping.\n\nfunction getQueryClientContext(contextSharing) {\n  if (contextSharing && typeof window !== 'undefined') {\n    if (!window.ReactQueryClientContext) {\n      window.ReactQueryClientContext = defaultContext;\n    }\n\n    return window.ReactQueryClientContext;\n  }\n\n  return defaultContext;\n}\n\nvar useQueryClient = function useQueryClient() {\n  var queryClient = react__WEBPACK_IMPORTED_MODULE_0___default().useContext(getQueryClientContext(react__WEBPACK_IMPORTED_MODULE_0___default().useContext(QueryClientSharingContext)));\n\n  if (!queryClient) {\n    throw new Error('No QueryClient set, use QueryClientProvider to set one');\n  }\n\n  return queryClient;\n};\nvar QueryClientProvider = function QueryClientProvider(_ref) {\n  var client = _ref.client,\n      _ref$contextSharing = _ref.contextSharing,\n      contextSharing = _ref$contextSharing === void 0 ? false : _ref$contextSharing,\n      children = _ref.children;\n  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(function () {\n    client.mount();\n    return function () {\n      client.unmount();\n    };\n  }, [client]);\n  var Context = getQueryClientContext(contextSharing);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(QueryClientSharingContext.Provider, {\n    value: contextSharing\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Context.Provider, {\n    value: client\n  }, children));\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/QueryClientProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/QueryErrorResetBoundary.js":
/*!**********************************************************************!*\
  !*** ./node_modules/react-query/es/react/QueryErrorResetBoundary.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryErrorResetBoundary: () => (/* binding */ QueryErrorResetBoundary),\n/* harmony export */   useQueryErrorResetBoundary: () => (/* binding */ useQueryErrorResetBoundary)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n // CONTEXT\n\nfunction createValue() {\n  var _isReset = false;\n  return {\n    clearReset: function clearReset() {\n      _isReset = false;\n    },\n    reset: function reset() {\n      _isReset = true;\n    },\n    isReset: function isReset() {\n      return _isReset;\n    }\n  };\n}\n\nvar QueryErrorResetBoundaryContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createContext(createValue()); // HOOK\n\nvar useQueryErrorResetBoundary = function useQueryErrorResetBoundary() {\n  return react__WEBPACK_IMPORTED_MODULE_0___default().useContext(QueryErrorResetBoundaryContext);\n}; // COMPONENT\n\nvar QueryErrorResetBoundary = function QueryErrorResetBoundary(_ref) {\n  var children = _ref.children;\n  var value = react__WEBPACK_IMPORTED_MODULE_0___default().useMemo(function () {\n    return createValue();\n  }, []);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(QueryErrorResetBoundaryContext.Provider, {\n    value: value\n  }, typeof children === 'function' ? children(value) : children);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvUXVlcnlFcnJvclJlc2V0Qm91bmRhcnkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUEwQixDQUFDOztBQUUzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxrREFBa0QsMERBQW1CLGlCQUFpQjs7QUFFL0U7QUFDUCxTQUFTLHVEQUFnQjtBQUN6QixHQUFHOztBQUVJO0FBQ1A7QUFDQSxjQUFjLG9EQUFhO0FBQzNCO0FBQ0EsR0FBRztBQUNILHNCQUFzQiwwREFBbUI7QUFDekM7QUFDQSxHQUFHO0FBQ0giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1zZWN1cml0eS1zY2FubmVyLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3JlYWN0LXF1ZXJ5L2VzL3JlYWN0L1F1ZXJ5RXJyb3JSZXNldEJvdW5kYXJ5LmpzPzdjYmUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JzsgLy8gQ09OVEVYVFxuXG5mdW5jdGlvbiBjcmVhdGVWYWx1ZSgpIHtcbiAgdmFyIF9pc1Jlc2V0ID0gZmFsc2U7XG4gIHJldHVybiB7XG4gICAgY2xlYXJSZXNldDogZnVuY3Rpb24gY2xlYXJSZXNldCgpIHtcbiAgICAgIF9pc1Jlc2V0ID0gZmFsc2U7XG4gICAgfSxcbiAgICByZXNldDogZnVuY3Rpb24gcmVzZXQoKSB7XG4gICAgICBfaXNSZXNldCA9IHRydWU7XG4gICAgfSxcbiAgICBpc1Jlc2V0OiBmdW5jdGlvbiBpc1Jlc2V0KCkge1xuICAgICAgcmV0dXJuIF9pc1Jlc2V0O1xuICAgIH1cbiAgfTtcbn1cblxudmFyIFF1ZXJ5RXJyb3JSZXNldEJvdW5kYXJ5Q29udGV4dCA9IC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVDb250ZXh0KGNyZWF0ZVZhbHVlKCkpOyAvLyBIT09LXG5cbmV4cG9ydCB2YXIgdXNlUXVlcnlFcnJvclJlc2V0Qm91bmRhcnkgPSBmdW5jdGlvbiB1c2VRdWVyeUVycm9yUmVzZXRCb3VuZGFyeSgpIHtcbiAgcmV0dXJuIFJlYWN0LnVzZUNvbnRleHQoUXVlcnlFcnJvclJlc2V0Qm91bmRhcnlDb250ZXh0KTtcbn07IC8vIENPTVBPTkVOVFxuXG5leHBvcnQgdmFyIFF1ZXJ5RXJyb3JSZXNldEJvdW5kYXJ5ID0gZnVuY3Rpb24gUXVlcnlFcnJvclJlc2V0Qm91bmRhcnkoX3JlZikge1xuICB2YXIgY2hpbGRyZW4gPSBfcmVmLmNoaWxkcmVuO1xuICB2YXIgdmFsdWUgPSBSZWFjdC51c2VNZW1vKGZ1bmN0aW9uICgpIHtcbiAgICByZXR1cm4gY3JlYXRlVmFsdWUoKTtcbiAgfSwgW10pO1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoUXVlcnlFcnJvclJlc2V0Qm91bmRhcnlDb250ZXh0LlByb3ZpZGVyLCB7XG4gICAgdmFsdWU6IHZhbHVlXG4gIH0sIHR5cGVvZiBjaGlsZHJlbiA9PT0gJ2Z1bmN0aW9uJyA/IGNoaWxkcmVuKHZhbHVlKSA6IGNoaWxkcmVuKTtcbn07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/QueryErrorResetBoundary.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/index.js":
/*!****************************************************!*\
  !*** ./node_modules/react-query/es/react/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hydrate: () => (/* reexport safe */ _Hydrate__WEBPACK_IMPORTED_MODULE_10__.Hydrate),\n/* harmony export */   QueryClientProvider: () => (/* reexport safe */ _QueryClientProvider__WEBPACK_IMPORTED_MODULE_2__.QueryClientProvider),\n/* harmony export */   QueryErrorResetBoundary: () => (/* reexport safe */ _QueryErrorResetBoundary__WEBPACK_IMPORTED_MODULE_3__.QueryErrorResetBoundary),\n/* harmony export */   useHydrate: () => (/* reexport safe */ _Hydrate__WEBPACK_IMPORTED_MODULE_10__.useHydrate),\n/* harmony export */   useInfiniteQuery: () => (/* reexport safe */ _useInfiniteQuery__WEBPACK_IMPORTED_MODULE_9__.useInfiniteQuery),\n/* harmony export */   useIsFetching: () => (/* reexport safe */ _useIsFetching__WEBPACK_IMPORTED_MODULE_4__.useIsFetching),\n/* harmony export */   useIsMutating: () => (/* reexport safe */ _useIsMutating__WEBPACK_IMPORTED_MODULE_5__.useIsMutating),\n/* harmony export */   useMutation: () => (/* reexport safe */ _useMutation__WEBPACK_IMPORTED_MODULE_6__.useMutation),\n/* harmony export */   useQueries: () => (/* reexport safe */ _useQueries__WEBPACK_IMPORTED_MODULE_8__.useQueries),\n/* harmony export */   useQuery: () => (/* reexport safe */ _useQuery__WEBPACK_IMPORTED_MODULE_7__.useQuery),\n/* harmony export */   useQueryClient: () => (/* reexport safe */ _QueryClientProvider__WEBPACK_IMPORTED_MODULE_2__.useQueryClient),\n/* harmony export */   useQueryErrorResetBoundary: () => (/* reexport safe */ _QueryErrorResetBoundary__WEBPACK_IMPORTED_MODULE_3__.useQueryErrorResetBoundary)\n/* harmony export */ });\n/* harmony import */ var _setBatchUpdatesFn__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./setBatchUpdatesFn */ \"(ssr)/./node_modules/react-query/es/react/setBatchUpdatesFn.js\");\n/* harmony import */ var _setLogger__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./setLogger */ \"(ssr)/./node_modules/react-query/es/react/setLogger.js\");\n/* harmony import */ var _QueryClientProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QueryClientProvider */ \"(ssr)/./node_modules/react-query/es/react/QueryClientProvider.js\");\n/* harmony import */ var _QueryErrorResetBoundary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QueryErrorResetBoundary */ \"(ssr)/./node_modules/react-query/es/react/QueryErrorResetBoundary.js\");\n/* harmony import */ var _useIsFetching__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./useIsFetching */ \"(ssr)/./node_modules/react-query/es/react/useIsFetching.js\");\n/* harmony import */ var _useIsMutating__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./useIsMutating */ \"(ssr)/./node_modules/react-query/es/react/useIsMutating.js\");\n/* harmony import */ var _useMutation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./useMutation */ \"(ssr)/./node_modules/react-query/es/react/useMutation.js\");\n/* harmony import */ var _useQuery__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./useQuery */ \"(ssr)/./node_modules/react-query/es/react/useQuery.js\");\n/* harmony import */ var _useQueries__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./useQueries */ \"(ssr)/./node_modules/react-query/es/react/useQueries.js\");\n/* harmony import */ var _useInfiniteQuery__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./useInfiniteQuery */ \"(ssr)/./node_modules/react-query/es/react/useInfiniteQuery.js\");\n/* harmony import */ var _Hydrate__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./Hydrate */ \"(ssr)/./node_modules/react-query/es/react/Hydrate.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/react-query/es/react/types.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_types__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _types__WEBPACK_IMPORTED_MODULE_11__) if([\"default\",\"QueryClientProvider\",\"useQueryClient\",\"QueryErrorResetBoundary\",\"useQueryErrorResetBoundary\",\"useIsFetching\",\"useIsMutating\",\"useMutation\",\"useQuery\",\"useQueries\",\"useInfiniteQuery\",\"useHydrate\",\"Hydrate\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _types__WEBPACK_IMPORTED_MODULE_11__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n// Side effects\n\n\n\n\n\n\n\n\n\n\n // Types\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQzZCO0FBQ1I7QUFDdUQ7QUFDb0I7QUFDaEQ7QUFDQTtBQUNKO0FBQ047QUFDSTtBQUNZO0FBQ04sQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXNlY3VyaXR5LXNjYW5uZXItZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvaW5kZXguanM/MzJlNSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBTaWRlIGVmZmVjdHNcbmltcG9ydCAnLi9zZXRCYXRjaFVwZGF0ZXNGbic7XG5pbXBvcnQgJy4vc2V0TG9nZ2VyJztcbmV4cG9ydCB7IFF1ZXJ5Q2xpZW50UHJvdmlkZXIsIHVzZVF1ZXJ5Q2xpZW50IH0gZnJvbSAnLi9RdWVyeUNsaWVudFByb3ZpZGVyJztcbmV4cG9ydCB7IFF1ZXJ5RXJyb3JSZXNldEJvdW5kYXJ5LCB1c2VRdWVyeUVycm9yUmVzZXRCb3VuZGFyeSB9IGZyb20gJy4vUXVlcnlFcnJvclJlc2V0Qm91bmRhcnknO1xuZXhwb3J0IHsgdXNlSXNGZXRjaGluZyB9IGZyb20gJy4vdXNlSXNGZXRjaGluZyc7XG5leHBvcnQgeyB1c2VJc011dGF0aW5nIH0gZnJvbSAnLi91c2VJc011dGF0aW5nJztcbmV4cG9ydCB7IHVzZU11dGF0aW9uIH0gZnJvbSAnLi91c2VNdXRhdGlvbic7XG5leHBvcnQgeyB1c2VRdWVyeSB9IGZyb20gJy4vdXNlUXVlcnknO1xuZXhwb3J0IHsgdXNlUXVlcmllcyB9IGZyb20gJy4vdXNlUXVlcmllcyc7XG5leHBvcnQgeyB1c2VJbmZpbml0ZVF1ZXJ5IH0gZnJvbSAnLi91c2VJbmZpbml0ZVF1ZXJ5JztcbmV4cG9ydCB7IHVzZUh5ZHJhdGUsIEh5ZHJhdGUgfSBmcm9tICcuL0h5ZHJhdGUnOyAvLyBUeXBlc1xuXG5leHBvcnQgKiBmcm9tICcuL3R5cGVzJzsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/logger.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-query/es/react/logger.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   logger: () => (/* binding */ logger)\n/* harmony export */ });\nvar logger = console;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvbG9nZ2VyLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXNlY3VyaXR5LXNjYW5uZXItZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvbG9nZ2VyLmpzPzE0OTEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHZhciBsb2dnZXIgPSBjb25zb2xlOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/logger.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/reactBatchedUpdates.js":
/*!******************************************************************!*\
  !*** ./node_modules/react-query/es/react/reactBatchedUpdates.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unstable_batchedUpdates: () => (/* binding */ unstable_batchedUpdates)\n/* harmony export */ });\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_0__);\n\nvar unstable_batchedUpdates = (react_dom__WEBPACK_IMPORTED_MODULE_0___default().unstable_batchedUpdates);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvcmVhY3RCYXRjaGVkVXBkYXRlcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBaUM7QUFDMUIsOEJBQThCLDBFQUFnQyIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXNlY3VyaXR5LXNjYW5uZXItZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvcmVhY3RCYXRjaGVkVXBkYXRlcy5qcz81NGVhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdERPTSBmcm9tICdyZWFjdC1kb20nO1xuZXhwb3J0IHZhciB1bnN0YWJsZV9iYXRjaGVkVXBkYXRlcyA9IFJlYWN0RE9NLnVuc3RhYmxlX2JhdGNoZWRVcGRhdGVzOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/reactBatchedUpdates.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/setBatchUpdatesFn.js":
/*!****************************************************************!*\
  !*** ./node_modules/react-query/es/react/setBatchUpdatesFn.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _reactBatchedUpdates__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./reactBatchedUpdates */ \"(ssr)/./node_modules/react-query/es/react/reactBatchedUpdates.js\");\n\n\n_core__WEBPACK_IMPORTED_MODULE_0__.notifyManager.setBatchNotifyFunction(_reactBatchedUpdates__WEBPACK_IMPORTED_MODULE_1__.unstable_batchedUpdates);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3Qvc2V0QmF0Y2hVcGRhdGVzRm4uanMiLCJtYXBwaW5ncyI6Ijs7O0FBQXdDO0FBQ3dCO0FBQ2hFLGdEQUFhLHdCQUF3Qix5RUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1zZWN1cml0eS1zY2FubmVyLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3JlYWN0LXF1ZXJ5L2VzL3JlYWN0L3NldEJhdGNoVXBkYXRlc0ZuLmpzP2ZmZTAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgbm90aWZ5TWFuYWdlciB9IGZyb20gJy4uL2NvcmUnO1xuaW1wb3J0IHsgdW5zdGFibGVfYmF0Y2hlZFVwZGF0ZXMgfSBmcm9tICcuL3JlYWN0QmF0Y2hlZFVwZGF0ZXMnO1xubm90aWZ5TWFuYWdlci5zZXRCYXRjaE5vdGlmeUZ1bmN0aW9uKHVuc3RhYmxlX2JhdGNoZWRVcGRhdGVzKTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/setBatchUpdatesFn.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/setLogger.js":
/*!********************************************************!*\
  !*** ./node_modules/react-query/es/react/setLogger.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core */ \"(ssr)/./node_modules/react-query/es/core/logger.js\");\n/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./logger */ \"(ssr)/./node_modules/react-query/es/react/logger.js\");\n\n\n(0,_core__WEBPACK_IMPORTED_MODULE_0__.setLogger)(_logger__WEBPACK_IMPORTED_MODULE_1__.logger);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3Qvc2V0TG9nZ2VyLmpzIiwibWFwcGluZ3MiOiI7OztBQUFvQztBQUNGO0FBQ2xDLGdEQUFTLENBQUMsMkNBQU0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1zZWN1cml0eS1zY2FubmVyLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3JlYWN0LXF1ZXJ5L2VzL3JlYWN0L3NldExvZ2dlci5qcz85ODMwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHNldExvZ2dlciB9IGZyb20gJy4uL2NvcmUnO1xuaW1wb3J0IHsgbG9nZ2VyIH0gZnJvbSAnLi9sb2dnZXInO1xuc2V0TG9nZ2VyKGxvZ2dlcik7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/setLogger.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/types.js":
/*!****************************************************!*\
  !*** ./node_modules/react-query/es/react/types.js ***!
  \****************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/useBaseQuery.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-query/es/react/useBaseQuery.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useBaseQuery: () => (/* binding */ useBaseQuery)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _core_notifyManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../core/notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _QueryErrorResetBoundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QueryErrorResetBoundary */ \"(ssr)/./node_modules/react-query/es/react/QueryErrorResetBoundary.js\");\n/* harmony import */ var _QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider */ \"(ssr)/./node_modules/react-query/es/react/QueryClientProvider.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/react/utils.js\");\n\n\n\n\n\nfunction useBaseQuery(options, Observer) {\n  var mountedRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(false);\n\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_0___default().useState(0),\n      forceUpdate = _React$useState[1];\n\n  var queryClient = (0,_QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n  var errorResetBoundary = (0,_QueryErrorResetBoundary__WEBPACK_IMPORTED_MODULE_2__.useQueryErrorResetBoundary)();\n  var defaultedOptions = queryClient.defaultQueryObserverOptions(options); // Make sure results are optimistically set in fetching state before subscribing or updating options\n\n  defaultedOptions.optimisticResults = true; // Include callbacks in batch renders\n\n  if (defaultedOptions.onError) {\n    defaultedOptions.onError = _core_notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(defaultedOptions.onError);\n  }\n\n  if (defaultedOptions.onSuccess) {\n    defaultedOptions.onSuccess = _core_notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(defaultedOptions.onSuccess);\n  }\n\n  if (defaultedOptions.onSettled) {\n    defaultedOptions.onSettled = _core_notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(defaultedOptions.onSettled);\n  }\n\n  if (defaultedOptions.suspense) {\n    // Always set stale time when using suspense to prevent\n    // fetching again when directly mounting after suspending\n    if (typeof defaultedOptions.staleTime !== 'number') {\n      defaultedOptions.staleTime = 1000;\n    } // Set cache time to 1 if the option has been set to 0\n    // when using suspense to prevent infinite loop of fetches\n\n\n    if (defaultedOptions.cacheTime === 0) {\n      defaultedOptions.cacheTime = 1;\n    }\n  }\n\n  if (defaultedOptions.suspense || defaultedOptions.useErrorBoundary) {\n    // Prevent retrying failed query if the error boundary has not been reset yet\n    if (!errorResetBoundary.isReset()) {\n      defaultedOptions.retryOnMount = false;\n    }\n  }\n\n  var _React$useState2 = react__WEBPACK_IMPORTED_MODULE_0___default().useState(function () {\n    return new Observer(queryClient, defaultedOptions);\n  }),\n      observer = _React$useState2[0];\n\n  var result = observer.getOptimisticResult(defaultedOptions);\n  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(function () {\n    mountedRef.current = true;\n    errorResetBoundary.clearReset();\n    var unsubscribe = observer.subscribe(_core_notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        forceUpdate(function (x) {\n          return x + 1;\n        });\n      }\n    })); // Update result to make sure we did not miss any query updates\n    // between creating the observer and subscribing to it.\n\n    observer.updateResult();\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, [errorResetBoundary, observer]);\n  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(function () {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setOptions(defaultedOptions, {\n      listeners: false\n    });\n  }, [defaultedOptions, observer]); // Handle suspense\n\n  if (defaultedOptions.suspense && result.isLoading) {\n    throw observer.fetchOptimistic(defaultedOptions).then(function (_ref) {\n      var data = _ref.data;\n      defaultedOptions.onSuccess == null ? void 0 : defaultedOptions.onSuccess(data);\n      defaultedOptions.onSettled == null ? void 0 : defaultedOptions.onSettled(data, null);\n    }).catch(function (error) {\n      errorResetBoundary.clearReset();\n      defaultedOptions.onError == null ? void 0 : defaultedOptions.onError(error);\n      defaultedOptions.onSettled == null ? void 0 : defaultedOptions.onSettled(undefined, error);\n    });\n  } // Handle error boundary\n\n\n  if (result.isError && !errorResetBoundary.isReset() && !result.isFetching && (0,_utils__WEBPACK_IMPORTED_MODULE_4__.shouldThrowError)(defaultedOptions.suspense, defaultedOptions.useErrorBoundary, [result.error, observer.getCurrentQuery()])) {\n    throw result.error;\n  } // Handle result property usage tracking\n\n\n  if (defaultedOptions.notifyOnChangeProps === 'tracked') {\n    result = observer.trackResult(result, defaultedOptions);\n  }\n\n  return result;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/useBaseQuery.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/useInfiniteQuery.js":
/*!***************************************************************!*\
  !*** ./node_modules/react-query/es/react/useInfiniteQuery.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useInfiniteQuery: () => (/* binding */ useInfiniteQuery)\n/* harmony export */ });\n/* harmony import */ var _core_infiniteQueryObserver__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../core/infiniteQueryObserver */ \"(ssr)/./node_modules/react-query/es/core/infiniteQueryObserver.js\");\n/* harmony import */ var _core_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _useBaseQuery__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useBaseQuery */ \"(ssr)/./node_modules/react-query/es/react/useBaseQuery.js\");\n\n\n // HOOK\n\nfunction useInfiniteQuery(arg1, arg2, arg3) {\n  var options = (0,_core_utils__WEBPACK_IMPORTED_MODULE_0__.parseQueryArgs)(arg1, arg2, arg3);\n  return (0,_useBaseQuery__WEBPACK_IMPORTED_MODULE_1__.useBaseQuery)(options, _core_infiniteQueryObserver__WEBPACK_IMPORTED_MODULE_2__.InfiniteQueryObserver);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvdXNlSW5maW5pdGVRdWVyeS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXNFO0FBQ3ZCO0FBQ0QsQ0FBQzs7QUFFeEM7QUFDUCxnQkFBZ0IsMkRBQWM7QUFDOUIsU0FBUywyREFBWSxVQUFVLDhFQUFxQjtBQUNwRCIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXNlY3VyaXR5LXNjYW5uZXItZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvdXNlSW5maW5pdGVRdWVyeS5qcz9iNWZlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEluZmluaXRlUXVlcnlPYnNlcnZlciB9IGZyb20gJy4uL2NvcmUvaW5maW5pdGVRdWVyeU9ic2VydmVyJztcbmltcG9ydCB7IHBhcnNlUXVlcnlBcmdzIH0gZnJvbSAnLi4vY29yZS91dGlscyc7XG5pbXBvcnQgeyB1c2VCYXNlUXVlcnkgfSBmcm9tICcuL3VzZUJhc2VRdWVyeSc7IC8vIEhPT0tcblxuZXhwb3J0IGZ1bmN0aW9uIHVzZUluZmluaXRlUXVlcnkoYXJnMSwgYXJnMiwgYXJnMykge1xuICB2YXIgb3B0aW9ucyA9IHBhcnNlUXVlcnlBcmdzKGFyZzEsIGFyZzIsIGFyZzMpO1xuICByZXR1cm4gdXNlQmFzZVF1ZXJ5KG9wdGlvbnMsIEluZmluaXRlUXVlcnlPYnNlcnZlcik7XG59Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/useInfiniteQuery.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/useIsFetching.js":
/*!************************************************************!*\
  !*** ./node_modules/react-query/es/react/useIsFetching.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useIsFetching: () => (/* binding */ useIsFetching)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _core_notifyManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../core/notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _core_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../core/utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider */ \"(ssr)/./node_modules/react-query/es/react/QueryClientProvider.js\");\n\n\n\n\n\nvar checkIsFetching = function checkIsFetching(queryClient, filters, isFetching, setIsFetching) {\n  var newIsFetching = queryClient.isFetching(filters);\n\n  if (isFetching !== newIsFetching) {\n    setIsFetching(newIsFetching);\n  }\n};\n\nfunction useIsFetching(arg1, arg2) {\n  var mountedRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(false);\n  var queryClient = (0,_QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n\n  var _parseFilterArgs = (0,_core_utils__WEBPACK_IMPORTED_MODULE_2__.parseFilterArgs)(arg1, arg2),\n      filters = _parseFilterArgs[0];\n\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_0___default().useState(queryClient.isFetching(filters)),\n      isFetching = _React$useState[0],\n      setIsFetching = _React$useState[1];\n\n  var filtersRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(filters);\n  filtersRef.current = filters;\n  var isFetchingRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(isFetching);\n  isFetchingRef.current = isFetching;\n  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(function () {\n    mountedRef.current = true;\n    checkIsFetching(queryClient, filtersRef.current, isFetchingRef.current, setIsFetching);\n    var unsubscribe = queryClient.getQueryCache().subscribe(_core_notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        checkIsFetching(queryClient, filtersRef.current, isFetchingRef.current, setIsFetching);\n      }\n    }));\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, [queryClient]);\n  return isFetching;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvdXNlSXNGZXRjaGluZy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEI7QUFDNEI7QUFDTjtBQUNPOztBQUV2RDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVPO0FBQ1AsbUJBQW1CLG1EQUFZO0FBQy9CLG9CQUFvQixvRUFBYzs7QUFFbEMseUJBQXlCLDREQUFlO0FBQ3hDOztBQUVBLHdCQUF3QixxREFBYztBQUN0QztBQUNBOztBQUVBLG1CQUFtQixtREFBWTtBQUMvQjtBQUNBLHNCQUFzQixtREFBWTtBQUNsQztBQUNBLEVBQUUsc0RBQWU7QUFDakI7QUFDQTtBQUNBLDREQUE0RCw4REFBYTtBQUN6RTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXNlY3VyaXR5LXNjYW5uZXItZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvdXNlSXNGZXRjaGluZy5qcz8xNzg3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBub3RpZnlNYW5hZ2VyIH0gZnJvbSAnLi4vY29yZS9ub3RpZnlNYW5hZ2VyJztcbmltcG9ydCB7IHBhcnNlRmlsdGVyQXJncyB9IGZyb20gJy4uL2NvcmUvdXRpbHMnO1xuaW1wb3J0IHsgdXNlUXVlcnlDbGllbnQgfSBmcm9tICcuL1F1ZXJ5Q2xpZW50UHJvdmlkZXInO1xuXG52YXIgY2hlY2tJc0ZldGNoaW5nID0gZnVuY3Rpb24gY2hlY2tJc0ZldGNoaW5nKHF1ZXJ5Q2xpZW50LCBmaWx0ZXJzLCBpc0ZldGNoaW5nLCBzZXRJc0ZldGNoaW5nKSB7XG4gIHZhciBuZXdJc0ZldGNoaW5nID0gcXVlcnlDbGllbnQuaXNGZXRjaGluZyhmaWx0ZXJzKTtcblxuICBpZiAoaXNGZXRjaGluZyAhPT0gbmV3SXNGZXRjaGluZykge1xuICAgIHNldElzRmV0Y2hpbmcobmV3SXNGZXRjaGluZyk7XG4gIH1cbn07XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VJc0ZldGNoaW5nKGFyZzEsIGFyZzIpIHtcbiAgdmFyIG1vdW50ZWRSZWYgPSBSZWFjdC51c2VSZWYoZmFsc2UpO1xuICB2YXIgcXVlcnlDbGllbnQgPSB1c2VRdWVyeUNsaWVudCgpO1xuXG4gIHZhciBfcGFyc2VGaWx0ZXJBcmdzID0gcGFyc2VGaWx0ZXJBcmdzKGFyZzEsIGFyZzIpLFxuICAgICAgZmlsdGVycyA9IF9wYXJzZUZpbHRlckFyZ3NbMF07XG5cbiAgdmFyIF9SZWFjdCR1c2VTdGF0ZSA9IFJlYWN0LnVzZVN0YXRlKHF1ZXJ5Q2xpZW50LmlzRmV0Y2hpbmcoZmlsdGVycykpLFxuICAgICAgaXNGZXRjaGluZyA9IF9SZWFjdCR1c2VTdGF0ZVswXSxcbiAgICAgIHNldElzRmV0Y2hpbmcgPSBfUmVhY3QkdXNlU3RhdGVbMV07XG5cbiAgdmFyIGZpbHRlcnNSZWYgPSBSZWFjdC51c2VSZWYoZmlsdGVycyk7XG4gIGZpbHRlcnNSZWYuY3VycmVudCA9IGZpbHRlcnM7XG4gIHZhciBpc0ZldGNoaW5nUmVmID0gUmVhY3QudXNlUmVmKGlzRmV0Y2hpbmcpO1xuICBpc0ZldGNoaW5nUmVmLmN1cnJlbnQgPSBpc0ZldGNoaW5nO1xuICBSZWFjdC51c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgIG1vdW50ZWRSZWYuY3VycmVudCA9IHRydWU7XG4gICAgY2hlY2tJc0ZldGNoaW5nKHF1ZXJ5Q2xpZW50LCBmaWx0ZXJzUmVmLmN1cnJlbnQsIGlzRmV0Y2hpbmdSZWYuY3VycmVudCwgc2V0SXNGZXRjaGluZyk7XG4gICAgdmFyIHVuc3Vic2NyaWJlID0gcXVlcnlDbGllbnQuZ2V0UXVlcnlDYWNoZSgpLnN1YnNjcmliZShub3RpZnlNYW5hZ2VyLmJhdGNoQ2FsbHMoZnVuY3Rpb24gKCkge1xuICAgICAgaWYgKG1vdW50ZWRSZWYuY3VycmVudCkge1xuICAgICAgICBjaGVja0lzRmV0Y2hpbmcocXVlcnlDbGllbnQsIGZpbHRlcnNSZWYuY3VycmVudCwgaXNGZXRjaGluZ1JlZi5jdXJyZW50LCBzZXRJc0ZldGNoaW5nKTtcbiAgICAgIH1cbiAgICB9KSk7XG4gICAgcmV0dXJuIGZ1bmN0aW9uICgpIHtcbiAgICAgIG1vdW50ZWRSZWYuY3VycmVudCA9IGZhbHNlO1xuICAgICAgdW5zdWJzY3JpYmUoKTtcbiAgICB9O1xuICB9LCBbcXVlcnlDbGllbnRdKTtcbiAgcmV0dXJuIGlzRmV0Y2hpbmc7XG59Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/useIsFetching.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/useIsMutating.js":
/*!************************************************************!*\
  !*** ./node_modules/react-query/es/react/useIsMutating.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useIsMutating: () => (/* binding */ useIsMutating)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _core_notifyManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../core/notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _core_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../core/utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _QueryClientProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QueryClientProvider */ \"(ssr)/./node_modules/react-query/es/react/QueryClientProvider.js\");\n\n\n\n\nfunction useIsMutating(arg1, arg2) {\n  var mountedRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(false);\n  var filters = (0,_core_utils__WEBPACK_IMPORTED_MODULE_1__.parseMutationFilterArgs)(arg1, arg2);\n  var queryClient = (0,_QueryClientProvider__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_0___default().useState(queryClient.isMutating(filters)),\n      isMutating = _React$useState[0],\n      setIsMutating = _React$useState[1];\n\n  var filtersRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(filters);\n  filtersRef.current = filters;\n  var isMutatingRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(isMutating);\n  isMutatingRef.current = isMutating;\n  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(function () {\n    mountedRef.current = true;\n    var unsubscribe = queryClient.getMutationCache().subscribe(_core_notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        var newIsMutating = queryClient.isMutating(filtersRef.current);\n\n        if (isMutatingRef.current !== newIsMutating) {\n          setIsMutating(newIsMutating);\n        }\n      }\n    }));\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, [queryClient]);\n  return isMutating;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/useIsMutating.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/useMutation.js":
/*!**********************************************************!*\
  !*** ./node_modules/react-query/es/react/useMutation.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMutation: () => (/* binding */ useMutation)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _core_notifyManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../core/notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _core_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../core/utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _core_mutationObserver__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../core/mutationObserver */ \"(ssr)/./node_modules/react-query/es/core/mutationObserver.js\");\n/* harmony import */ var _QueryClientProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QueryClientProvider */ \"(ssr)/./node_modules/react-query/es/react/QueryClientProvider.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-query/es/react/utils.js\");\n\n\n\n\n\n\n // HOOK\n\nfunction useMutation(arg1, arg2, arg3) {\n  var mountedRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(false);\n\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_1___default().useState(0),\n      forceUpdate = _React$useState[1];\n\n  var options = (0,_core_utils__WEBPACK_IMPORTED_MODULE_2__.parseMutationArgs)(arg1, arg2, arg3);\n  var queryClient = (0,_QueryClientProvider__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n  var obsRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef();\n\n  if (!obsRef.current) {\n    obsRef.current = new _core_mutationObserver__WEBPACK_IMPORTED_MODULE_4__.MutationObserver(queryClient, options);\n  } else {\n    obsRef.current.setOptions(options);\n  }\n\n  var currentResult = obsRef.current.getCurrentResult();\n  react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(function () {\n    mountedRef.current = true;\n    var unsubscribe = obsRef.current.subscribe(_core_notifyManager__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        forceUpdate(function (x) {\n          return x + 1;\n        });\n      }\n    }));\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, []);\n  var mutate = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback(function (variables, mutateOptions) {\n    obsRef.current.mutate(variables, mutateOptions).catch(_core_utils__WEBPACK_IMPORTED_MODULE_2__.noop);\n  }, []);\n\n  if (currentResult.error && (0,_utils__WEBPACK_IMPORTED_MODULE_6__.shouldThrowError)(undefined, obsRef.current.options.useErrorBoundary, [currentResult.error])) {\n    throw currentResult.error;\n  }\n\n  return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, currentResult, {\n    mutate: mutate,\n    mutateAsync: currentResult.mutate\n  });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/useMutation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/useQueries.js":
/*!*********************************************************!*\
  !*** ./node_modules/react-query/es/react/useQueries.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useQueries: () => (/* binding */ useQueries)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _core_notifyManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../core/notifyManager */ \"(ssr)/./node_modules/react-query/es/core/notifyManager.js\");\n/* harmony import */ var _core_queriesObserver__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../core/queriesObserver */ \"(ssr)/./node_modules/react-query/es/core/queriesObserver.js\");\n/* harmony import */ var _QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider */ \"(ssr)/./node_modules/react-query/es/react/QueryClientProvider.js\");\n\n\n\n\nfunction useQueries(queries) {\n  var mountedRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(false);\n\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_0___default().useState(0),\n      forceUpdate = _React$useState[1];\n\n  var queryClient = (0,_QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n  var defaultedQueries = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {\n    return queries.map(function (options) {\n      var defaultedOptions = queryClient.defaultQueryObserverOptions(options); // Make sure the results are already in fetching state before subscribing or updating options\n\n      defaultedOptions.optimisticResults = true;\n      return defaultedOptions;\n    });\n  }, [queries, queryClient]);\n\n  var _React$useState2 = react__WEBPACK_IMPORTED_MODULE_0___default().useState(function () {\n    return new _core_queriesObserver__WEBPACK_IMPORTED_MODULE_2__.QueriesObserver(queryClient, defaultedQueries);\n  }),\n      observer = _React$useState2[0];\n\n  var result = observer.getOptimisticResult(defaultedQueries);\n  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(function () {\n    mountedRef.current = true;\n    var unsubscribe = observer.subscribe(_core_notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        forceUpdate(function (x) {\n          return x + 1;\n        });\n      }\n    }));\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, [observer]);\n  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(function () {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setQueries(defaultedQueries, {\n      listeners: false\n    });\n  }, [defaultedQueries, observer]);\n  return result;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/useQueries.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/useQuery.js":
/*!*******************************************************!*\
  !*** ./node_modules/react-query/es/react/useQuery.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useQuery: () => (/* binding */ useQuery)\n/* harmony export */ });\n/* harmony import */ var _core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../core */ \"(ssr)/./node_modules/react-query/es/core/queryObserver.js\");\n/* harmony import */ var _core_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/utils */ \"(ssr)/./node_modules/react-query/es/core/utils.js\");\n/* harmony import */ var _useBaseQuery__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useBaseQuery */ \"(ssr)/./node_modules/react-query/es/react/useBaseQuery.js\");\n\n\n // HOOK\n\nfunction useQuery(arg1, arg2, arg3) {\n  var parsedOptions = (0,_core_utils__WEBPACK_IMPORTED_MODULE_0__.parseQueryArgs)(arg1, arg2, arg3);\n  return (0,_useBaseQuery__WEBPACK_IMPORTED_MODULE_1__.useBaseQuery)(parsedOptions, _core__WEBPACK_IMPORTED_MODULE_2__.QueryObserver);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvdXNlUXVlcnkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUF3QztBQUNPO0FBQ0QsQ0FBQzs7QUFFeEM7QUFDUCxzQkFBc0IsMkRBQWM7QUFDcEMsU0FBUywyREFBWSxnQkFBZ0IsZ0RBQWE7QUFDbEQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1zZWN1cml0eS1zY2FubmVyLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3JlYWN0LXF1ZXJ5L2VzL3JlYWN0L3VzZVF1ZXJ5LmpzPzRjMjgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUXVlcnlPYnNlcnZlciB9IGZyb20gJy4uL2NvcmUnO1xuaW1wb3J0IHsgcGFyc2VRdWVyeUFyZ3MgfSBmcm9tICcuLi9jb3JlL3V0aWxzJztcbmltcG9ydCB7IHVzZUJhc2VRdWVyeSB9IGZyb20gJy4vdXNlQmFzZVF1ZXJ5JzsgLy8gSE9PS1xuXG5leHBvcnQgZnVuY3Rpb24gdXNlUXVlcnkoYXJnMSwgYXJnMiwgYXJnMykge1xuICB2YXIgcGFyc2VkT3B0aW9ucyA9IHBhcnNlUXVlcnlBcmdzKGFyZzEsIGFyZzIsIGFyZzMpO1xuICByZXR1cm4gdXNlQmFzZVF1ZXJ5KHBhcnNlZE9wdGlvbnMsIFF1ZXJ5T2JzZXJ2ZXIpO1xufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/useQuery.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-query/es/react/utils.js":
/*!****************************************************!*\
  !*** ./node_modules/react-query/es/react/utils.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   shouldThrowError: () => (/* binding */ shouldThrowError)\n/* harmony export */ });\nfunction shouldThrowError(suspense, _useErrorBoundary, params) {\n  // Allow useErrorBoundary function to override throwing behavior on a per-error basis\n  if (typeof _useErrorBoundary === 'function') {\n    return _useErrorBoundary.apply(void 0, params);\n  } // Allow useErrorBoundary to override suspense's throwing behavior\n\n\n  if (typeof _useErrorBoundary === 'boolean') return _useErrorBoundary; // If suspense is enabled default to throwing errors\n\n  return !!suspense;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVlcnkvZXMvcmVhY3QvdXRpbHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsSUFBSTs7O0FBR0osd0VBQXdFOztBQUV4RTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktc2VjdXJpdHktc2Nhbm5lci1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9yZWFjdC1xdWVyeS9lcy9yZWFjdC91dGlscy5qcz8yNzZiIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBzaG91bGRUaHJvd0Vycm9yKHN1c3BlbnNlLCBfdXNlRXJyb3JCb3VuZGFyeSwgcGFyYW1zKSB7XG4gIC8vIEFsbG93IHVzZUVycm9yQm91bmRhcnkgZnVuY3Rpb24gdG8gb3ZlcnJpZGUgdGhyb3dpbmcgYmVoYXZpb3Igb24gYSBwZXItZXJyb3IgYmFzaXNcbiAgaWYgKHR5cGVvZiBfdXNlRXJyb3JCb3VuZGFyeSA9PT0gJ2Z1bmN0aW9uJykge1xuICAgIHJldHVybiBfdXNlRXJyb3JCb3VuZGFyeS5hcHBseSh2b2lkIDAsIHBhcmFtcyk7XG4gIH0gLy8gQWxsb3cgdXNlRXJyb3JCb3VuZGFyeSB0byBvdmVycmlkZSBzdXNwZW5zZSdzIHRocm93aW5nIGJlaGF2aW9yXG5cblxuICBpZiAodHlwZW9mIF91c2VFcnJvckJvdW5kYXJ5ID09PSAnYm9vbGVhbicpIHJldHVybiBfdXNlRXJyb3JCb3VuZGFyeTsgLy8gSWYgc3VzcGVuc2UgaXMgZW5hYmxlZCBkZWZhdWx0IHRvIHRocm93aW5nIGVycm9yc1xuXG4gIHJldHVybiAhIXN1c3BlbnNlO1xufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-query/es/react/utils.js\n");

/***/ })

};
;