(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[185],{91971:function(e,n,i){Promise.resolve().then(i.t.bind(i,14811,23)),Promise.resolve().then(i.t.bind(i,2778,23)),Promise.resolve().then(i.bind(i,94043))},94043:function(e,n,i){"use strict";i.d(n,{Providers:function(){return m}});var o=i(57437),r=i(86484),a=i(25360),t=i(69064),l=i(2265);i(27499);var s=i(42921),c=i(40257);let u={api:{baseUrl:c.env.NEXT_PUBLIC_API_URL||"http://localhost:3001",timeout:3e4},aws:{region:c.env.NEXT_PUBLIC_REGION||"us-east-1",userPoolId:c.env.NEXT_PUBLIC_USER_POOL_ID||"",userPoolClientId:c.env.NEXT_PUBLIC_USER_POOL_CLIENT_ID||"",identityPoolId:c.env.NEXT_PUBLIC_IDENTITY_POOL_ID||""},app:{name:"AI Security Scanner",version:"1.0.0",environment:c.env.NEXT_PUBLIC_ENVIRONMENT||"development",isDevelopment:"development"===c.env.NEXT_PUBLIC_ENVIRONMENT,isProduction:"production"===c.env.NEXT_PUBLIC_ENVIRONMENT},upload:{maxFileSize:52428800,allowedTypes:["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/msword","text/plain","text/csv","application/json","application/xml","text/xml","application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","image/jpeg","image/png","image/gif","image/bmp","image/tiff"],allowedExtensions:[".pdf",".docx",".doc",".txt",".csv",".json",".xml",".xls",".xlsx",".jpg",".jpeg",".png",".gif",".bmp",".tiff"]},scan:{types:[{value:"security",label:"Security Only",description:"Scan for security threats and vulnerabilities"},{value:"compliance",label:"Compliance Only",description:"Check for PII and compliance violations"},{value:"anomaly",label:"Anomaly Only",description:"Detect unusual patterns and behaviors"},{value:"full",label:"Full Scan",description:"Complete security, compliance, and anomaly analysis"}],statusColors:{pending_upload:"gray",uploaded:"blue",processing:"yellow",completed:"green",failed:"red",cancelled:"gray"},riskColors:{MINIMAL:"green",LOW:"green",MEDIUM:"yellow",HIGH:"orange",CRITICAL:"red"}},ui:{pageSize:20,debounceDelay:300,toastDuration:5e3,animationDuration:300},features:{enableAnalytics:!1,enableNotifications:!0,enableDarkMode:!1,enableExport:!0,enableSharing:!1}};(()=>{let e=["NEXT_PUBLIC_API_URL","NEXT_PUBLIC_USER_POOL_ID","NEXT_PUBLIC_USER_POOL_CLIENT_ID","NEXT_PUBLIC_IDENTITY_POOL_ID","NEXT_PUBLIC_REGION"].filter(e=>!c.env[e]);if(e.length>0&&(console.warn("Missing required environment variables:",e),u.app.isProduction))throw Error("Missing required environment variables: ".concat(e.join(", ")))})();let d={Auth:{Cognito:{userPoolId:u.aws.userPoolId,userPoolClientId:u.aws.userPoolClientId,identityPoolId:u.aws.identityPoolId,loginWith:{email:!0},signUpVerificationMethod:"code",userAttributes:{email:{required:!0},given_name:{required:!0},family_name:{required:!0}},allowGuestAccess:!1,passwordFormat:{minLength:8,requireLowercase:!0,requireUppercase:!0,requireNumbers:!0,requireSpecialCharacters:!0}}},API:{REST:{"ai-scanner-api":{endpoint:u.api.baseUrl,region:u.aws.region}}},Storage:{S3:{region:u.aws.region,bucket:"ai-scanner-uploads"}}};s.V.configure(d);let p=()=>new r.QueryClient({defaultOptions:{queries:{retry:3,retryDelay:e=>Math.min(1e3*2**e,3e4),staleTime:3e5,cacheTime:6e5,refetchOnWindowFocus:!1},mutations:{retry:1}}});function m(e){let{children:n}=e,[i]=(0,l.useState)(()=>p());return(0,o.jsx)(r.QueryClientProvider,{client:i,children:(0,o.jsxs)(a._.Provider,{children:[n,(0,o.jsx)(t.x7,{position:"top-right",toastOptions:{duration:5e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#10B981",secondary:"#fff"}},error:{duration:5e3,iconTheme:{primary:"#EF4444",secondary:"#fff"}}}})]})})}},2778:function(){}},function(e){e.O(0,[2,396,360,638,971,117,744],function(){return e(e.s=91971)}),_N_E=e.O()}]);