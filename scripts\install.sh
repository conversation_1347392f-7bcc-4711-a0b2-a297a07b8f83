#!/bin/bash

# AWS AI Security Scanner - Installation Script
# This script sets up the development environment and deploys the application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Node.js
    if command_exists node; then
        NODE_VERSION=$(node --version)
        print_success "Node.js found: $NODE_VERSION"
    else
        print_error "Node.js is not installed. Please install Node.js 18.x or later."
        exit 1
    fi
    
    # Check npm
    if command_exists npm; then
        NPM_VERSION=$(npm --version)
        print_success "npm found: $NPM_VERSION"
    else
        print_error "npm is not installed. Please install npm."
        exit 1
    fi
    
    # Check Python
    if command_exists python3; then
        PYTHON_VERSION=$(python3 --version)
        print_success "Python found: $PYTHON_VERSION"
    else
        print_error "Python 3.9+ is not installed. Please install Python."
        exit 1
    fi
    
    # Check AWS CLI
    if command_exists aws; then
        AWS_VERSION=$(aws --version)
        print_success "AWS CLI found: $AWS_VERSION"
    else
        print_error "AWS CLI is not installed. Please install AWS CLI v2."
        exit 1
    fi
    
    # Check AWS credentials
    if aws sts get-caller-identity >/dev/null 2>&1; then
        ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
        print_success "AWS credentials configured for account: $ACCOUNT_ID"
    else
        print_error "AWS credentials not configured. Please run 'aws configure'."
        exit 1
    fi
}

# Function to install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Install root dependencies
    print_status "Installing root dependencies..."
    npm install
    
    # Install infrastructure dependencies
    print_status "Installing infrastructure dependencies..."
    cd infrastructure
    npm install
    cd ..
    
    # Install frontend dependencies
    print_status "Installing frontend dependencies..."
    cd frontend
    npm install
    cd ..
    
    # Install Python dependencies for backend functions
    print_status "Installing Python dependencies..."
    for dir in backend/functions/*/; do
        if [ -f "$dir/requirements.txt" ]; then
            print_status "Installing dependencies for $(basename "$dir")"
            cd "$dir"
            pip3 install -r requirements.txt
            cd ../../..
        fi
    done
    
    print_success "All dependencies installed successfully!"
}

# Function to setup environment
setup_environment() {
    print_status "Setting up environment..."
    
    # Get AWS account ID and region
    ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
    REGION=$(aws configure get region)
    
    if [ -z "$REGION" ]; then
        REGION="us-east-1"
        print_warning "No default region found, using us-east-1"
    fi
    
    # Create .env file if it doesn't exist
    if [ ! -f .env ]; then
        print_status "Creating .env file..."
        cat > .env << EOF
# AWS Configuration
AWS_ACCOUNT_ID=$ACCOUNT_ID
AWS_REGION=$REGION
CDK_DEFAULT_ACCOUNT=$ACCOUNT_ID
CDK_DEFAULT_REGION=$REGION

# Environment
ENVIRONMENT=dev

# Notifications (update with your email)
ALERT_EMAIL=<EMAIL>

# Production only (uncomment and update for production)
# CERTIFICATE_ARN=arn:aws:acm:us-east-1:$ACCOUNT_ID:certificate/********-1234-1234-1234-************
# DOMAIN_NAME=ai-security-scanner.com
EOF
        print_success ".env file created. Please update ALERT_EMAIL with your email address."
    else
        print_warning ".env file already exists. Skipping creation."
    fi
}

# Function to bootstrap CDK
bootstrap_cdk() {
    print_status "Bootstrapping AWS CDK..."
    
    cd infrastructure
    
    # Install CDK globally if not present
    if ! command_exists cdk; then
        print_status "Installing AWS CDK globally..."
        npm install -g aws-cdk
    fi
    
    # Bootstrap CDK
    print_status "Bootstrapping CDK for account $ACCOUNT_ID in region $REGION..."
    npx cdk bootstrap
    
    cd ..
    print_success "CDK bootstrap completed!"
}

# Function to deploy infrastructure
deploy_infrastructure() {
    print_status "Deploying infrastructure..."
    
    cd infrastructure
    
    # Build the project
    print_status "Building infrastructure..."
    npm run build
    
    # Deploy all stacks
    print_status "Deploying CDK stacks..."
    npx cdk deploy --all --context environment=dev --require-approval never
    
    cd ..
    print_success "Infrastructure deployed successfully!"
}

# Function to build and deploy frontend
deploy_frontend() {
    print_status "Building and deploying frontend..."
    
    cd frontend
    
    # Build the frontend
    print_status "Building React application..."
    npm run build
    
    # Get the S3 bucket name from CDK outputs
    BUCKET_NAME="ai-scanner-frontend-dev-$ACCOUNT_ID"
    
    # Deploy to S3
    print_status "Deploying to S3 bucket: $BUCKET_NAME"
    aws s3 sync out/ s3://$BUCKET_NAME/ --delete
    
    # Get CloudFront distribution ID and invalidate cache
    DISTRIBUTION_ID=$(aws cloudformation describe-stacks \
        --stack-name AiSecurityScanner-dev-Frontend \
        --query 'Stacks[0].Outputs[?OutputKey==`CloudFrontDistributionId`].OutputValue' \
        --output text 2>/dev/null || echo "")
    
    if [ -n "$DISTRIBUTION_ID" ]; then
        print_status "Invalidating CloudFront cache..."
        aws cloudfront create-invalidation \
            --distribution-id $DISTRIBUTION_ID \
            --paths "/*" >/dev/null
    fi
    
    cd ..
    print_success "Frontend deployed successfully!"
}

# Function to get application URLs
get_application_urls() {
    print_status "Getting application URLs..."
    
    # Get CloudFront URL
    CLOUDFRONT_URL=$(aws cloudformation describe-stacks \
        --stack-name AiSecurityScanner-dev-Frontend \
        --query 'Stacks[0].Outputs[?OutputKey==`CloudFrontUrl`].OutputValue' \
        --output text 2>/dev/null || echo "Not found")
    
    # Get API Gateway URL
    API_URL=$(aws cloudformation describe-stacks \
        --stack-name AiSecurityScanner-dev-Backend \
        --query 'Stacks[0].Outputs[?OutputKey==`ApiGatewayUrl`].OutputValue' \
        --output text 2>/dev/null || echo "Not found")
    
    echo ""
    print_success "🎉 Deployment completed successfully!"
    echo ""
    echo "Application URLs:"
    echo "  Frontend: $CLOUDFRONT_URL"
    echo "  API:      $API_URL"
    echo ""
    echo "Next steps:"
    echo "  1. Open the frontend URL in your browser"
    echo "  2. Create an account or sign in"
    echo "  3. Upload a test file to try the scanner"
    echo "  4. Check the monitoring dashboard in AWS CloudWatch"
    echo ""
}

# Function to run tests
run_tests() {
    print_status "Running tests..."
    
    # Test infrastructure
    cd infrastructure
    if [ -f "package.json" ] && grep -q '"test"' package.json; then
        print_status "Running infrastructure tests..."
        npm test
    fi
    cd ..
    
    # Test frontend
    cd frontend
    if [ -f "package.json" ] && grep -q '"test"' package.json; then
        print_status "Running frontend tests..."
        npm test -- --watchAll=false
    fi
    cd ..
    
    print_success "Tests completed!"
}

# Main installation function
main() {
    echo ""
    echo "🛡️  AWS AI Security & Compliance Scanner"
    echo "========================================="
    echo ""
    
    # Parse command line arguments
    SKIP_TESTS=false
    SKIP_DEPLOY=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-tests)
                SKIP_TESTS=true
                shift
                ;;
            --skip-deploy)
                SKIP_DEPLOY=true
                shift
                ;;
            --help)
                echo "Usage: $0 [options]"
                echo ""
                echo "Options:"
                echo "  --skip-tests    Skip running tests"
                echo "  --skip-deploy   Skip deployment (setup only)"
                echo "  --help          Show this help message"
                echo ""
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                echo "Use --help for usage information"
                exit 1
                ;;
        esac
    done
    
    # Run installation steps
    check_prerequisites
    install_dependencies
    setup_environment
    
    if [ "$SKIP_TESTS" = false ]; then
        run_tests
    fi
    
    if [ "$SKIP_DEPLOY" = false ]; then
        bootstrap_cdk
        deploy_infrastructure
        deploy_frontend
        get_application_urls
    else
        print_success "Setup completed! Run without --skip-deploy to deploy."
    fi
}

# Run main function
main "$@"
