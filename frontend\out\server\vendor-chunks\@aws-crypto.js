"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-crypto";
exports.ids = ["vendor-chunks/@aws-crypto"];
exports.modules = {

/***/ "(ssr)/./node_modules/@aws-crypto/sha256-js/build/module/RawSha256.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@aws-crypto/sha256-js/build/module/RawSha256.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RawSha256: () => (/* binding */ RawSha256)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/@aws-crypto/sha256-js/build/module/constants.js\");\n\n/**\n * @internal\n */\nvar RawSha256 = /** @class */ (function () {\n    function RawSha256() {\n        this.state = Int32Array.from(_constants__WEBPACK_IMPORTED_MODULE_0__.INIT);\n        this.temp = new Int32Array(64);\n        this.buffer = new Uint8Array(64);\n        this.bufferLength = 0;\n        this.bytesHashed = 0;\n        /**\n         * @internal\n         */\n        this.finished = false;\n    }\n    RawSha256.prototype.update = function (data) {\n        if (this.finished) {\n            throw new Error(\"Attempted to update an already finished hash.\");\n        }\n        var position = 0;\n        var byteLength = data.byteLength;\n        this.bytesHashed += byteLength;\n        if (this.bytesHashed * 8 > _constants__WEBPACK_IMPORTED_MODULE_0__.MAX_HASHABLE_LENGTH) {\n            throw new Error(\"Cannot hash more than 2^53 - 1 bits\");\n        }\n        while (byteLength > 0) {\n            this.buffer[this.bufferLength++] = data[position++];\n            byteLength--;\n            if (this.bufferLength === _constants__WEBPACK_IMPORTED_MODULE_0__.BLOCK_SIZE) {\n                this.hashBuffer();\n                this.bufferLength = 0;\n            }\n        }\n    };\n    RawSha256.prototype.digest = function () {\n        if (!this.finished) {\n            var bitsHashed = this.bytesHashed * 8;\n            var bufferView = new DataView(this.buffer.buffer, this.buffer.byteOffset, this.buffer.byteLength);\n            var undecoratedLength = this.bufferLength;\n            bufferView.setUint8(this.bufferLength++, 0x80);\n            // Ensure the final block has enough room for the hashed length\n            if (undecoratedLength % _constants__WEBPACK_IMPORTED_MODULE_0__.BLOCK_SIZE >= _constants__WEBPACK_IMPORTED_MODULE_0__.BLOCK_SIZE - 8) {\n                for (var i = this.bufferLength; i < _constants__WEBPACK_IMPORTED_MODULE_0__.BLOCK_SIZE; i++) {\n                    bufferView.setUint8(i, 0);\n                }\n                this.hashBuffer();\n                this.bufferLength = 0;\n            }\n            for (var i = this.bufferLength; i < _constants__WEBPACK_IMPORTED_MODULE_0__.BLOCK_SIZE - 8; i++) {\n                bufferView.setUint8(i, 0);\n            }\n            bufferView.setUint32(_constants__WEBPACK_IMPORTED_MODULE_0__.BLOCK_SIZE - 8, Math.floor(bitsHashed / 0x100000000), true);\n            bufferView.setUint32(_constants__WEBPACK_IMPORTED_MODULE_0__.BLOCK_SIZE - 4, bitsHashed);\n            this.hashBuffer();\n            this.finished = true;\n        }\n        // The value in state is little-endian rather than big-endian, so flip\n        // each word into a new Uint8Array\n        var out = new Uint8Array(_constants__WEBPACK_IMPORTED_MODULE_0__.DIGEST_LENGTH);\n        for (var i = 0; i < 8; i++) {\n            out[i * 4] = (this.state[i] >>> 24) & 0xff;\n            out[i * 4 + 1] = (this.state[i] >>> 16) & 0xff;\n            out[i * 4 + 2] = (this.state[i] >>> 8) & 0xff;\n            out[i * 4 + 3] = (this.state[i] >>> 0) & 0xff;\n        }\n        return out;\n    };\n    RawSha256.prototype.hashBuffer = function () {\n        var _a = this, buffer = _a.buffer, state = _a.state;\n        var state0 = state[0], state1 = state[1], state2 = state[2], state3 = state[3], state4 = state[4], state5 = state[5], state6 = state[6], state7 = state[7];\n        for (var i = 0; i < _constants__WEBPACK_IMPORTED_MODULE_0__.BLOCK_SIZE; i++) {\n            if (i < 16) {\n                this.temp[i] =\n                    ((buffer[i * 4] & 0xff) << 24) |\n                        ((buffer[i * 4 + 1] & 0xff) << 16) |\n                        ((buffer[i * 4 + 2] & 0xff) << 8) |\n                        (buffer[i * 4 + 3] & 0xff);\n            }\n            else {\n                var u = this.temp[i - 2];\n                var t1_1 = ((u >>> 17) | (u << 15)) ^ ((u >>> 19) | (u << 13)) ^ (u >>> 10);\n                u = this.temp[i - 15];\n                var t2_1 = ((u >>> 7) | (u << 25)) ^ ((u >>> 18) | (u << 14)) ^ (u >>> 3);\n                this.temp[i] =\n                    ((t1_1 + this.temp[i - 7]) | 0) + ((t2_1 + this.temp[i - 16]) | 0);\n            }\n            var t1 = ((((((state4 >>> 6) | (state4 << 26)) ^\n                ((state4 >>> 11) | (state4 << 21)) ^\n                ((state4 >>> 25) | (state4 << 7))) +\n                ((state4 & state5) ^ (~state4 & state6))) |\n                0) +\n                ((state7 + ((_constants__WEBPACK_IMPORTED_MODULE_0__.KEY[i] + this.temp[i]) | 0)) | 0)) |\n                0;\n            var t2 = ((((state0 >>> 2) | (state0 << 30)) ^\n                ((state0 >>> 13) | (state0 << 19)) ^\n                ((state0 >>> 22) | (state0 << 10))) +\n                ((state0 & state1) ^ (state0 & state2) ^ (state1 & state2))) |\n                0;\n            state7 = state6;\n            state6 = state5;\n            state5 = state4;\n            state4 = (state3 + t1) | 0;\n            state3 = state2;\n            state2 = state1;\n            state1 = state0;\n            state0 = (t1 + t2) | 0;\n        }\n        state[0] += state0;\n        state[1] += state1;\n        state[2] += state2;\n        state[3] += state3;\n        state[4] += state4;\n        state[5] += state5;\n        state[6] += state6;\n        state[7] += state7;\n    };\n    return RawSha256;\n}());\n\n//# sourceMappingURL=RawSha256.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-crypto/sha256-js/build/module/RawSha256.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-crypto/sha256-js/build/module/constants.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@aws-crypto/sha256-js/build/module/constants.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BLOCK_SIZE: () => (/* binding */ BLOCK_SIZE),\n/* harmony export */   DIGEST_LENGTH: () => (/* binding */ DIGEST_LENGTH),\n/* harmony export */   INIT: () => (/* binding */ INIT),\n/* harmony export */   KEY: () => (/* binding */ KEY),\n/* harmony export */   MAX_HASHABLE_LENGTH: () => (/* binding */ MAX_HASHABLE_LENGTH)\n/* harmony export */ });\n/**\n * @internal\n */\nvar BLOCK_SIZE = 64;\n/**\n * @internal\n */\nvar DIGEST_LENGTH = 32;\n/**\n * @internal\n */\nvar KEY = new Uint32Array([\n    0x428a2f98,\n    0x71374491,\n    0xb5c0fbcf,\n    0xe9b5dba5,\n    0x3956c25b,\n    0x59f111f1,\n    0x923f82a4,\n    0xab1c5ed5,\n    0xd807aa98,\n    0x12835b01,\n    0x243185be,\n    0x550c7dc3,\n    0x72be5d74,\n    0x80deb1fe,\n    0x9bdc06a7,\n    0xc19bf174,\n    0xe49b69c1,\n    0xefbe4786,\n    0x0fc19dc6,\n    0x240ca1cc,\n    0x2de92c6f,\n    0x4a7484aa,\n    0x5cb0a9dc,\n    0x76f988da,\n    0x983e5152,\n    0xa831c66d,\n    0xb00327c8,\n    0xbf597fc7,\n    0xc6e00bf3,\n    0xd5a79147,\n    0x06ca6351,\n    0x14292967,\n    0x27b70a85,\n    0x2e1b2138,\n    0x4d2c6dfc,\n    0x53380d13,\n    0x650a7354,\n    0x766a0abb,\n    0x81c2c92e,\n    0x92722c85,\n    0xa2bfe8a1,\n    0xa81a664b,\n    0xc24b8b70,\n    0xc76c51a3,\n    0xd192e819,\n    0xd6990624,\n    0xf40e3585,\n    0x106aa070,\n    0x19a4c116,\n    0x1e376c08,\n    0x2748774c,\n    0x34b0bcb5,\n    0x391c0cb3,\n    0x4ed8aa4a,\n    0x5b9cca4f,\n    0x682e6ff3,\n    0x748f82ee,\n    0x78a5636f,\n    0x84c87814,\n    0x8cc70208,\n    0x90befffa,\n    0xa4506ceb,\n    0xbef9a3f7,\n    0xc67178f2\n]);\n/**\n * @internal\n */\nvar INIT = [\n    0x6a09e667,\n    0xbb67ae85,\n    0x3c6ef372,\n    0xa54ff53a,\n    0x510e527f,\n    0x9b05688c,\n    0x1f83d9ab,\n    0x5be0cd19\n];\n/**\n * @internal\n */\nvar MAX_HASHABLE_LENGTH = Math.pow(2, 53) - 1;\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-crypto/sha256-js/build/module/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-crypto/sha256-js/build/module/index.js":
/*!******************************************************************!*\
  !*** ./node_modules/@aws-crypto/sha256-js/build/module/index.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sha256: () => (/* reexport safe */ _jsSha256__WEBPACK_IMPORTED_MODULE_0__.Sha256)\n/* harmony export */ });\n/* harmony import */ var _jsSha256__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./jsSha256 */ \"(ssr)/./node_modules/@aws-crypto/sha256-js/build/module/jsSha256.js\");\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGF3cy1jcnlwdG8vc2hhMjU2LWpzL2J1aWxkL21vZHVsZS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEyQjtBQUMzQiIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXNlY3VyaXR5LXNjYW5uZXItZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQGF3cy1jcnlwdG8vc2hhMjU2LWpzL2J1aWxkL21vZHVsZS9pbmRleC5qcz84YThmIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL2pzU2hhMjU2XCI7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-crypto/sha256-js/build/module/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-crypto/sha256-js/build/module/jsSha256.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@aws-crypto/sha256-js/build/module/jsSha256.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sha256: () => (/* binding */ Sha256)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/@aws-crypto/sha256-js/build/module/constants.js\");\n/* harmony import */ var _RawSha256__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./RawSha256 */ \"(ssr)/./node_modules/@aws-crypto/sha256-js/build/module/RawSha256.js\");\n/* harmony import */ var _aws_crypto_util__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @aws-crypto/util */ \"(ssr)/./node_modules/@aws-crypto/util/build/module/index.js\");\n\n\n\n\nvar Sha256 = /** @class */ (function () {\n    function Sha256(secret) {\n        this.secret = secret;\n        this.hash = new _RawSha256__WEBPACK_IMPORTED_MODULE_1__.RawSha256();\n        this.reset();\n    }\n    Sha256.prototype.update = function (toHash) {\n        if ((0,_aws_crypto_util__WEBPACK_IMPORTED_MODULE_2__.isEmptyData)(toHash) || this.error) {\n            return;\n        }\n        try {\n            this.hash.update((0,_aws_crypto_util__WEBPACK_IMPORTED_MODULE_2__.convertToBuffer)(toHash));\n        }\n        catch (e) {\n            this.error = e;\n        }\n    };\n    /* This synchronous method keeps compatibility\n     * with the v2 aws-sdk.\n     */\n    Sha256.prototype.digestSync = function () {\n        if (this.error) {\n            throw this.error;\n        }\n        if (this.outer) {\n            if (!this.outer.finished) {\n                this.outer.update(this.hash.digest());\n            }\n            return this.outer.digest();\n        }\n        return this.hash.digest();\n    };\n    /* The underlying digest method here is synchronous.\n     * To keep the same interface with the other hash functions\n     * the default is to expose this as an async method.\n     * However, it can sometimes be useful to have a sync method.\n     */\n    Sha256.prototype.digest = function () {\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_3__.__awaiter)(this, void 0, void 0, function () {\n            return (0,tslib__WEBPACK_IMPORTED_MODULE_3__.__generator)(this, function (_a) {\n                return [2 /*return*/, this.digestSync()];\n            });\n        });\n    };\n    Sha256.prototype.reset = function () {\n        this.hash = new _RawSha256__WEBPACK_IMPORTED_MODULE_1__.RawSha256();\n        if (this.secret) {\n            this.outer = new _RawSha256__WEBPACK_IMPORTED_MODULE_1__.RawSha256();\n            var inner = bufferFromSecret(this.secret);\n            var outer = new Uint8Array(_constants__WEBPACK_IMPORTED_MODULE_0__.BLOCK_SIZE);\n            outer.set(inner);\n            for (var i = 0; i < _constants__WEBPACK_IMPORTED_MODULE_0__.BLOCK_SIZE; i++) {\n                inner[i] ^= 0x36;\n                outer[i] ^= 0x5c;\n            }\n            this.hash.update(inner);\n            this.outer.update(outer);\n            // overwrite the copied key in memory\n            for (var i = 0; i < inner.byteLength; i++) {\n                inner[i] = 0;\n            }\n        }\n    };\n    return Sha256;\n}());\n\nfunction bufferFromSecret(secret) {\n    var input = (0,_aws_crypto_util__WEBPACK_IMPORTED_MODULE_2__.convertToBuffer)(secret);\n    if (input.byteLength > _constants__WEBPACK_IMPORTED_MODULE_0__.BLOCK_SIZE) {\n        var bufferHash = new _RawSha256__WEBPACK_IMPORTED_MODULE_1__.RawSha256();\n        bufferHash.update(input);\n        input = bufferHash.digest();\n    }\n    var buffer = new Uint8Array(_constants__WEBPACK_IMPORTED_MODULE_0__.BLOCK_SIZE);\n    buffer.set(input);\n    return buffer;\n}\n//# sourceMappingURL=jsSha256.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-crypto/sha256-js/build/module/jsSha256.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-crypto/util/build/module/convertToBuffer.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@aws-crypto/util/build/module/convertToBuffer.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertToBuffer: () => (/* binding */ convertToBuffer)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-utf8 */ \"(ssr)/./node_modules/@smithy/util-utf8/dist-es/index.js\");\n// Copyright Amazon.com Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n\n// Quick polyfill\nvar fromUtf8 = typeof Buffer !== \"undefined\" && Buffer.from\n    ? function (input) { return Buffer.from(input, \"utf8\"); }\n    : _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_0__.fromUtf8;\nfunction convertToBuffer(data) {\n    // Already a Uint8, do nothing\n    if (data instanceof Uint8Array)\n        return data;\n    if (typeof data === \"string\") {\n        return fromUtf8(data);\n    }\n    if (ArrayBuffer.isView(data)) {\n        return new Uint8Array(data.buffer, data.byteOffset, data.byteLength / Uint8Array.BYTES_PER_ELEMENT);\n    }\n    return new Uint8Array(data);\n}\n//# sourceMappingURL=convertToBuffer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGF3cy1jcnlwdG8vdXRpbC9idWlsZC9tb2R1bGUvY29udmVydFRvQnVmZmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNnRTtBQUNoRTtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCLE1BQU0sdURBQWU7QUFDZDtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXNlY3VyaXR5LXNjYW5uZXItZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQGF3cy1jcnlwdG8vdXRpbC9idWlsZC9tb2R1bGUvY29udmVydFRvQnVmZmVyLmpzP2NhNGMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IEFtYXpvbi5jb20gSW5jLiBvciBpdHMgYWZmaWxpYXRlcy4gQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbi8vIFNQRFgtTGljZW5zZS1JZGVudGlmaWVyOiBBcGFjaGUtMi4wXG5pbXBvcnQgeyBmcm9tVXRmOCBhcyBmcm9tVXRmOEJyb3dzZXIgfSBmcm9tIFwiQHNtaXRoeS91dGlsLXV0ZjhcIjtcbi8vIFF1aWNrIHBvbHlmaWxsXG52YXIgZnJvbVV0ZjggPSB0eXBlb2YgQnVmZmVyICE9PSBcInVuZGVmaW5lZFwiICYmIEJ1ZmZlci5mcm9tXG4gICAgPyBmdW5jdGlvbiAoaW5wdXQpIHsgcmV0dXJuIEJ1ZmZlci5mcm9tKGlucHV0LCBcInV0ZjhcIik7IH1cbiAgICA6IGZyb21VdGY4QnJvd3NlcjtcbmV4cG9ydCBmdW5jdGlvbiBjb252ZXJ0VG9CdWZmZXIoZGF0YSkge1xuICAgIC8vIEFscmVhZHkgYSBVaW50OCwgZG8gbm90aGluZ1xuICAgIGlmIChkYXRhIGluc3RhbmNlb2YgVWludDhBcnJheSlcbiAgICAgICAgcmV0dXJuIGRhdGE7XG4gICAgaWYgKHR5cGVvZiBkYXRhID09PSBcInN0cmluZ1wiKSB7XG4gICAgICAgIHJldHVybiBmcm9tVXRmOChkYXRhKTtcbiAgICB9XG4gICAgaWYgKEFycmF5QnVmZmVyLmlzVmlldyhkYXRhKSkge1xuICAgICAgICByZXR1cm4gbmV3IFVpbnQ4QXJyYXkoZGF0YS5idWZmZXIsIGRhdGEuYnl0ZU9mZnNldCwgZGF0YS5ieXRlTGVuZ3RoIC8gVWludDhBcnJheS5CWVRFU19QRVJfRUxFTUVOVCk7XG4gICAgfVxuICAgIHJldHVybiBuZXcgVWludDhBcnJheShkYXRhKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbnZlcnRUb0J1ZmZlci5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-crypto/util/build/module/convertToBuffer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-crypto/util/build/module/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/@aws-crypto/util/build/module/index.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertToBuffer: () => (/* reexport safe */ _convertToBuffer__WEBPACK_IMPORTED_MODULE_0__.convertToBuffer),\n/* harmony export */   isEmptyData: () => (/* reexport safe */ _isEmptyData__WEBPACK_IMPORTED_MODULE_1__.isEmptyData),\n/* harmony export */   numToUint8: () => (/* reexport safe */ _numToUint8__WEBPACK_IMPORTED_MODULE_2__.numToUint8),\n/* harmony export */   uint32ArrayFrom: () => (/* reexport safe */ _uint32ArrayFrom__WEBPACK_IMPORTED_MODULE_3__.uint32ArrayFrom)\n/* harmony export */ });\n/* harmony import */ var _convertToBuffer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./convertToBuffer */ \"(ssr)/./node_modules/@aws-crypto/util/build/module/convertToBuffer.js\");\n/* harmony import */ var _isEmptyData__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./isEmptyData */ \"(ssr)/./node_modules/@aws-crypto/util/build/module/isEmptyData.js\");\n/* harmony import */ var _numToUint8__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./numToUint8 */ \"(ssr)/./node_modules/@aws-crypto/util/build/module/numToUint8.js\");\n/* harmony import */ var _uint32ArrayFrom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./uint32ArrayFrom */ \"(ssr)/./node_modules/@aws-crypto/util/build/module/uint32ArrayFrom.js\");\n// Copyright Amazon.com Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGF3cy1jcnlwdG8vdXRpbC9idWlsZC9tb2R1bGUvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ29EO0FBQ1I7QUFDRjtBQUNVO0FBQ3BEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktc2VjdXJpdHktc2Nhbm5lci1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9AYXdzLWNyeXB0by91dGlsL2J1aWxkL21vZHVsZS9pbmRleC5qcz9lZGU0Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCBBbWF6b24uY29tIEluYy4gb3IgaXRzIGFmZmlsaWF0ZXMuIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4vLyBTUERYLUxpY2Vuc2UtSWRlbnRpZmllcjogQXBhY2hlLTIuMFxuZXhwb3J0IHsgY29udmVydFRvQnVmZmVyIH0gZnJvbSBcIi4vY29udmVydFRvQnVmZmVyXCI7XG5leHBvcnQgeyBpc0VtcHR5RGF0YSB9IGZyb20gXCIuL2lzRW1wdHlEYXRhXCI7XG5leHBvcnQgeyBudW1Ub1VpbnQ4IH0gZnJvbSBcIi4vbnVtVG9VaW50OFwiO1xuZXhwb3J0IHsgdWludDMyQXJyYXlGcm9tIH0gZnJvbSAnLi91aW50MzJBcnJheUZyb20nO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-crypto/util/build/module/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-crypto/util/build/module/isEmptyData.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@aws-crypto/util/build/module/isEmptyData.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isEmptyData: () => (/* binding */ isEmptyData)\n/* harmony export */ });\n// Copyright Amazon.com Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction isEmptyData(data) {\n    if (typeof data === \"string\") {\n        return data.length === 0;\n    }\n    return data.byteLength === 0;\n}\n//# sourceMappingURL=isEmptyData.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGF3cy1jcnlwdG8vdXRpbC9idWlsZC9tb2R1bGUvaXNFbXB0eURhdGEuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXNlY3VyaXR5LXNjYW5uZXItZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQGF3cy1jcnlwdG8vdXRpbC9idWlsZC9tb2R1bGUvaXNFbXB0eURhdGEuanM/MTI4NCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgQW1hem9uLmNvbSBJbmMuIG9yIGl0cyBhZmZpbGlhdGVzLiBBbGwgUmlnaHRzIFJlc2VydmVkLlxuLy8gU1BEWC1MaWNlbnNlLUlkZW50aWZpZXI6IEFwYWNoZS0yLjBcbmV4cG9ydCBmdW5jdGlvbiBpc0VtcHR5RGF0YShkYXRhKSB7XG4gICAgaWYgKHR5cGVvZiBkYXRhID09PSBcInN0cmluZ1wiKSB7XG4gICAgICAgIHJldHVybiBkYXRhLmxlbmd0aCA9PT0gMDtcbiAgICB9XG4gICAgcmV0dXJuIGRhdGEuYnl0ZUxlbmd0aCA9PT0gMDtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWlzRW1wdHlEYXRhLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-crypto/util/build/module/isEmptyData.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-crypto/util/build/module/numToUint8.js":
/*!******************************************************************!*\
  !*** ./node_modules/@aws-crypto/util/build/module/numToUint8.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   numToUint8: () => (/* binding */ numToUint8)\n/* harmony export */ });\n// Copyright Amazon.com Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction numToUint8(num) {\n    return new Uint8Array([\n        (num & 0xff000000) >> 24,\n        (num & 0x00ff0000) >> 16,\n        (num & 0x0000ff00) >> 8,\n        num & 0x000000ff,\n    ]);\n}\n//# sourceMappingURL=numToUint8.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGF3cy1jcnlwdG8vdXRpbC9idWlsZC9tb2R1bGUvbnVtVG9VaW50OC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXNlY3VyaXR5LXNjYW5uZXItZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQGF3cy1jcnlwdG8vdXRpbC9idWlsZC9tb2R1bGUvbnVtVG9VaW50OC5qcz82NDJlIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCBBbWF6b24uY29tIEluYy4gb3IgaXRzIGFmZmlsaWF0ZXMuIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4vLyBTUERYLUxpY2Vuc2UtSWRlbnRpZmllcjogQXBhY2hlLTIuMFxuZXhwb3J0IGZ1bmN0aW9uIG51bVRvVWludDgobnVtKSB7XG4gICAgcmV0dXJuIG5ldyBVaW50OEFycmF5KFtcbiAgICAgICAgKG51bSAmIDB4ZmYwMDAwMDApID4+IDI0LFxuICAgICAgICAobnVtICYgMHgwMGZmMDAwMCkgPj4gMTYsXG4gICAgICAgIChudW0gJiAweDAwMDBmZjAwKSA+PiA4LFxuICAgICAgICBudW0gJiAweDAwMDAwMGZmLFxuICAgIF0pO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bnVtVG9VaW50OC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-crypto/util/build/module/numToUint8.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-crypto/util/build/module/uint32ArrayFrom.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@aws-crypto/util/build/module/uint32ArrayFrom.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   uint32ArrayFrom: () => (/* binding */ uint32ArrayFrom)\n/* harmony export */ });\n// Copyright Amazon.com Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// IE 11 does not support Array.from, so we do it manually\nfunction uint32ArrayFrom(a_lookUpTable) {\n    if (!Uint32Array.from) {\n        var return_array = new Uint32Array(a_lookUpTable.length);\n        var a_index = 0;\n        while (a_index < a_lookUpTable.length) {\n            return_array[a_index] = a_lookUpTable[a_index];\n            a_index += 1;\n        }\n        return return_array;\n    }\n    return Uint32Array.from(a_lookUpTable);\n}\n//# sourceMappingURL=uint32ArrayFrom.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGF3cy1jcnlwdG8vdXRpbC9idWlsZC9tb2R1bGUvdWludDMyQXJyYXlGcm9tLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXNlY3VyaXR5LXNjYW5uZXItZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQGF3cy1jcnlwdG8vdXRpbC9idWlsZC9tb2R1bGUvdWludDMyQXJyYXlGcm9tLmpzPzQyYmYiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IEFtYXpvbi5jb20gSW5jLiBvciBpdHMgYWZmaWxpYXRlcy4gQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbi8vIFNQRFgtTGljZW5zZS1JZGVudGlmaWVyOiBBcGFjaGUtMi4wXG4vLyBJRSAxMSBkb2VzIG5vdCBzdXBwb3J0IEFycmF5LmZyb20sIHNvIHdlIGRvIGl0IG1hbnVhbGx5XG5leHBvcnQgZnVuY3Rpb24gdWludDMyQXJyYXlGcm9tKGFfbG9va1VwVGFibGUpIHtcbiAgICBpZiAoIVVpbnQzMkFycmF5LmZyb20pIHtcbiAgICAgICAgdmFyIHJldHVybl9hcnJheSA9IG5ldyBVaW50MzJBcnJheShhX2xvb2tVcFRhYmxlLmxlbmd0aCk7XG4gICAgICAgIHZhciBhX2luZGV4ID0gMDtcbiAgICAgICAgd2hpbGUgKGFfaW5kZXggPCBhX2xvb2tVcFRhYmxlLmxlbmd0aCkge1xuICAgICAgICAgICAgcmV0dXJuX2FycmF5W2FfaW5kZXhdID0gYV9sb29rVXBUYWJsZVthX2luZGV4XTtcbiAgICAgICAgICAgIGFfaW5kZXggKz0gMTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gcmV0dXJuX2FycmF5O1xuICAgIH1cbiAgICByZXR1cm4gVWludDMyQXJyYXkuZnJvbShhX2xvb2tVcFRhYmxlKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXVpbnQzMkFycmF5RnJvbS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-crypto/util/build/module/uint32ArrayFrom.js\n");

/***/ })

};
;