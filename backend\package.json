{"name": "aws-ai-security-scanner-backend", "version": "1.0.0", "description": "Backend Lambda functions for AI Security Scanner", "main": "index.js", "scripts": {"build": "npm run build:functions && npm run build:layers", "build:functions": "npm run build:auth && npm run build:upload && npm run build:orchestrator && npm run build:security && npm run build:pii && npm run build:anomaly && npm run build:report", "build:auth": "cd functions/auth && npm run build", "build:upload": "cd functions/upload && npm run build", "build:orchestrator": "cd functions/scan-orchestrator && npm run build", "build:security": "cd functions/security-scanner && npm run build", "build:pii": "cd functions/pii-detector && npm run build", "build:anomaly": "cd functions/anomaly-detector && npm run build", "build:report": "cd functions/report-generator && npm run build", "build:layers": "cd layers/common && npm run build", "install:all": "npm run install:functions && npm run install:layers", "install:functions": "npm run install:auth && npm run install:upload && npm run install:orchestrator && npm run install:security && npm run install:pii && npm run install:anomaly && npm run install:report", "install:auth": "cd functions/auth && npm install", "install:upload": "cd functions/upload && npm install", "install:orchestrator": "cd functions/scan-orchestrator && pip install -r requirements.txt -t .", "install:security": "cd functions/security-scanner && pip install -r requirements.txt -t .", "install:pii": "cd functions/pii-detector && pip install -r requirements.txt -t .", "install:anomaly": "cd functions/anomaly-detector && pip install -r requirements.txt -t .", "install:report": "cd functions/report-generator && pip install -r requirements.txt -t .", "install:layers": "cd layers/common && npm install", "test": "npm run test:functions", "test:functions": "npm run test:auth && npm run test:upload", "test:auth": "cd functions/auth && npm test", "test:upload": "cd functions/upload && npm test", "lint": "npm run lint:functions", "lint:functions": "npm run lint:auth && npm run lint:upload", "lint:auth": "cd functions/auth && npm run lint", "lint:upload": "cd functions/upload && npm run lint", "clean": "npm run clean:functions && npm run clean:layers", "clean:functions": "npm run clean:auth && npm run clean:upload && npm run clean:orchestrator && npm run clean:security && npm run clean:pii && npm run clean:anomaly && npm run clean:report", "clean:auth": "cd functions/auth && npm run clean", "clean:upload": "cd functions/upload && npm run clean", "clean:orchestrator": "cd functions/scan-orchestrator && rm -rf __pycache__ *.pyc", "clean:security": "cd functions/security-scanner && rm -rf __pycache__ *.pyc", "clean:pii": "cd functions/pii-detector && rm -rf __pycache__ *.pyc", "clean:anomaly": "cd functions/anomaly-detector && rm -rf __pycache__ *.pyc", "clean:report": "cd functions/report-generator && rm -rf __pycache__ *.pyc", "clean:layers": "cd layers/common && npm run clean", "deploy": "echo 'Backend deployment is handled by CDK'", "dev": "echo 'Use SAM local for local development'"}, "keywords": ["aws", "lambda", "security", "compliance", "ai", "serverless"], "author": "AWS AI Security Scanner Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}