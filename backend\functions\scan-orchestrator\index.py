import json
import boto3
import uuid
import logging
import os
from datetime import datetime
from typing import Dict, Any, Optional
from pydantic import BaseModel, ValidationError
import structlog

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Initialize AWS clients
dynamodb = boto3.resource('dynamodb')
stepfunctions = boto3.client('stepfunctions')
s3 = boto3.client('s3')

# Environment variables
SCANS_TABLE = os.environ['SCANS_TABLE']
RESULTS_TABLE = os.environ['RESULTS_TABLE']
STEP_FUNCTION_ARN = os.environ.get('STEP_FUNCTION_ARN')

# Pydantic models
class ScanRequest(BaseModel):
    scanId: str
    scanType: str = 'full'
    priority: str = 'normal'

class ScanStatusResponse(BaseModel):
    scanId: str
    status: str
    progress: int
    message: str
    results: Optional[Dict[str, Any]] = None
    reportUrl: Optional[str] = None
    createdAt: str
    updatedAt: str

def create_response(status_code: int, body: Dict[str, Any]) -> Dict[str, Any]:
    """Create standardized API response"""
    return {
        'statusCode': status_code,
        'headers': {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        'body': json.dumps(body, default=str)
    }

def handle_error(error: Exception) -> Dict[str, Any]:
    """Handle and format errors"""
    logger.error("Error occurred", error=str(error), error_type=type(error).__name__)
    
    if isinstance(error, ValidationError):
        return create_response(400, {
            'error': 'Validation Error',
            'message': str(error)
        })
    elif isinstance(error, ValueError):
        return create_response(400, {
            'error': 'Invalid Request',
            'message': str(error)
        })
    else:
        return create_response(500, {
            'error': 'Internal Server Error',
            'message': 'An unexpected error occurred'
        })

def extract_user_id(event: Dict[str, Any]) -> str:
    """Extract user ID from JWT token"""
    try:
        # Extract from API Gateway authorizer context
        claims = event.get('requestContext', {}).get('authorizer', {}).get('claims', {})
        user_id = claims.get('sub')
        
        if not user_id:
            raise ValueError("User ID not found in token")
        
        return user_id
    except Exception as e:
        logger.error("Failed to extract user ID", error=str(e))
        raise ValueError("Invalid authorization token")

def get_scan_record(scan_id: str, user_id: str) -> Optional[Dict[str, Any]]:
    """Get scan record from DynamoDB"""
    try:
        table = dynamodb.Table(SCANS_TABLE)
        response = table.get_item(
            Key={'scanId': scan_id}
        )
        
        item = response.get('Item')
        if not item:
            return None
        
        # Verify user owns this scan
        if item.get('userId') != user_id:
            raise ValueError("Access denied: scan belongs to different user")
        
        return item
    except Exception as e:
        logger.error("Failed to get scan record", scan_id=scan_id, error=str(e))
        raise

def update_scan_status(scan_id: str, status: str, progress: int = 0, message: str = "", results: Optional[Dict] = None) -> None:
    """Update scan status in DynamoDB"""
    try:
        table = dynamodb.Table(SCANS_TABLE)
        
        update_expression = "SET #status = :status, #progress = :progress, #message = :message, updatedAt = :updated_at"
        expression_attribute_names = {
            '#status': 'status',
            '#progress': 'progress',
            '#message': 'message'
        }
        expression_attribute_values = {
            ':status': status,
            ':progress': progress,
            ':message': message,
            ':updated_at': datetime.utcnow().isoformat()
        }
        
        if results:
            update_expression += ", results = :results"
            expression_attribute_values[':results'] = results
        
        if status == 'completed':
            update_expression += ", completedAt = :completed_at"
            expression_attribute_values[':completed_at'] = datetime.utcnow().isoformat()
        
        table.update_item(
            Key={'scanId': scan_id},
            UpdateExpression=update_expression,
            ExpressionAttributeNames=expression_attribute_names,
            ExpressionAttributeValues=expression_attribute_values
        )
        
        logger.info("Scan status updated", scan_id=scan_id, status=status, progress=progress)
        
    except Exception as e:
        logger.error("Failed to update scan status", scan_id=scan_id, error=str(e))
        raise

def start_step_function_execution(scan_id: str, s3_key: str, scan_type: str) -> str:
    """Start Step Functions execution for scan processing"""
    try:
        execution_input = {
            'scanId': scan_id,
            's3Key': s3_key,
            'scanType': scan_type,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        execution_name = f"scan-{scan_id}-{int(datetime.utcnow().timestamp())}"
        
        response = stepfunctions.start_execution(
            stateMachineArn=STEP_FUNCTION_ARN,
            name=execution_name,
            input=json.dumps(execution_input)
        )
        
        execution_arn = response['executionArn']
        logger.info("Step Functions execution started", 
                   scan_id=scan_id, 
                   execution_arn=execution_arn)
        
        return execution_arn
        
    except Exception as e:
        logger.error("Failed to start Step Functions execution", scan_id=scan_id, error=str(e))
        raise

def handle_start_scan(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle scan initiation request"""
    try:
        body = json.loads(event.get('body', '{}'))
        scan_request = ScanRequest(**body)
        
        user_id = extract_user_id(event)
        
        # Get scan record
        scan_record = get_scan_record(scan_request.scanId, user_id)
        if not scan_record:
            return create_response(404, {
                'error': 'Scan Not Found',
                'message': 'Scan record not found'
            })
        
        # Check if file is uploaded
        if scan_record.get('status') != 'uploaded':
            return create_response(400, {
                'error': 'Invalid Status',
                'message': 'File must be uploaded before starting scan'
            })
        
        # Update status to processing
        update_scan_status(scan_request.scanId, 'processing', 0, "Starting scan analysis...")
        
        # Start Step Functions execution
        if STEP_FUNCTION_ARN:
            execution_arn = start_step_function_execution(
                scan_request.scanId,
                scan_record['s3Key'],
                scan_request.scanType
            )
            
            # Update scan record with execution ARN
            table = dynamodb.Table(SCANS_TABLE)
            table.update_item(
                Key={'scanId': scan_request.scanId},
                UpdateExpression="SET executionArn = :execution_arn",
                ExpressionAttributeValues={':execution_arn': execution_arn}
            )
        
        return create_response(200, {
            'message': 'Scan started successfully',
            'scanId': scan_request.scanId,
            'status': 'processing'
        })
        
    except Exception as e:
        return handle_error(e)

def handle_get_scan_status(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle scan status request"""
    try:
        scan_id = event.get('pathParameters', {}).get('scanId')
        if not scan_id:
            return create_response(400, {
                'error': 'Missing Parameter',
                'message': 'Scan ID is required'
            })
        
        user_id = extract_user_id(event)
        
        # Get scan record
        scan_record = get_scan_record(scan_id, user_id)
        if not scan_record:
            return create_response(404, {
                'error': 'Scan Not Found',
                'message': 'Scan record not found'
            })
        
        # Create response
        response_data = ScanStatusResponse(
            scanId=scan_record['scanId'],
            status=scan_record.get('status', 'unknown'),
            progress=scan_record.get('progress', 0),
            message=scan_record.get('message', ''),
            results=scan_record.get('results'),
            reportUrl=scan_record.get('reportUrl'),
            createdAt=scan_record['createdAt'],
            updatedAt=scan_record.get('updatedAt', scan_record['createdAt'])
        )
        
        return create_response(200, response_data.dict())
        
    except Exception as e:
        return handle_error(e)

def handle_list_scans(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle list scans request"""
    try:
        user_id = extract_user_id(event)
        
        # Query scans for user
        table = dynamodb.Table(SCANS_TABLE)
        response = table.query(
            IndexName='UserScansIndex',
            KeyConditionExpression='userId = :user_id',
            ExpressionAttributeValues={':user_id': user_id},
            ScanIndexForward=False,  # Sort by timestamp descending
            Limit=50  # Limit to 50 most recent scans
        )
        
        scans = []
        for item in response.get('Items', []):
            scan_data = {
                'scanId': item['scanId'],
                'fileName': item.get('fileName', ''),
                'status': item.get('status', 'unknown'),
                'progress': item.get('progress', 0),
                'scanType': item.get('scanType', 'full'),
                'createdAt': item['createdAt'],
                'updatedAt': item.get('updatedAt', item['createdAt']),
                'completedAt': item.get('completedAt')
            }
            scans.append(scan_data)
        
        return create_response(200, {
            'scans': scans,
            'total': len(scans)
        })
        
    except Exception as e:
        return handle_error(e)

def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """Main Lambda handler"""
    logger.info("Request received", event=event)
    
    try:
        http_method = event.get('httpMethod')
        path = event.get('path', '')
        
        # Handle OPTIONS request for CORS
        if http_method == 'OPTIONS':
            return create_response(200, {})
        
        # Route requests
        if http_method == 'POST' and path.endswith('/scan'):
            return handle_start_scan(event)
        elif http_method == 'GET' and '/scan/' in path:
            return handle_get_scan_status(event)
        elif http_method == 'GET' and path.endswith('/scans'):
            return handle_list_scans(event)
        else:
            return create_response(404, {
                'error': 'Not Found',
                'message': 'Endpoint not found'
            })
            
    except Exception as e:
        logger.error("Unhandled error in lambda_handler", error=str(e))
        return handle_error(e)
