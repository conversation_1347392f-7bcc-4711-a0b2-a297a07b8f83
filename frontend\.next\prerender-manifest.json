{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "2d7c03bd3b596d3390a043d2d8f5cdf4", "previewModeSigningKey": "ac90e67d01745886290a25ce63e44e0f977655aed8b0247eae3c62373bae1005", "previewModeEncryptionKey": "ddca5f99da1fa9515aad15779b81a9e29914ac32515b275289224612b88ef209"}}