{"name": "ai-scanner-common-layer", "version": "1.0.0", "description": "Common dependencies layer for AI Scanner Lambda functions", "main": "nodejs/node_modules/index.js", "scripts": {"build": "npm run clean && npm run install:deps && npm run build:python", "install:deps": "mkdir -p nodejs && cd nodejs && npm install", "build:python": "mkdir -p python && pip install -r requirements.txt -t python/", "clean": "<PERSON><PERSON><PERSON>s python", "test": "echo 'No tests for layer'", "lint": "echo 'No linting for layer'"}, "dependencies": {"aws-sdk": "^2.1490.0", "@aws-sdk/client-s3": "^3.450.0", "@aws-sdk/client-dynamodb": "^3.450.0", "@aws-sdk/client-cognito-identity-provider": "^3.450.0", "@aws-sdk/client-step-functions": "^3.450.0", "@aws-sdk/lib-dynamodb": "^3.450.0", "@aws-sdk/s3-request-presigner": "^3.450.0", "uuid": "^9.0.1", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "lodash": "^4.17.21"}, "devDependencies": {"rimraf": "^5.0.5"}, "keywords": ["aws", "lambda", "layer", "common", "dependencies"], "author": "AWS AI Security Scanner Team", "license": "MIT"}