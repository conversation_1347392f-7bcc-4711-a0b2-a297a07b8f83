@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900 antialiased;
  }
  
  * {
    @apply border-gray-200;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
  }
  
  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }
  
  .btn-warning {
    @apply btn bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }
  
  .btn-danger {
    @apply btn bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;
  }
  
  .btn-outline {
    @apply btn bg-transparent border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-primary-500;
  }
  
  .btn-ghost {
    @apply btn bg-transparent border-transparent text-gray-700 hover:bg-gray-100 focus:ring-primary-500;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-base;
  }
  
  .btn-xl {
    @apply px-8 py-4 text-lg;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-soft border border-gray-200;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50 rounded-b-lg;
  }
  
  .input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
  }
  
  .input-error {
    @apply border-danger-300 text-danger-900 placeholder-danger-300 focus:ring-danger-500 focus:border-danger-500;
  }
  
  .label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }
  
  .error-text {
    @apply mt-1 text-sm text-danger-600;
  }
  
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-success {
    @apply badge bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }
  
  .badge-danger {
    @apply badge bg-danger-100 text-danger-800;
  }
  
  .badge-info {
    @apply badge bg-primary-100 text-primary-800;
  }
  
  .badge-gray {
    @apply badge bg-gray-100 text-gray-800;
  }
  
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }
  
  .progress-bar {
    @apply w-full bg-gray-200 rounded-full h-2;
  }
  
  .progress-fill {
    @apply bg-primary-600 h-2 rounded-full transition-all duration-300 ease-out;
  }
  
  .tooltip {
    @apply absolute z-50 px-2 py-1 text-xs text-white bg-gray-900 rounded shadow-lg opacity-0 pointer-events-none transition-opacity duration-200;
  }
  
  .tooltip.show {
    @apply opacity-100;
  }
  
  .dropdown {
    @apply absolute right-0 z-50 mt-2 w-56 origin-top-right bg-white border border-gray-200 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none;
  }
  
  .dropdown-item {
    @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 cursor-pointer;
  }
  
  .modal-overlay {
    @apply fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4;
  }
  
  .modal {
    @apply bg-white rounded-lg shadow-xl max-w-md w-full max-h-screen overflow-y-auto;
  }
  
  .modal-header {
    @apply px-6 py-4 border-b border-gray-200;
  }
  
  .modal-body {
    @apply px-6 py-4;
  }
  
  .modal-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50 rounded-b-lg flex justify-end space-x-3;
  }
  
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }
  
  .table-header {
    @apply bg-gray-50;
  }
  
  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }
  
  .table-body {
    @apply bg-white divide-y divide-gray-200;
  }
  
  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }
  
  .sidebar {
    @apply fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out;
  }
  
  .sidebar.closed {
    @apply -translate-x-full;
  }
  
  .sidebar-overlay {
    @apply fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
  }
  
  .bg-gradient-primary {
    @apply bg-gradient-to-r from-primary-500 to-primary-700;
  }
  
  .bg-gradient-secondary {
    @apply bg-gradient-to-r from-secondary-500 to-secondary-700;
  }
  
  .bg-gradient-success {
    @apply bg-gradient-to-r from-success-500 to-success-700;
  }
  
  .bg-gradient-warning {
    @apply bg-gradient-to-r from-warning-500 to-warning-700;
  }
  
  .bg-gradient-danger {
    @apply bg-gradient-to-r from-danger-500 to-danger-700;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  .glass {
    @apply bg-white bg-opacity-80 backdrop-blur-sm border border-white border-opacity-20;
  }
  
  .glass-dark {
    @apply bg-gray-900 bg-opacity-80 backdrop-blur-sm border border-gray-700 border-opacity-20;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}

/* Focus styles */
.focus-visible {
  @apply outline-none ring-2 ring-primary-500 ring-offset-2;
}

/* Animation classes */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-slide-down {
  animation: slideDown 0.3s ease-out;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break {
    page-break-before: always;
  }
}
