# Deployment Guide

## Prerequisites

### Required Software
- **Node.js**: 18.x or later
- **Python**: 3.9 or later
- **AWS CLI**: 2.x configured with appropriate credentials
- **AWS CDK**: 2.x installed globally
- **Git**: For version control

### AWS Account Setup
1. **AWS Account**: Active AWS account with billing enabled
2. **IAM Permissions**: Administrator access or specific permissions for:
   - CloudFormation
   - Lambda
   - API Gateway
   - S3
   - DynamoDB
   - Cognito
   - Step Functions
   - CloudWatch
   - IAM

### Environment Variables
Create a `.env` file in the project root:
```bash
# AWS Configuration
AWS_ACCOUNT_ID=************
AWS_REGION=us-east-1
CDK_DEFAULT_ACCOUNT=************
CDK_DEFAULT_REGION=us-east-1

# Environment
ENVIRONMENT=dev  # or prod

# Notifications
ALERT_EMAIL=<EMAIL>

# Production only
CERTIFICATE_ARN=arn:aws:acm:us-east-1:************:certificate/********-1234-1234-1234-************
DOMAIN_NAME=ai-security-scanner.com
```

## Quick Start

### 1. Clone and Setup
```bash
# Clone the repository
git clone https://github.com/your-org/aws-ai-security-scanner.git
cd aws-ai-security-scanner

# Install dependencies
npm install
npm run install:all
```

### 2. Configure AWS
```bash
# Configure AWS CLI
aws configure

# Bootstrap CDK (first time only)
cd infrastructure
npx cdk bootstrap
```

### 3. Deploy Development Environment
```bash
# Deploy infrastructure
cd infrastructure
npm run deploy:dev

# Deploy frontend
cd ../frontend
npm run build
aws s3 sync out/ s3://ai-scanner-frontend-dev-${AWS_ACCOUNT_ID}/ --delete
```

### 4. Access the Application
After deployment, the CDK will output the CloudFront URL. Access the application at:
```
https://d************3.cloudfront.net
```

## Detailed Deployment Steps

### Step 1: Infrastructure Deployment

#### Development Environment
```bash
cd infrastructure
npm install
npm run build

# Deploy all stacks
cdk deploy --all --context environment=dev --require-approval never

# Or deploy individual stacks
cdk deploy AiSecurityScanner-dev-Backend --context environment=dev
cdk deploy AiSecurityScanner-dev-Frontend --context environment=dev
cdk deploy AiSecurityScanner-dev-Monitoring --context environment=dev
```

#### Production Environment
```bash
cd infrastructure
npm install
npm run build

# Set production environment variables
export ENVIRONMENT=prod
export CERTIFICATE_ARN=arn:aws:acm:us-east-1:************:certificate/...
export DOMAIN_NAME=ai-security-scanner.com

# Deploy production stacks
cdk deploy --all --context environment=prod --require-approval never
```

### Step 2: Backend Functions Deployment

The Lambda functions are automatically deployed with the infrastructure. To update functions only:

```bash
cd backend

# Build all functions
npm run build

# Deploy via CDK
cd ../infrastructure
cdk deploy AiSecurityScanner-${ENVIRONMENT}-Backend
```

### Step 3: Frontend Deployment

#### Build and Deploy
```bash
cd frontend
npm install
npm run build

# Deploy to S3
aws s3 sync out/ s3://ai-scanner-frontend-${ENVIRONMENT}-${AWS_ACCOUNT_ID}/ --delete

# Invalidate CloudFront cache
aws cloudfront create-invalidation \
  --distribution-id ${CLOUDFRONT_DISTRIBUTION_ID} \
  --paths "/*"
```

#### Environment Configuration
The frontend automatically loads configuration from `/env.json` which is created during infrastructure deployment.

### Step 4: Post-Deployment Configuration

#### 1. Cognito User Pool Setup
```bash
# Create admin user (optional)
aws cognito-idp admin-create-user \
  --user-pool-id ${USER_POOL_ID} \
  --username <EMAIL> \
  --user-attributes Name=email,Value=<EMAIL> \
  --temporary-password TempPassword123! \
  --message-action SUPPRESS
```

#### 2. DNS Configuration (Production)
If using a custom domain:
```bash
# Create Route 53 hosted zone (if not exists)
aws route53 create-hosted-zone \
  --name ai-security-scanner.com \
  --caller-reference $(date +%s)

# Update nameservers with your domain registrar
aws route53 get-hosted-zone --id ${HOSTED_ZONE_ID}
```

#### 3. SSL Certificate (Production)
```bash
# Request certificate (if not exists)
aws acm request-certificate \
  --domain-name ai-security-scanner.com \
  --subject-alternative-names www.ai-security-scanner.com \
  --validation-method DNS \
  --region us-east-1
```

## Environment-Specific Configurations

### Development Environment
- **Domain**: CloudFront default domain
- **Retention**: 7 days
- **Monitoring**: Basic CloudWatch metrics
- **Costs**: Optimized for development

### Production Environment
- **Domain**: Custom domain with SSL
- **Retention**: 30 days
- **Monitoring**: Full monitoring with alerts
- **Costs**: Optimized for performance

## Monitoring and Logging

### CloudWatch Dashboard
Access the monitoring dashboard:
```
https://console.aws.amazon.com/cloudwatch/home?region=${AWS_REGION}#dashboards:name=ai-scanner-${ENVIRONMENT}
```

### Log Groups
- `/aws/lambda/ai-scanner-*-${ENVIRONMENT}`
- `/aws/apigateway/ai-scanner-api-${ENVIRONMENT}`
- `/aws/stepfunctions/ai-scanner-${ENVIRONMENT}`

### Alarms
Key alarms configured:
- API Gateway high error rate
- Lambda function errors
- Step Functions failures
- High latency alerts

## Backup and Recovery

### Automated Backups
- **DynamoDB**: Point-in-time recovery enabled (production)
- **S3**: Versioning enabled with lifecycle policies
- **Lambda**: Code stored in S3 with versioning

### Manual Backup
```bash
# Export DynamoDB tables
aws dynamodb create-backup \
  --table-name ai-scanner-scans-${ENVIRONMENT} \
  --backup-name ai-scanner-scans-backup-$(date +%Y%m%d)

# Backup S3 buckets
aws s3 sync s3://ai-scanner-uploads-${ENVIRONMENT}-${AWS_ACCOUNT_ID}/ \
  s3://ai-scanner-backup-${ENVIRONMENT}-${AWS_ACCOUNT_ID}/uploads/

# Export CloudFormation templates
aws cloudformation describe-stacks \
  --stack-name AiSecurityScanner-${ENVIRONMENT}-Backend \
  --query 'Stacks[0].TemplateDescription' > backup/backend-stack.json
```

## Scaling Considerations

### Lambda Concurrency
```bash
# Set reserved concurrency for critical functions
aws lambda put-reserved-concurrency \
  --function-name ai-scanner-security-${ENVIRONMENT} \
  --reserved-concurrency-config ReservedConcurrencyConfig=100
```

### DynamoDB Scaling
- **On-Demand**: Automatically scales (default)
- **Provisioned**: Set specific read/write capacity

### API Gateway Throttling
- **Rate Limit**: 1000 requests/second (configurable)
- **Burst Limit**: 2000 requests (configurable)

## Security Hardening

### Post-Deployment Security
1. **Review IAM Policies**: Ensure least privilege
2. **Enable GuardDuty**: For threat detection
3. **Configure WAF**: For API protection
4. **Enable Config**: For compliance monitoring

### Security Checklist
- [ ] All data encrypted at rest and in transit
- [ ] IAM roles follow least privilege principle
- [ ] CloudTrail logging enabled
- [ ] VPC endpoints configured (if using VPC)
- [ ] Security groups properly configured
- [ ] Secrets stored in AWS Secrets Manager

## Troubleshooting

### Common Issues

#### 1. CDK Bootstrap Issues
```bash
# Re-bootstrap if needed
cdk bootstrap --force
```

#### 2. Lambda Deployment Failures
```bash
# Check function logs
aws logs tail /aws/lambda/ai-scanner-auth-${ENVIRONMENT} --follow

# Update function code manually
aws lambda update-function-code \
  --function-name ai-scanner-auth-${ENVIRONMENT} \
  --zip-file fileb://function.zip
```

#### 3. Frontend Build Issues
```bash
# Clear cache and rebuild
cd frontend
rm -rf .next node_modules
npm install
npm run build
```

#### 4. API Gateway CORS Issues
```bash
# Redeploy API Gateway
aws apigateway create-deployment \
  --rest-api-id ${API_ID} \
  --stage-name ${ENVIRONMENT}
```

### Debug Mode
Enable debug logging:
```bash
export DEBUG=true
export CDK_DEBUG=true
cdk deploy --verbose
```

## Cost Optimization

### Development Environment
- Use smaller Lambda memory sizes
- Shorter log retention periods
- On-demand DynamoDB billing

### Production Environment
- Reserved capacity for predictable workloads
- S3 Intelligent Tiering
- CloudWatch log retention optimization

### Cost Monitoring
```bash
# Set up billing alerts
aws budgets create-budget \
  --account-id ${AWS_ACCOUNT_ID} \
  --budget file://budget.json
```

## Maintenance

### Regular Tasks
1. **Update Dependencies**: Monthly security updates
2. **Review Logs**: Weekly log analysis
3. **Monitor Costs**: Daily cost review
4. **Security Patches**: Apply as needed

### Automated Maintenance
- Lambda function updates via CI/CD
- Dependency scanning with Snyk
- Security scanning with AWS Inspector

## Support and Documentation

### Resources
- **AWS Documentation**: https://docs.aws.amazon.com/
- **CDK Documentation**: https://docs.aws.amazon.com/cdk/
- **Project Wiki**: Internal documentation

### Getting Help
1. Check CloudWatch logs for errors
2. Review AWS service health dashboard
3. Contact AWS support for service issues
4. Internal team escalation procedures
