(()=>{var t={};t.id=931,t.ids=[931],t.modules={2934:t=>{"use strict";t.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:t=>{"use strict";t.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:t=>{"use strict";t.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7790:t=>{"use strict";t.exports=require("assert")},8893:t=>{"use strict";t.exports=require("buffer")},4770:t=>{"use strict";t.exports=require("crypto")},2048:t=>{"use strict";t.exports=require("fs")},6162:t=>{"use strict";t.exports=require("stream")},1764:t=>{"use strict";t.exports=require("util")},1568:t=>{"use strict";t.exports=require("zlib")},6782:(t,e,r)=>{"use strict";r.r(e),r.d(e,{GlobalError:()=>o.a,__next_app__:()=>h,originalPathname:()=>d,pages:()=>c,routeModule:()=>p,tree:()=>u}),r(5480),r(5495),r(5866);var i=r(3191),n=r(8716),s=r(7922),o=r.n(s),a=r(5231),l={};for(let t in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(t)&&(l[t]=()=>a[t]);r.d(e,l);let u=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,5480)),"C:\\Users\\<USER>\\Desktop\\AWS Task\\frontend\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,5495)),"C:\\Users\\<USER>\\Desktop\\AWS Task\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,5866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\AWS Task\\frontend\\src\\app\\page.tsx"],d="/page",h={require:r,loadChunk:()=>Promise.resolve()},p=new i.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},1167:(t,e,r)=>{Promise.resolve().then(r.t.bind(r,2994,23)),Promise.resolve().then(r.t.bind(r,6114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,9671,23)),Promise.resolve().then(r.t.bind(r,1868,23)),Promise.resolve().then(r.t.bind(r,4759,23))},6390:(t,e,r)=>{Promise.resolve().then(r.bind(r,2395))},5951:(t,e,r)=>{Promise.resolve().then(r.bind(r,2332))},2332:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>sH});var i=r(326),n=r(5149),s=r(7577),o=r(7389),a=r(2735);let l=s.forwardRef(function({title:t,titleId:e,...r},i){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":e},r),t?s.createElement("title",{id:e},t):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))}),u=s.forwardRef(function({title:t,titleId:e,...r},i){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":e},r),t?s.createElement("title",{id:e},t):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m5.231 13.481L15 17.25m-4.5-15H5.625c-.621 0-1.125.504-1.125 1.125v16.5c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Zm3.75 11.625a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))}),c=s.forwardRef(function({title:t,titleId:e,...r},i){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":e},r),t?s.createElement("title",{id:e},t):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))}),d=s.forwardRef(function({title:t,titleId:e,...r},i){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":e},r),t?s.createElement("title",{id:e},t):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 16.5V9.75m0 0 3 3m-3-3-3 3M6.75 19.5a4.5 4.5 0 0 1-1.41-8.775 5.25 5.25 0 0 1 10.233-2.33 3 3 0 0 1 3.758 3.848A3.752 3.752 0 0 1 18 19.5H6.75Z"}))}),h=s.forwardRef(function({title:t,titleId:e,...r},i){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":e},r),t?s.createElement("title",{id:e},t):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z"}))}),p=s.forwardRef(function({title:t,titleId:e,...r},i){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":e},r),t?s.createElement("title",{id:e},t):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"}))}),m=(0,s.createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),f=(0,s.createContext)({}),g=(0,s.createContext)(null),y="undefined"!=typeof document,v=y?s.useLayoutEffect:s.useEffect,x=(0,s.createContext)({strict:!1}),b=t=>t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),w="data-"+b("framerAppearId");function P(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function S(t){return"string"==typeof t||Array.isArray(t)}function A(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}let T=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],k=["initial",...T];function j(t){return A(t.animate)||k.some(e=>S(t[e]))}function E(t){return!!(j(t)||t.variants)}function C(t){return Array.isArray(t)?t.join(" "):t}let V={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},M={};for(let t in V)M[t]={isEnabled:e=>V[t].some(t=>!!e[t])};let D=(0,s.createContext)({}),L=(0,s.createContext)({}),R=Symbol.for("motionComponentSymbol"),N=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function I(t){if("string"!=typeof t||t.includes("-"));else if(N.indexOf(t)>-1||/[A-Z]/.test(t))return!0;return!1}let B={},F=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],O=new Set(F);function U(t,{layout:e,layoutId:r}){return O.has(t)||t.startsWith("origin")||(e||void 0!==r)&&(!!B[t]||"opacity"===t)}let z=t=>!!(t&&t.getVelocity),W={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},$=F.length,_=t=>e=>"string"==typeof e&&e.startsWith(t),H=_("--"),G=_("var(--"),q=(t,e)=>e&&"number"==typeof t?e.transform(t):t,X=(t,e,r)=>Math.min(Math.max(r,t),e),Y={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},Z={...Y,transform:t=>X(0,1,t)},K={...Y,default:1},Q=t=>Math.round(1e5*t)/1e5,J=/(-)?([\d]*\.?[\d])+/g,tt=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,te=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function tr(t){return"string"==typeof t}let ti=t=>({test:e=>tr(e)&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),tn=ti("deg"),ts=ti("%"),to=ti("px"),ta=ti("vh"),tl=ti("vw"),tu={...ts,parse:t=>ts.parse(t)/100,transform:t=>ts.transform(100*t)},tc={...Y,transform:Math.round},td={borderWidth:to,borderTopWidth:to,borderRightWidth:to,borderBottomWidth:to,borderLeftWidth:to,borderRadius:to,radius:to,borderTopLeftRadius:to,borderTopRightRadius:to,borderBottomRightRadius:to,borderBottomLeftRadius:to,width:to,maxWidth:to,height:to,maxHeight:to,size:to,top:to,right:to,bottom:to,left:to,padding:to,paddingTop:to,paddingRight:to,paddingBottom:to,paddingLeft:to,margin:to,marginTop:to,marginRight:to,marginBottom:to,marginLeft:to,rotate:tn,rotateX:tn,rotateY:tn,rotateZ:tn,scale:K,scaleX:K,scaleY:K,scaleZ:K,skew:tn,skewX:tn,skewY:tn,distance:to,translateX:to,translateY:to,translateZ:to,x:to,y:to,z:to,perspective:to,transformPerspective:to,opacity:Z,originX:tu,originY:tu,originZ:to,zIndex:tc,fillOpacity:Z,strokeOpacity:Z,numOctaves:tc};function th(t,e,r,i){let{style:n,vars:s,transform:o,transformOrigin:a}=t,l=!1,u=!1,c=!0;for(let t in e){let r=e[t];if(H(t)){s[t]=r;continue}let i=td[t],d=q(r,i);if(O.has(t)){if(l=!0,o[t]=d,!c)continue;r!==(i.default||0)&&(c=!1)}else t.startsWith("origin")?(u=!0,a[t]=d):n[t]=d}if(!e.transform&&(l||i?n.transform=function(t,{enableHardwareAcceleration:e=!0,allowTransformNone:r=!0},i,n){let s="";for(let e=0;e<$;e++){let r=F[e];if(void 0!==t[r]){let e=W[r]||r;s+=`${e}(${t[r]}) `}}return e&&!t.z&&(s+="translateZ(0)"),s=s.trim(),n?s=n(t,i?"":s):r&&i&&(s="none"),s}(t.transform,r,c,i):n.transform&&(n.transform="none")),u){let{originX:t="50%",originY:e="50%",originZ:r=0}=a;n.transformOrigin=`${t} ${e} ${r}`}}let tp=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function tm(t,e,r){for(let i in e)z(e[i])||U(i,r)||(t[i]=e[i])}let tf=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function tg(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||tf.has(t)}let ty=t=>!tg(t);try{!function(t){t&&(ty=e=>e.startsWith("on")?!tg(e):t(e))}(require("@emotion/is-prop-valid").default)}catch(t){}function tv(t,e,r){return"string"==typeof t?t:to.transform(e+r*t)}let tx={offset:"stroke-dashoffset",array:"stroke-dasharray"},tb={offset:"strokeDashoffset",array:"strokeDasharray"};function tw(t,{attrX:e,attrY:r,attrScale:i,originX:n,originY:s,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...u},c,d,h){if(th(t,u,c,h),d){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:p,style:m,dimensions:f}=t;p.transform&&(f&&(m.transform=p.transform),delete p.transform),f&&(void 0!==n||void 0!==s||m.transform)&&(m.transformOrigin=function(t,e,r){let i=tv(e,t.x,t.width),n=tv(r,t.y,t.height);return`${i} ${n}`}(f,void 0!==n?n:.5,void 0!==s?s:.5)),void 0!==e&&(p.x=e),void 0!==r&&(p.y=r),void 0!==i&&(p.scale=i),void 0!==o&&function(t,e,r=1,i=0,n=!0){t.pathLength=1;let s=n?tx:tb;t[s.offset]=to.transform(-i);let o=to.transform(e),a=to.transform(r);t[s.array]=`${o} ${a}`}(p,o,a,l,!1)}let tP=()=>({...tp(),attrs:{}}),tS=t=>"string"==typeof t&&"svg"===t.toLowerCase();function tA(t,{style:e,vars:r},i,n){for(let s in Object.assign(t.style,e,n&&n.getProjectionStyles(i)),r)t.style.setProperty(s,r[s])}let tT=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function tk(t,e,r,i){for(let r in tA(t,e,void 0,i),e.attrs)t.setAttribute(tT.has(r)?r:b(r),e.attrs[r])}function tj(t,e){let{style:r}=t,i={};for(let n in r)(z(r[n])||e.style&&z(e.style[n])||U(n,t))&&(i[n]=r[n]);return i}function tE(t,e){let r=tj(t,e);for(let i in t)(z(t[i])||z(e[i]))&&(r[-1!==F.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return r}function tC(t,e,r,i={},n={}){return"function"==typeof e&&(e=e(void 0!==r?r:t.custom,i,n)),"string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e&&(e=e(void 0!==r?r:t.custom,i,n)),e}let tV=t=>Array.isArray(t),tM=t=>!!(t&&"object"==typeof t&&t.mix&&t.toValue),tD=t=>tV(t)?t[t.length-1]||0:t;function tL(t){let e=z(t)?t.get():t;return tM(e)?e.toValue():e}let tR=t=>(e,r)=>{let i=(0,s.useContext)(f),n=(0,s.useContext)(g),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e,onMount:r},i,n,s){let o={latestValues:function(t,e,r,i){let n={},s=i(t,{});for(let t in s)n[t]=tL(s[t]);let{initial:o,animate:a}=t,l=j(t),u=E(t);e&&u&&!l&&!1!==t.inherit&&(void 0===o&&(o=e.initial),void 0===a&&(a=e.animate));let c=!!r&&!1===r.initial,d=(c=c||!1===o)?a:o;return d&&"boolean"!=typeof d&&!A(d)&&(Array.isArray(d)?d:[d]).forEach(e=>{let r=tC(t,e);if(!r)return;let{transitionEnd:i,transition:s,...o}=r;for(let t in o){let e=o[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(n[t]=e)}for(let t in i)n[t]=i[t]}),n}(i,n,s,t),renderState:e()};return r&&(o.mount=t=>r(i,t,o)),o})(t,e,i,n);return r?o():function(t){let e=(0,s.useRef)(null);return null===e.current&&(e.current=t()),e.current}(o)},tN=t=>t;class tI{constructor(){this.order=[],this.scheduled=new Set}add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){let e=this.order.indexOf(t);-1!==e&&(this.order.splice(e,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}}let tB=["prepare","read","update","preRender","render","postRender"],{schedule:tF,cancel:tO,state:tU,steps:tz}=function(t,e){let r=!1,i=!0,n={delta:0,timestamp:0,isProcessing:!1},s=tB.reduce((t,e)=>(t[e]=function(t){let e=new tI,r=new tI,i=0,n=!1,s=!1,o=new WeakSet,a={schedule:(t,s=!1,a=!1)=>{let l=a&&n,u=l?e:r;return s&&o.add(t),u.add(t)&&l&&n&&(i=e.order.length),t},cancel:t=>{r.remove(t),o.delete(t)},process:l=>{if(n){s=!0;return}if(n=!0,[e,r]=[r,e],r.clear(),i=e.order.length)for(let r=0;r<i;r++){let i=e.order[r];i(l),o.has(i)&&(a.schedule(i),t())}n=!1,s&&(s=!1,a.process(l))}};return a}(()=>r=!0),t),{}),o=t=>s[t].process(n),a=()=>{let s=performance.now();r=!1,n.delta=i?1e3/60:Math.max(Math.min(s-n.timestamp,40),1),n.timestamp=s,n.isProcessing=!0,tB.forEach(o),n.isProcessing=!1,r&&e&&(i=!1,t(a))},l=()=>{r=!0,i=!0,n.isProcessing||t(a)};return{schedule:tB.reduce((t,e)=>{let i=s[e];return t[e]=(t,e=!1,n=!1)=>(r||l(),i.schedule(t,e,n)),t},{}),cancel:t=>tB.forEach(e=>s[e].cancel(t)),state:n,steps:s}}("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:tN,!0),tW={useVisualState:tR({scrapeMotionValuesFromProps:tE,createRenderState:tP,onMount:(t,e,{renderState:r,latestValues:i})=>{tF.read(()=>{try{r.dimensions="function"==typeof e.getBBox?e.getBBox():e.getBoundingClientRect()}catch(t){r.dimensions={x:0,y:0,width:0,height:0}}}),tF.render(()=>{tw(r,i,{enableHardwareAcceleration:!1},tS(e.tagName),t.transformTemplate),tk(e,r)})}})},t$={useVisualState:tR({scrapeMotionValuesFromProps:tj,createRenderState:tp})};function t_(t,e,r,i={passive:!0}){return t.addEventListener(e,r,i),()=>t.removeEventListener(e,r)}let tH=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function tG(t,e="page"){return{point:{x:t[e+"X"],y:t[e+"Y"]}}}let tq=t=>e=>tH(e)&&t(e,tG(e));function tX(t,e,r,i){return t_(t,e,tq(r),i)}let tY=(t,e)=>r=>e(t(r)),tZ=(...t)=>t.reduce(tY);function tK(t){let e=null;return()=>null===e&&(e=t,()=>{e=null})}let tQ=tK("dragHorizontal"),tJ=tK("dragVertical");function t0(t){let e=!1;if("y"===t)e=tJ();else if("x"===t)e=tQ();else{let t=tQ(),r=tJ();t&&r?e=()=>{t(),r()}:(t&&t(),r&&r())}return e}function t1(){let t=t0(!0);return!t||(t(),!1)}class t5{constructor(t){this.isMounted=!1,this.node=t}update(){}}function t2(t,e){let r="onHover"+(e?"Start":"End");return tX(t.current,"pointer"+(e?"enter":"leave"),(i,n)=>{if("touch"===i.pointerType||t1())return;let s=t.getProps();t.animationState&&s.whileHover&&t.animationState.setActive("whileHover",e),s[r]&&tF.update(()=>s[r](i,n))},{passive:!t.getProps()[r]})}class t3 extends t5{mount(){this.unmount=tZ(t2(this.node,!0),t2(this.node,!1))}unmount(){}}class t6 extends t5{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=tZ(t_(this.node.current,"focus",()=>this.onFocus()),t_(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let t4=(t,e)=>!!e&&(t===e||t4(t,e.parentElement));function t9(t,e){if(!e)return;let r=new PointerEvent("pointer"+t);e(r,tG(r))}class t7 extends t5{constructor(){super(...arguments),this.removeStartListeners=tN,this.removeEndListeners=tN,this.removeAccessibleListeners=tN,this.startPointerPress=(t,e)=>{if(this.isPressing)return;this.removeEndListeners();let r=this.node.getProps(),i=tX(window,"pointerup",(t,e)=>{if(!this.checkPressEnd())return;let{onTap:r,onTapCancel:i,globalTapTarget:n}=this.node.getProps();tF.update(()=>{n||t4(this.node.current,t.target)?r&&r(t,e):i&&i(t,e)})},{passive:!(r.onTap||r.onPointerUp)}),n=tX(window,"pointercancel",(t,e)=>this.cancelPress(t,e),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=tZ(i,n),this.startPress(t,e)},this.startAccessiblePress=()=>{let t=t_(this.node.current,"keydown",t=>{"Enter"!==t.key||this.isPressing||(this.removeEndListeners(),this.removeEndListeners=t_(this.node.current,"keyup",t=>{"Enter"===t.key&&this.checkPressEnd()&&t9("up",(t,e)=>{let{onTap:r}=this.node.getProps();r&&tF.update(()=>r(t,e))})}),t9("down",(t,e)=>{this.startPress(t,e)}))}),e=t_(this.node.current,"blur",()=>{this.isPressing&&t9("cancel",(t,e)=>this.cancelPress(t,e))});this.removeAccessibleListeners=tZ(t,e)}}startPress(t,e){this.isPressing=!0;let{onTapStart:r,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&tF.update(()=>r(t,e))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!t1()}cancelPress(t,e){if(!this.checkPressEnd())return;let{onTapCancel:r}=this.node.getProps();r&&tF.update(()=>r(t,e))}mount(){let t=this.node.getProps(),e=tX(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),r=t_(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=tZ(e,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}let t8=new WeakMap,et=new WeakMap,ee=t=>{let e=t8.get(t.target);e&&e(t)},er=t=>{t.forEach(ee)},ei={some:0,all:1};class en extends t5{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:r,amount:i="some",once:n}=t,s={root:e?e.current:void 0,rootMargin:r,threshold:"number"==typeof i?i:ei[i]};return function(t,e,r){let i=function({root:t,...e}){let r=t||document;et.has(r)||et.set(r,{});let i=et.get(r),n=JSON.stringify(e);return i[n]||(i[n]=new IntersectionObserver(er,{root:t,...e})),i[n]}(e);return t8.set(t,r),i.observe(t),()=>{t8.delete(t),i.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:r,onViewportLeave:i}=this.node.getProps(),s=e?r:i;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return r=>t[r]!==e[r]}(t,e))&&this.startObserver()}unmount(){}}function es(t,e){if(!Array.isArray(e))return!1;let r=e.length;if(r!==t.length)return!1;for(let i=0;i<r;i++)if(e[i]!==t[i])return!1;return!0}function eo(t,e,r){let i=t.getProps();return tC(i,e,void 0!==r?r:i.custom,function(t){let e={};return t.values.forEach((t,r)=>e[r]=t.get()),e}(t),function(t){let e={};return t.values.forEach((t,r)=>e[r]=t.getVelocity()),e}(t))}let ea=t=>1e3*t,el=t=>t/1e3,eu={current:!1},ec=t=>Array.isArray(t)&&"number"==typeof t[0],ed=([t,e,r,i])=>`cubic-bezier(${t}, ${e}, ${r}, ${i})`,eh={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:ed([0,.65,.55,1]),circOut:ed([.55,0,1,.45]),backIn:ed([.31,.01,.66,-.59]),backOut:ed([.33,1.53,.69,.99])},ep=(t,e,r)=>(((1-3*r+3*e)*t+(3*r-6*e))*t+3*e)*t;function em(t,e,r,i){if(t===e&&r===i)return tN;let n=e=>(function(t,e,r,i,n){let s,o;let a=0;do(s=ep(o=e+(r-e)/2,i,n)-t)>0?r=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o})(e,0,1,t,r);return t=>0===t||1===t?t:ep(n(t),e,i)}let ef=em(.42,0,1,1),eg=em(0,0,.58,1),ey=em(.42,0,.58,1),ev=t=>Array.isArray(t)&&"number"!=typeof t[0],ex=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,eb=t=>e=>1-t(1-e),ew=t=>1-Math.sin(Math.acos(t)),eP=eb(ew),eS=ex(ew),eA=em(.33,1.53,.69,.99),eT=eb(eA),ek=ex(eT),ej={linear:tN,easeIn:ef,easeInOut:ey,easeOut:eg,circIn:ew,circInOut:eS,circOut:eP,backIn:eT,backInOut:ek,backOut:eA,anticipate:t=>(t*=2)<1?.5*eT(t):.5*(2-Math.pow(2,-10*(t-1)))},eE=t=>{if(Array.isArray(t)){tN(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,r,i,n]=t;return em(e,r,i,n)}return"string"==typeof t?(tN(void 0!==ej[t],`Invalid easing type '${t}'`),ej[t]):t},eC=(t,e)=>r=>!!(tr(r)&&te.test(r)&&r.startsWith(t)||e&&Object.prototype.hasOwnProperty.call(r,e)),eV=(t,e,r)=>i=>{if(!tr(i))return i;let[n,s,o,a]=i.match(J);return{[t]:parseFloat(n),[e]:parseFloat(s),[r]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},eM=t=>X(0,255,t),eD={...Y,transform:t=>Math.round(eM(t))},eL={test:eC("rgb","red"),parse:eV("red","green","blue"),transform:({red:t,green:e,blue:r,alpha:i=1})=>"rgba("+eD.transform(t)+", "+eD.transform(e)+", "+eD.transform(r)+", "+Q(Z.transform(i))+")"},eR={test:eC("#"),parse:function(t){let e="",r="",i="",n="";return t.length>5?(e=t.substring(1,3),r=t.substring(3,5),i=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),r=t.substring(2,3),i=t.substring(3,4),n=t.substring(4,5),e+=e,r+=r,i+=i,n+=n),{red:parseInt(e,16),green:parseInt(r,16),blue:parseInt(i,16),alpha:n?parseInt(n,16)/255:1}},transform:eL.transform},eN={test:eC("hsl","hue"),parse:eV("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:r,alpha:i=1})=>"hsla("+Math.round(t)+", "+ts.transform(Q(e))+", "+ts.transform(Q(r))+", "+Q(Z.transform(i))+")"},eI={test:t=>eL.test(t)||eR.test(t)||eN.test(t),parse:t=>eL.test(t)?eL.parse(t):eN.test(t)?eN.parse(t):eR.parse(t),transform:t=>tr(t)?t:t.hasOwnProperty("red")?eL.transform(t):eN.transform(t)},eB=(t,e,r)=>-r*t+r*e+t;function eF(t,e,r){return(r<0&&(r+=1),r>1&&(r-=1),r<1/6)?t+(e-t)*6*r:r<.5?e:r<2/3?t+(e-t)*(2/3-r)*6:t}let eO=(t,e,r)=>{let i=t*t;return Math.sqrt(Math.max(0,r*(e*e-i)+i))},eU=[eR,eL,eN],ez=t=>eU.find(e=>e.test(t));function eW(t){let e=ez(t);tN(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`);let r=e.parse(t);return e===eN&&(r=function({hue:t,saturation:e,lightness:r,alpha:i}){t/=360,r/=100;let n=0,s=0,o=0;if(e/=100){let i=r<.5?r*(1+e):r+e-r*e,a=2*r-i;n=eF(a,i,t+1/3),s=eF(a,i,t),o=eF(a,i,t-1/3)}else n=s=o=r;return{red:Math.round(255*n),green:Math.round(255*s),blue:Math.round(255*o),alpha:i}}(r)),r}let e$=(t,e)=>{let r=eW(t),i=eW(e),n={...r};return t=>(n.red=eO(r.red,i.red,t),n.green=eO(r.green,i.green,t),n.blue=eO(r.blue,i.blue,t),n.alpha=eB(r.alpha,i.alpha,t),eL.transform(n))},e_={regex:/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,countKey:"Vars",token:"${v}",parse:tN},eH={regex:tt,countKey:"Colors",token:"${c}",parse:eI.parse},eG={regex:J,countKey:"Numbers",token:"${n}",parse:Y.parse};function eq(t,{regex:e,countKey:r,token:i,parse:n}){let s=t.tokenised.match(e);s&&(t["num"+r]=s.length,t.tokenised=t.tokenised.replace(e,i),t.values.push(...s.map(n)))}function eX(t){let e=t.toString(),r={value:e,tokenised:e,values:[],numVars:0,numColors:0,numNumbers:0};return r.value.includes("var(--")&&eq(r,e_),eq(r,eH),eq(r,eG),r}function eY(t){return eX(t).values}function eZ(t){let{values:e,numColors:r,numVars:i,tokenised:n}=eX(t),s=e.length;return t=>{let e=n;for(let n=0;n<s;n++)e=n<i?e.replace(e_.token,t[n]):n<i+r?e.replace(eH.token,eI.transform(t[n])):e.replace(eG.token,Q(t[n]));return e}}let eK=t=>"number"==typeof t?0:t,eQ={test:function(t){var e,r;return isNaN(t)&&tr(t)&&((null===(e=t.match(J))||void 0===e?void 0:e.length)||0)+((null===(r=t.match(tt))||void 0===r?void 0:r.length)||0)>0},parse:eY,createTransformer:eZ,getAnimatableNone:function(t){let e=eY(t);return eZ(t)(e.map(eK))}},eJ=(t,e)=>r=>`${r>0?e:t}`;function e0(t,e){return"number"==typeof t?r=>eB(t,e,r):eI.test(t)?e$(t,e):t.startsWith("var(")?eJ(t,e):e2(t,e)}let e1=(t,e)=>{let r=[...t],i=r.length,n=t.map((t,r)=>e0(t,e[r]));return t=>{for(let e=0;e<i;e++)r[e]=n[e](t);return r}},e5=(t,e)=>{let r={...t,...e},i={};for(let n in r)void 0!==t[n]&&void 0!==e[n]&&(i[n]=e0(t[n],e[n]));return t=>{for(let e in i)r[e]=i[e](t);return r}},e2=(t,e)=>{let r=eQ.createTransformer(e),i=eX(t),n=eX(e);return i.numVars===n.numVars&&i.numColors===n.numColors&&i.numNumbers>=n.numNumbers?tZ(e1(i.values,n.values),r):(tN(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eJ(t,e))},e3=(t,e,r)=>{let i=e-t;return 0===i?1:(r-t)/i},e6=(t,e)=>r=>eB(t,e,r);function e4(t,e,{clamp:r=!0,ease:i,mixer:n}={}){let s=t.length;if(tN(s===e.length,"Both input and output ranges must be the same length"),1===s)return()=>e[0];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let o=function(t,e,r){let i=[],n=r||function(t){if("number"==typeof t);else if("string"==typeof t)return eI.test(t)?e$:e2;else if(Array.isArray(t))return e1;else if("object"==typeof t)return e5;return e6}(t[0]),s=t.length-1;for(let r=0;r<s;r++){let s=n(t[r],t[r+1]);e&&(s=tZ(Array.isArray(e)?e[r]||tN:e,s)),i.push(s)}return i}(e,i,n),a=o.length,l=e=>{let r=0;if(a>1)for(;r<t.length-2&&!(e<t[r+1]);r++);let i=e3(t[r],t[r+1],e);return o[r](i)};return r?e=>l(X(t[0],t[s-1],e)):l}function e9({duration:t=300,keyframes:e,times:r,ease:i="easeInOut"}){let n=ev(i)?i.map(eE):eE(i),s={done:!1,value:e[0]},o=e4((r&&r.length===e.length?r:function(t){let e=[0];return function(t,e){let r=t[t.length-1];for(let i=1;i<=e;i++){let n=e3(0,e,i);t.push(eB(r,1,n))}}(e,t.length-1),e}(e)).map(e=>e*t),e,{ease:Array.isArray(n)?n:e.map(()=>n||ey).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(s.value=o(e),s.done=e>=t,s)}}function e7(t,e,r){var i,n;let s=Math.max(e-5,0);return i=r-t(s),(n=e-s)?1e3/n*i:0}function e8(t,e){return t*Math.sqrt(1-e*e)}let rt=["duration","bounce"],re=["stiffness","damping","mass"];function rr(t,e){return e.some(e=>void 0!==t[e])}function ri({keyframes:t,restDelta:e,restSpeed:r,...i}){let n;let s=t[0],o=t[t.length-1],a={done:!1,value:s},{stiffness:l,damping:u,mass:c,duration:d,velocity:h,isResolvedFromDuration:p}=function(t){let e={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...t};if(!rr(t,re)&&rr(t,rt)){let r=function({duration:t=800,bounce:e=.25,velocity:r=0,mass:i=1}){let n,s;tN(t<=ea(10),"Spring duration must be 10 seconds or less");let o=1-e;o=X(.05,1,o),t=X(.01,10,el(t)),o<1?(n=e=>{let i=e*o,n=i*t;return .001-(i-r)/e8(e,o)*Math.exp(-n)},s=e=>{let i=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=e8(Math.pow(e,2),o);return(i*r+r-s)*Math.exp(-i)*(-n(e)+.001>0?-1:1)/a}):(n=e=>-.001+Math.exp(-e*t)*((e-r)*t+1),s=e=>t*t*(r-e)*Math.exp(-e*t));let a=function(t,e,r){let i=r;for(let r=1;r<12;r++)i-=t(i)/e(i);return i}(n,s,5/t);if(t=ea(t),isNaN(a))return{stiffness:100,damping:10,duration:t};{let e=Math.pow(a,2)*i;return{stiffness:e,damping:2*o*Math.sqrt(i*e),duration:t}}}(t);(e={...e,...r,mass:1}).isResolvedFromDuration=!0}return e}({...i,velocity:-el(i.velocity||0)}),m=h||0,f=u/(2*Math.sqrt(l*c)),g=o-s,y=el(Math.sqrt(l/c)),v=5>Math.abs(g);if(r||(r=v?.01:2),e||(e=v?.005:.5),f<1){let t=e8(y,f);n=e=>o-Math.exp(-f*y*e)*((m+f*y*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}else if(1===f)n=t=>o-Math.exp(-y*t)*(g+(m+y*g)*t);else{let t=y*Math.sqrt(f*f-1);n=e=>{let r=Math.exp(-f*y*e),i=Math.min(t*e,300);return o-r*((m+f*y*g)*Math.sinh(i)+t*g*Math.cosh(i))/t}}return{calculatedDuration:p&&d||null,next:t=>{let i=n(t);if(p)a.done=t>=d;else{let s=m;0!==t&&(s=f<1?e7(n,t,i):0);let l=Math.abs(s)<=r,u=Math.abs(o-i)<=e;a.done=l&&u}return a.value=a.done?o:i,a}}}function rn({keyframes:t,velocity:e=0,power:r=.8,timeConstant:i=325,bounceDamping:n=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:c}){let d,h;let p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,g=t=>void 0===a?l:void 0===l?a:Math.abs(a-t)<Math.abs(l-t)?a:l,y=r*e,v=p+y,x=void 0===o?v:o(v);x!==v&&(y=x-p);let b=t=>-y*Math.exp(-t/i),w=t=>x+b(t),P=t=>{let e=b(t),r=w(t);m.done=Math.abs(e)<=u,m.value=m.done?x:r},S=t=>{f(m.value)&&(d=t,h=ri({keyframes:[m.value,g(m.value)],velocity:e7(w,t,m.value),damping:n,stiffness:s,restDelta:u,restSpeed:c}))};return S(0),{calculatedDuration:null,next:t=>{let e=!1;return(h||void 0!==d||(e=!0,P(t),S(t)),void 0!==d&&t>d)?h.next(t-d):(e||P(t),m)}}}let rs=t=>{let e=({timestamp:e})=>t(e);return{start:()=>tF.update(e,!0),stop:()=>tO(e),now:()=>tU.isProcessing?tU.timestamp:performance.now()}};function ro(t){let e=0,r=t.next(e);for(;!r.done&&e<2e4;)e+=50,r=t.next(e);return e>=2e4?1/0:e}let ra={decay:rn,inertia:rn,tween:e9,keyframes:e9,spring:ri};function rl({autoplay:t=!0,delay:e=0,driver:r=rs,keyframes:i,type:n="keyframes",repeat:s=0,repeatDelay:o=0,repeatType:a="loop",onPlay:l,onStop:u,onComplete:c,onUpdate:d,...h}){let p,m,f,g,y,v=1,x=!1,b=()=>{m=new Promise(t=>{p=t})};b();let w=ra[n]||e9;w!==e9&&"number"!=typeof i[0]&&(g=e4([0,100],i,{clamp:!1}),i=[0,100]);let P=w({...h,keyframes:i});"mirror"===a&&(y=w({...h,keyframes:[...i].reverse(),velocity:-(h.velocity||0)}));let S="idle",A=null,T=null,k=null;null===P.calculatedDuration&&s&&(P.calculatedDuration=ro(P));let{calculatedDuration:j}=P,E=1/0,C=1/0;null!==j&&(C=(E=j+o)*(s+1)-o);let V=0,M=t=>{if(null===T)return;v>0&&(T=Math.min(T,t)),v<0&&(T=Math.min(t-C/v,T));let r=(V=null!==A?A:Math.round(t-T)*v)-e*(v>=0?1:-1),n=v>=0?r<0:r>C;V=Math.max(r,0),"finished"===S&&null===A&&(V=C);let l=V,u=P;if(s){let t=Math.min(V,C)/E,e=Math.floor(t),r=t%1;!r&&t>=1&&(r=1),1===r&&e--,(e=Math.min(e,s+1))%2&&("reverse"===a?(r=1-r,o&&(r-=o/E)):"mirror"===a&&(u=y)),l=X(0,1,r)*E}let c=n?{done:!1,value:i[0]}:u.next(l);g&&(c.value=g(c.value));let{done:h}=c;n||null===j||(h=v>=0?V>=C:V<=0);let p=null===A&&("finished"===S||"running"===S&&h);return d&&d(c.value),p&&R(),c},D=()=>{f&&f.stop(),f=void 0},L=()=>{S="idle",D(),p(),b(),T=k=null},R=()=>{S="finished",c&&c(),D(),p()},N=()=>{if(x)return;f||(f=r(M));let t=f.now();l&&l(),null!==A?T=t-A:T&&"finished"!==S||(T=t),"finished"===S&&b(),k=T,A=null,S="running",f.start()};t&&N();let I={then:(t,e)=>m.then(t,e),get time(){return el(V)},set time(newTime){V=newTime=ea(newTime),null===A&&f&&0!==v?T=f.now()-newTime/v:A=newTime},get duration(){return el(null===P.calculatedDuration?ro(P):P.calculatedDuration)},get speed(){return v},set speed(newSpeed){if(newSpeed===v||!f)return;v=newSpeed,I.time=el(V)},get state(){return S},play:N,pause:()=>{S="paused",A=V},stop:()=>{x=!0,"idle"!==S&&(S="idle",u&&u(),L())},cancel:()=>{null!==k&&M(k),L()},complete:()=>{S="finished"},sample:t=>(T=0,M(t))};return I}let ru=function(t){let e;return()=>(void 0===e&&(e=t()),e)}(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),rc=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),rd=(t,e)=>"spring"===e.type||"backgroundColor"===t||!function t(e){return!!(!e||"string"==typeof e&&eh[e]||ec(e)||Array.isArray(e)&&e.every(t))}(e.ease),rh={type:"spring",stiffness:500,damping:25,restSpeed:10},rp=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),rm={type:"keyframes",duration:.8},rf={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},rg=(t,{keyframes:e})=>e.length>2?rm:O.has(t)?t.startsWith("scale")?rp(e[1]):rh:rf,ry=(t,e)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eQ.test(e)||"0"===e)&&!e.startsWith("url(")),rv=new Set(["brightness","contrast","saturate","opacity"]);function rx(t){let[e,r]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[i]=r.match(J)||[];if(!i)return t;let n=r.replace(i,""),s=rv.has(e)?1:0;return i!==r&&(s*=100),e+"("+s+n+")"}let rb=/([a-z-]*)\(.*?\)/g,rw={...eQ,getAnimatableNone:t=>{let e=t.match(rb);return e?e.map(rx).join(" "):t}},rP={...td,color:eI,backgroundColor:eI,outlineColor:eI,fill:eI,stroke:eI,borderColor:eI,borderTopColor:eI,borderRightColor:eI,borderBottomColor:eI,borderLeftColor:eI,filter:rw,WebkitFilter:rw},rS=t=>rP[t];function rA(t,e){let r=rS(t);return r!==rw&&(r=eQ),r.getAnimatableNone?r.getAnimatableNone(e):void 0}let rT=t=>/^0[^.\s]+$/.test(t);function rk(t,e){return t[e]||t.default||t}let rj={skipAnimations:!1},rE=(t,e,r,i={})=>n=>{let s=rk(i,t)||{},o=s.delay||i.delay||0,{elapsed:a=0}=i;a-=ea(o);let l=function(t,e,r,i){let n,s;let o=ry(e,r);n=Array.isArray(r)?[...r]:[null,r];let a=void 0!==i.from?i.from:t.get(),l=[];for(let t=0;t<n.length;t++){var u;null===n[t]&&(n[t]=0===t?a:n[t-1]),("number"==typeof(u=n[t])?0===u:null!==u?"none"===u||"0"===u||rT(u):void 0)&&l.push(t),"string"==typeof n[t]&&"none"!==n[t]&&"0"!==n[t]&&(s=n[t])}if(o&&l.length&&s)for(let t=0;t<l.length;t++)n[l[t]]=rA(e,s);return n}(e,t,r,s),u=l[0],c=l[l.length-1],d=ry(t,u),h=ry(t,c);tN(d===h,`You are trying to animate ${t} from "${u}" to "${c}". ${u} is not an animatable value - to enable this animation set ${u} to a value animatable to ${c} via the \`style\` property.`);let p={keyframes:l,velocity:e.getVelocity(),ease:"easeOut",...s,delay:-a,onUpdate:t=>{e.set(t),s.onUpdate&&s.onUpdate(t)},onComplete:()=>{n(),s.onComplete&&s.onComplete()}};if(!function({when:t,delay:e,delayChildren:r,staggerChildren:i,staggerDirection:n,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(s)&&(p={...p,...rg(t,p)}),p.duration&&(p.duration=ea(p.duration)),p.repeatDelay&&(p.repeatDelay=ea(p.repeatDelay)),!d||!h||eu.current||!1===s.type||rj.skipAnimations)return function({keyframes:t,delay:e,onUpdate:r,onComplete:i}){let n=()=>(r&&r(t[t.length-1]),i&&i(),{time:0,speed:1,duration:0,play:tN,pause:tN,stop:tN,then:t=>(t(),Promise.resolve()),cancel:tN,complete:tN});return e?rl({keyframes:[0,1],duration:0,delay:e,onComplete:n}):n()}(eu.current?{...p,delay:0}:p);if(!i.isHandoff&&e.owner&&e.owner.current instanceof HTMLElement&&!e.owner.getProps().onUpdate){let r=function(t,e,{onUpdate:r,onComplete:i,...n}){let s,o;if(!(ru()&&rc.has(e)&&!n.repeatDelay&&"mirror"!==n.repeatType&&0!==n.damping&&"inertia"!==n.type))return!1;let a=!1,l=!1,u=()=>{o=new Promise(t=>{s=t})};u();let{keyframes:c,duration:d=300,ease:h,times:p}=n;if(rd(e,n)){let t=rl({...n,repeat:0,delay:0}),e={done:!1,value:c[0]},r=[],i=0;for(;!e.done&&i<2e4;)e=t.sample(i),r.push(e.value),i+=10;p=void 0,c=r,d=i-10,h="linear"}let m=function(t,e,r,{delay:i=0,duration:n,repeat:s=0,repeatType:o="loop",ease:a,times:l}={}){let u={[e]:r};l&&(u.offset=l);let c=function t(e){if(e)return ec(e)?ed(e):Array.isArray(e)?e.map(t):eh[e]}(a);return Array.isArray(c)&&(u.easing=c),t.animate(u,{delay:i,duration:n,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"})}(t.owner.current,e,c,{...n,duration:d,ease:h,times:p}),f=()=>{l=!1,m.cancel()},g=()=>{l=!0,tF.update(f),s(),u()};return m.onfinish=()=>{l||(t.set(function(t,{repeat:e,repeatType:r="loop"}){let i=e&&"loop"!==r&&e%2==1?0:t.length-1;return t[i]}(c,n)),i&&i(),g())},{then:(t,e)=>o.then(t,e),attachTimeline:t=>(m.timeline=t,m.onfinish=null,tN),get time(){return el(m.currentTime||0)},set time(newTime){m.currentTime=ea(newTime)},get speed(){return m.playbackRate},set speed(newSpeed){m.playbackRate=newSpeed},get duration(){return el(d)},play:()=>{a||(m.play(),tO(f))},pause:()=>m.pause(),stop:()=>{if(a=!0,"idle"===m.playState)return;let{currentTime:e}=m;if(e){let r=rl({...n,autoplay:!1});t.setWithVelocity(r.sample(e-10).value,r.sample(e).value,10)}g()},complete:()=>{l||m.finish()},cancel:g}}(e,t,p);if(r)return r}return rl(p)};function rC(t){return!!(z(t)&&t.add)}let rV=t=>/^\-?\d*\.?\d+$/.test(t);function rM(t,e){-1===t.indexOf(e)&&t.push(e)}function rD(t,e){let r=t.indexOf(e);r>-1&&t.splice(r,1)}class rL{constructor(){this.subscriptions=[]}add(t){return rM(this.subscriptions,t),()=>rD(this.subscriptions,t)}notify(t,e,r){let i=this.subscriptions.length;if(i){if(1===i)this.subscriptions[0](t,e,r);else for(let n=0;n<i;n++){let i=this.subscriptions[n];i&&i(t,e,r)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let rR=t=>!isNaN(parseFloat(t)),rN={current:void 0};class rI{constructor(t,e={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(t,e=!0)=>{this.prev=this.current,this.current=t;let{delta:r,timestamp:i}=tU;this.lastUpdated!==i&&(this.timeDelta=r,this.lastUpdated=i,tF.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>tF.postRender(this.velocityCheck),this.velocityCheck=({timestamp:t})=>{t!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=rR(this.current),this.owner=e.owner}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new rL);let r=this.events[t].add(e);return"change"===t?()=>{r(),tF.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,r){this.set(e),this.prev=t,this.timeDelta=r}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return rN.current&&rN.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t,e;return this.canTrackVelocity?(t=parseFloat(this.current)-parseFloat(this.prev),(e=this.timeDelta)?1e3/e*t:0):0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function rB(t,e){return new rI(t,e)}let rF=t=>e=>e.test(t),rO=[Y,to,ts,tn,tl,ta,{test:t=>"auto"===t,parse:t=>t}],rU=t=>rO.find(rF(t)),rz=[...rO,eI,eQ],rW=t=>rz.find(rF(t));function r$(t,e,{delay:r=0,transitionOverride:i,type:n}={}){let{transition:s=t.getDefaultTransition(),transitionEnd:o,...a}=t.makeTargetAnimatable(e),l=t.getValue("willChange");i&&(s=i);let u=[],c=n&&t.animationState&&t.animationState.getState()[n];for(let e in a){let i=t.getValue(e),n=a[e];if(!i||void 0===n||c&&function({protectedKeys:t,needsAnimating:e},r){let i=t.hasOwnProperty(r)&&!0!==e[r];return e[r]=!1,i}(c,e))continue;let o={delay:r,elapsed:0,...rk(s||{},e)};if(window.HandoffAppearAnimations){let r=t.getProps()[w];if(r){let t=window.HandoffAppearAnimations(r,e,i,tF);null!==t&&(o.elapsed=t,o.isHandoff=!0)}}let d=!o.isHandoff&&!function(t,e){let r=t.get();if(!Array.isArray(e))return r!==e;for(let t=0;t<e.length;t++)if(e[t]!==r)return!0}(i,n);if("spring"===o.type&&(i.getVelocity()||o.velocity)&&(d=!1),i.animation&&(d=!1),d)continue;i.start(rE(e,i,n,t.shouldReduceMotion&&O.has(e)?{type:!1}:o));let h=i.animation;rC(l)&&(l.add(e),h.then(()=>l.remove(e))),u.push(h)}return o&&Promise.all(u).then(()=>{o&&function(t,e){let r=eo(t,e),{transitionEnd:i={},transition:n={},...s}=r?t.makeTargetAnimatable(r,!1):{};for(let e in s={...s,...i}){let r=tD(s[e]);t.hasValue(e)?t.getValue(e).set(r):t.addValue(e,rB(r))}}(t,o)}),u}function r_(t,e,r={}){let i=eo(t,e,r.custom),{transition:n=t.getDefaultTransition()||{}}=i||{};r.transitionOverride&&(n=r.transitionOverride);let s=i?()=>Promise.all(r$(t,i,r)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(i=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=n;return function(t,e,r=0,i=0,n=1,s){let o=[],a=(t.variantChildren.size-1)*i,l=1===n?(t=0)=>t*i:(t=0)=>a-t*i;return Array.from(t.variantChildren).sort(rH).forEach((t,i)=>{t.notify("AnimationStart",e),o.push(r_(t,e,{...s,delay:r+l(i)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,s+i,o,a,r)}:()=>Promise.resolve(),{when:a}=n;if(!a)return Promise.all([s(),o(r.delay)]);{let[t,e]="beforeChildren"===a?[s,o]:[o,s];return t().then(()=>e())}}function rH(t,e){return t.sortNodePosition(e)}let rG=[...T].reverse(),rq=T.length;function rX(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}class rY extends t5{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:r})=>(function(t,e,r={}){let i;if(t.notify("AnimationStart",e),Array.isArray(e))i=Promise.all(e.map(e=>r_(t,e,r)));else if("string"==typeof e)i=r_(t,e,r);else{let n="function"==typeof e?eo(t,e,r.custom):e;i=Promise.all(r$(t,n,r))}return i.then(()=>t.notify("AnimationComplete",e))})(t,e,r))),r={animate:rX(!0),whileInView:rX(),whileHover:rX(),whileTap:rX(),whileDrag:rX(),whileFocus:rX(),exit:rX()},i=!0,n=(e,r)=>{let i=eo(t,r);if(i){let{transition:t,transitionEnd:r,...n}=i;e={...e,...n,...r}}return e};function s(s,o){let a=t.getProps(),l=t.getVariantContext(!0)||{},u=[],c=new Set,d={},h=1/0;for(let e=0;e<rq;e++){var p;let m=rG[e],f=r[m],g=void 0!==a[m]?a[m]:l[m],y=S(g),v=m===o?f.isActive:null;!1===v&&(h=e);let x=g===l[m]&&g!==a[m]&&y;if(x&&i&&t.manuallyAnimateOnMount&&(x=!1),f.protectedKeys={...d},!f.isActive&&null===v||!g&&!f.prevProp||A(g)||"boolean"==typeof g)continue;let b=(p=f.prevProp,("string"==typeof g?g!==p:!!Array.isArray(g)&&!es(g,p))||m===o&&f.isActive&&!x&&y||e>h&&y),w=!1,P=Array.isArray(g)?g:[g],T=P.reduce(n,{});!1===v&&(T={});let{prevResolvedValues:k={}}=f,j={...k,...T},E=t=>{b=!0,c.has(t)&&(w=!0,c.delete(t)),f.needsAnimating[t]=!0};for(let t in j){let e=T[t],r=k[t];if(!d.hasOwnProperty(t))(tV(e)&&tV(r)?es(e,r):e===r)?void 0!==e&&c.has(t)?E(t):f.protectedKeys[t]=!0:void 0!==e?E(t):c.add(t)}f.prevProp=g,f.prevResolvedValues=T,f.isActive&&(d={...d,...T}),i&&t.blockInitialAnimation&&(b=!1),b&&(!x||w)&&u.push(...P.map(t=>({animation:t,options:{type:m,...s}})))}if(c.size){let e={};c.forEach(r=>{let i=t.getBaseTarget(r);void 0!==i&&(e[r]=i)}),u.push({animation:e})}let m=!!u.length;return i&&(!1===a.initial||a.initial===a.animate)&&!t.manuallyAnimateOnMount&&(m=!1),i=!1,m?e(u):Promise.resolve()}return{animateChanges:s,setActive:function(e,i,n){var o;if(r[e].isActive===i)return Promise.resolve();null===(o=t.variantChildren)||void 0===o||o.forEach(t=>{var r;return null===(r=t.animationState)||void 0===r?void 0:r.setActive(e,i)}),r[e].isActive=i;let a=s(n,e);for(let t in r)r[t].protectedKeys={};return a},setAnimateFunction:function(r){e=r(t)},getState:()=>r}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();this.unmount(),A(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){}}let rZ=0;class rK extends t5{constructor(){super(...arguments),this.id=rZ++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e,custom:r}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t,{custom:null!=r?r:this.node.getProps().custom});e&&!t&&n.then(()=>e(this.id))}mount(){let{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}let rQ=(t,e)=>Math.abs(t-e);class rJ{constructor(t,e,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=r5(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,r=function(t,e){return Math.sqrt(rQ(t.x,e.x)**2+rQ(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!r)return;let{point:i}=t,{timestamp:n}=tU;this.history.push({...i,timestamp:n});let{onStart:s,onMove:o}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=r0(e,this.transformPagePoint),tF.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:r,onSessionEnd:i,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=r5("pointercancel"===t.type?this.lastMoveEventInfo:r0(e,this.transformPagePoint),this.history);this.startEvent&&r&&r(t,s),i&&i(t,s)},!tH(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=r,this.contextWindow=i||window;let s=r0(tG(t),this.transformPagePoint),{point:o}=s,{timestamp:a}=tU;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,r5(s,this.history)),this.removeListeners=tZ(tX(this.contextWindow,"pointermove",this.handlePointerMove),tX(this.contextWindow,"pointerup",this.handlePointerUp),tX(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),tO(this.updatePoint)}}function r0(t,e){return e?{point:e(t.point)}:t}function r1(t,e){return{x:t.x-e.x,y:t.y-e.y}}function r5({point:t},e){return{point:t,delta:r1(t,r2(e)),offset:r1(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let r=t.length-1,i=null,n=r2(t);for(;r>=0&&(i=t[r],!(n.timestamp-i.timestamp>ea(.1)));)r--;if(!i)return{x:0,y:0};let s=el(n.timestamp-i.timestamp);if(0===s)return{x:0,y:0};let o={x:(n.x-i.x)/s,y:(n.y-i.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,0)}}function r2(t){return t[t.length-1]}function r3(t){return t.max-t.min}function r6(t,e=0,r=.01){return Math.abs(t-e)<=r}function r4(t,e,r,i=.5){t.origin=i,t.originPoint=eB(e.min,e.max,t.origin),t.scale=r3(r)/r3(e),(r6(t.scale,1,1e-4)||isNaN(t.scale))&&(t.scale=1),t.translate=eB(r.min,r.max,t.origin)-t.originPoint,(r6(t.translate)||isNaN(t.translate))&&(t.translate=0)}function r9(t,e,r,i){r4(t.x,e.x,r.x,i?i.originX:void 0),r4(t.y,e.y,r.y,i?i.originY:void 0)}function r7(t,e,r){t.min=r.min+e.min,t.max=t.min+r3(e)}function r8(t,e,r){t.min=e.min-r.min,t.max=t.min+r3(e)}function it(t,e,r){r8(t.x,e.x,r.x),r8(t.y,e.y,r.y)}function ie(t,e,r){return{min:void 0!==e?t.min+e:void 0,max:void 0!==r?t.max+r-(t.max-t.min):void 0}}function ir(t,e){let r=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([r,i]=[i,r]),{min:r,max:i}}function ii(t,e,r){return{min:is(t,e),max:is(t,r)}}function is(t,e){return"number"==typeof t?t:t[e]||0}let io=()=>({translate:0,scale:1,origin:0,originPoint:0}),ia=()=>({x:io(),y:io()}),il=()=>({min:0,max:0}),iu=()=>({x:il(),y:il()});function ic(t){return[t("x"),t("y")]}function id({top:t,left:e,right:r,bottom:i}){return{x:{min:e,max:r},y:{min:t,max:i}}}function ih(t){return void 0===t||1===t}function ip({scale:t,scaleX:e,scaleY:r}){return!ih(t)||!ih(e)||!ih(r)}function im(t){return ip(t)||ig(t)||t.z||t.rotate||t.rotateX||t.rotateY}function ig(t){var e,r;return(e=t.x)&&"0%"!==e||(r=t.y)&&"0%"!==r}function iy(t,e,r,i,n){return void 0!==n&&(t=i+n*(t-i)),i+r*(t-i)+e}function iv(t,e=0,r=1,i,n){t.min=iy(t.min,e,r,i,n),t.max=iy(t.max,e,r,i,n)}function ix(t,{x:e,y:r}){iv(t.x,e.translate,e.scale,e.originPoint),iv(t.y,r.translate,r.scale,r.originPoint)}function ib(t){return Number.isInteger(t)?t:t>1.0000000000001||t<.999999999999?t:1}function iw(t,e){t.min=t.min+e,t.max=t.max+e}function iP(t,e,[r,i,n]){let s=void 0!==e[n]?e[n]:.5,o=eB(t.min,t.max,s);iv(t,e[r],e[i],o,e.scale)}let iS=["x","scaleX","originX"],iA=["y","scaleY","originY"];function iT(t,e){iP(t.x,e,iS),iP(t.y,e,iA)}function ik(t,e){return id(function(t,e){if(!e)return t;let r=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:r.y,left:r.x,bottom:i.y,right:i.x}}(t.getBoundingClientRect(),e))}let ij=({current:t})=>t?t.ownerDocument.defaultView:null,iE=new WeakMap;class iC{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iu(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let{dragSnapToOrigin:i}=this.getProps();this.panSession=new rJ(t,{onSessionStart:t=>{let{dragSnapToOrigin:r}=this.getProps();r?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(tG(t,"page").point)},onStart:(t,e)=>{let{drag:r,dragPropagation:i,onDragStart:n}=this.getProps();if(r&&!i&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=t0(r),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),ic(t=>{let e=this.getAxisMotionValue(t).get()||0;if(ts.test(e)){let{projection:r}=this.visualElement;if(r&&r.layout){let i=r.layout.layoutBox[t];if(i){let t=r3(i);e=parseFloat(e)/100*t}}}this.originPoint[t]=e}),n&&tF.update(()=>n(t,e),!1,!0);let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:r,dragDirectionLock:i,onDirectionLock:n,onDrag:s}=this.getProps();if(!r&&!this.openGlobalLock)return;let{offset:o}=e;if(i&&null===this.currentDirection){this.currentDirection=function(t,e=10){let r=null;return Math.abs(t.y)>e?r="y":Math.abs(t.x)>e&&(r="x"),r}(o),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>ic(t=>{var e;return"paused"===this.getAnimationState(t)&&(null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:ij(this.visualElement)})}stop(t,e){let r=this.isDragging;if(this.cancel(),!r)return;let{velocity:i}=e;this.startAnimation(i);let{onDragEnd:n}=this.getProps();n&&tF.update(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,r){let{drag:i}=this.getProps();if(!r||!iV(t,i,this.currentDirection))return;let n=this.getAxisMotionValue(t),s=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:r},i){return void 0!==e&&t<e?t=i?eB(e,t,i.min):Math.max(t,e):void 0!==r&&t>r&&(t=i?eB(r,t,i.max):Math.min(t,r)),t}(s,this.constraints[t],this.elastic[t])),n.set(s)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:r}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(t=this.visualElement.projection)||void 0===t?void 0:t.layout,n=this.constraints;e&&P(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&i?this.constraints=function(t,{top:e,left:r,bottom:i,right:n}){return{x:ie(t.x,r,n),y:ie(t.y,e,i)}}(i.layoutBox,e):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:ii(t,"left","right"),y:ii(t,"top","bottom")}}(r),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&ic(t=>{this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let r={};return void 0!==e.min&&(r.min=e.min-t.min),void 0!==e.max&&(r.max=e.max-t.min),r}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:r}=this.getProps();if(!e||!P(e))return!1;let i=e.current;tN(null!==i,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let s=function(t,e,r){let i=ik(t,r),{scroll:n}=e;return n&&(iw(i.x,n.offset.x),iw(i.y,n.offset.y)),i}(i,n.root,this.visualElement.getTransformPagePoint()),o={x:ir((t=n.layout.layoutBox).x,s.x),y:ir(t.y,s.y)};if(r){let t=r(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=id(t))}return o}startAnimation(t){let{drag:e,dragMomentum:r,dragElastic:i,dragTransition:n,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(ic(o=>{if(!iV(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:r?t[o]:0,bounceStiffness:i?200:1e6,bounceDamping:i?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let r=this.getAxisMotionValue(t);return r.start(rE(t,r,0,e))}stopAnimation(){ic(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){ic(t=>{var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.pause()})}getAnimationState(t){var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.state}getAxisMotionValue(t){let e="_drag"+t.toUpperCase(),r=this.visualElement.getProps();return r[e]||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){ic(e=>{let{drag:r}=this.getProps();if(!iV(e,r,this.currentDirection))return;let{projection:i}=this.visualElement,n=this.getAxisMotionValue(e);if(i&&i.layout){let{min:r,max:s}=i.layout.layoutBox[e];n.set(t[e]-eB(r,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:r}=this.visualElement;if(!P(e)||!r||!this.constraints)return;this.stopAnimation();let i={x:0,y:0};ic(t=>{let e=this.getAxisMotionValue(t);if(e){let r=e.get();i[t]=function(t,e){let r=.5,i=r3(t),n=r3(e);return n>i?r=e3(e.min,e.max-i,t.min):i>n&&(r=e3(t.min,t.max-n,e.min)),X(0,1,r)}({min:r,max:r},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),ic(e=>{if(!iV(e,t,null))return;let r=this.getAxisMotionValue(e),{min:n,max:s}=this.constraints[e];r.set(eB(n,s,i[e]))})}addListeners(){if(!this.visualElement.current)return;iE.set(this.visualElement,this);let t=tX(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:r=!0}=this.getProps();e&&r&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();P(t)&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,i=r.addEventListener("measure",e);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),e();let n=t_(window,"resize",()=>this.scalePositionWithinConstraints()),s=r.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(ic(e=>{let r=this.getAxisMotionValue(e);r&&(this.originPoint[e]+=t[e].translate,r.set(r.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),i(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:n=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:r,dragPropagation:i,dragConstraints:n,dragElastic:s,dragMomentum:o}}}function iV(t,e,r){return(!0===e||e===t)&&(null===r||r===t)}class iM extends t5{constructor(t){super(t),this.removeGroupControls=tN,this.removeListeners=tN,this.controls=new iC(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||tN}unmount(){this.removeGroupControls(),this.removeListeners()}}let iD=t=>(e,r)=>{t&&tF.update(()=>t(e,r))};class iL extends t5{constructor(){super(...arguments),this.removePointerDownListener=tN}onPointerDown(t){this.session=new rJ(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:ij(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:iD(t),onStart:iD(e),onMove:r,onEnd:(t,e)=>{delete this.session,i&&tF.update(()=>i(t,e))}}}mount(){this.removePointerDownListener=tX(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let iR={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function iN(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let iI={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!to.test(t))return t;t=parseFloat(t)}let r=iN(t,e.target.x),i=iN(t,e.target.y);return`${r}% ${i}%`}};class iB extends s.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:r,layoutId:i}=this.props,{projection:n}=t;Object.assign(B,iO),n&&(e.group&&e.group.add(n),r&&r.register&&i&&r.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),iR.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:r,drag:i,isPresent:n}=this.props,s=r.projection;return s&&(s.isPresent=n,i||t.layoutDependency!==e||void 0===e?s.willUpdate():this.safeToRemove(),t.isPresent===n||(n?s.promote():s.relegate()||tF.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function iF(t){let[e,r]=function(){let t=(0,s.useContext)(g);if(null===t)return[!0,null];let{isPresent:e,onExitComplete:r,register:i}=t,n=(0,s.useId)();return(0,s.useEffect)(()=>i(n),[]),!e&&r?[!1,()=>r&&r(n)]:[!0]}(),i=(0,s.useContext)(D);return s.createElement(iB,{...t,layoutGroup:i,switchLayoutGroup:(0,s.useContext)(L),isPresent:e,safeToRemove:r})}let iO={borderRadius:{...iI,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:iI,borderTopRightRadius:iI,borderBottomLeftRadius:iI,borderBottomRightRadius:iI,boxShadow:{correct:(t,{treeScale:e,projectionDelta:r})=>{let i=eQ.parse(t);if(i.length>5)return t;let n=eQ.createTransformer(t),s="number"!=typeof i[0]?1:0,o=r.x.scale*e.x,a=r.y.scale*e.y;i[0+s]/=o,i[1+s]/=a;let l=eB(o,a,.5);return"number"==typeof i[2+s]&&(i[2+s]/=l),"number"==typeof i[3+s]&&(i[3+s]/=l),n(i)}}},iU=["TopLeft","TopRight","BottomLeft","BottomRight"],iz=iU.length,iW=t=>"string"==typeof t?parseFloat(t):t,i$=t=>"number"==typeof t||to.test(t);function i_(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let iH=iq(0,.5,eP),iG=iq(.5,.95,tN);function iq(t,e,r){return i=>i<t?0:i>e?1:r(e3(t,e,i))}function iX(t,e){t.min=e.min,t.max=e.max}function iY(t,e){iX(t.x,e.x),iX(t.y,e.y)}function iZ(t,e,r,i,n){return t-=e,t=i+1/r*(t-i),void 0!==n&&(t=i+1/n*(t-i)),t}function iK(t,e,[r,i,n],s,o){!function(t,e=0,r=1,i=.5,n,s=t,o=t){if(ts.test(e)&&(e=parseFloat(e),e=eB(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=eB(s.min,s.max,i);t===s&&(a-=e),t.min=iZ(t.min,e,r,a,n),t.max=iZ(t.max,e,r,a,n)}(t,e[r],e[i],e[n],e.scale,s,o)}let iQ=["x","scaleX","originX"],iJ=["y","scaleY","originY"];function i0(t,e,r,i){iK(t.x,e,iQ,r?r.x:void 0,i?i.x:void 0),iK(t.y,e,iJ,r?r.y:void 0,i?i.y:void 0)}function i1(t){return 0===t.translate&&1===t.scale}function i5(t){return i1(t.x)&&i1(t.y)}function i2(t,e){return Math.round(t.x.min)===Math.round(e.x.min)&&Math.round(t.x.max)===Math.round(e.x.max)&&Math.round(t.y.min)===Math.round(e.y.min)&&Math.round(t.y.max)===Math.round(e.y.max)}function i3(t){return r3(t.x)/r3(t.y)}class i6{constructor(){this.members=[]}add(t){rM(this.members,t),t.scheduleRender()}remove(t){if(rD(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e;let r=this.members.findIndex(e=>t===e);if(0===r)return!1;for(let t=r;t>=0;t--){let r=this.members[t];if(!1!==r.isPresent){e=r;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,e&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:i}=t.options;!1===i&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:r}=t;e.onExitComplete&&e.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function i4(t,e,r){let i="",n=t.x.translate/e.x,s=t.y.translate/e.y;if((n||s)&&(i=`translate3d(${n}px, ${s}px, 0) `),(1!==e.x||1!==e.y)&&(i+=`scale(${1/e.x}, ${1/e.y}) `),r){let{rotate:t,rotateX:e,rotateY:n}=r;t&&(i+=`rotate(${t}deg) `),e&&(i+=`rotateX(${e}deg) `),n&&(i+=`rotateY(${n}deg) `)}let o=t.x.scale*e.x,a=t.y.scale*e.y;return(1!==o||1!==a)&&(i+=`scale(${o}, ${a})`),i||"none"}let i9=(t,e)=>t.depth-e.depth;class i7{constructor(){this.children=[],this.isDirty=!1}add(t){rM(this.children,t),this.isDirty=!0}remove(t){rD(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(i9),this.isDirty=!1,this.children.forEach(t)}}let i8=["","X","Y","Z"],nt={visibility:"hidden"},ne=0,nr={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function ni({attachResizeListener:t,defaultParent:e,measureScroll:r,checkIsScrollRoot:i,resetTransform:n}){return class{constructor(t={},r=null==e?void 0:e()){this.id=ne++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,nr.totalNodes=nr.resolvedTargetDeltas=nr.recalculatedProjection=0,this.nodes.forEach(no),this.nodes.forEach(np),this.nodes.forEach(nm),this.nodes.forEach(na),window.MotionDebug&&window.MotionDebug.record(nr)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=r?r.root||r:this,this.path=r?[...r.path,r]:[],this.parent=r,this.depth=r?r.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new i7)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new rL),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let r=this.eventHandlers.get(t);r&&r.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,r=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:i,layout:n,visualElement:s}=this.options;if(s&&!s.current&&s.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),r&&(n||i)&&(this.isLayoutDirty=!0),t){let r;let i=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,r&&r(),r=function(t,e){let r=performance.now(),i=({timestamp:e})=>{let n=e-r;n>=250&&(tO(i),t(n-250))};return tF.read(i,!0),()=>tO(i)}(i,0),iR.hasAnimatedSinceResize&&(iR.hasAnimatedSinceResize=!1,this.nodes.forEach(nh))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&s&&(i||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeTargetChanged:r,layout:i})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let n=this.options.transition||s.getDefaultTransition()||nb,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=s.getProps(),l=!this.targetLayout||!i2(this.targetLayout,i)||r,u=!e&&r;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);let e={...rk(n,"layout"),onPlay:o,onComplete:a};(s.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||nh(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,tO(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(nf),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:r}=this.options;if(void 0===e&&!r)return;let i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nu);return}this.isUpdating||this.nodes.forEach(nc),this.isUpdating=!1,this.nodes.forEach(nd),this.nodes.forEach(nn),this.nodes.forEach(ns),this.clearAllSnapshots();let t=performance.now();tU.delta=X(0,1e3/60,t-tU.timestamp),tU.timestamp=t,tU.isProcessing=!0,tz.update.process(tU),tz.preRender.process(tU),tz.render.process(tU),tU.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(nl),this.sharedNodes.forEach(ng)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,tF.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){tF.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iu(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&(this.scroll={animationId:this.root.animationId,phase:t,isRoot:i(this.instance),offset:r(this.instance)})}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform,e=this.projectionDelta&&!i5(this.projectionDelta),r=this.getTransformTemplate(),i=r?r(this.latestValues,""):void 0,s=i!==this.prevTransformTemplateValue;t&&(e||im(this.latestValues)||s)&&(n(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let r=this.measurePageBox(),i=this.removeElementScroll(r);return t&&(i=this.removeTransform(i)),nS((e=i).x),nS(e.y),{animationId:this.root.animationId,measuredBox:r,layoutBox:i,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return iu();let e=t.measureViewportBox(),{scroll:r}=this.root;return r&&(iw(e.x,r.offset.x),iw(e.y,r.offset.y)),e}removeElementScroll(t){let e=iu();iY(e,t);for(let r=0;r<this.path.length;r++){let i=this.path[r],{scroll:n,options:s}=i;if(i!==this.root&&n&&s.layoutScroll){if(n.isRoot){iY(e,t);let{scroll:r}=this.root;r&&(iw(e.x,-r.offset.x),iw(e.y,-r.offset.y))}iw(e.x,n.offset.x),iw(e.y,n.offset.y)}}return e}applyTransform(t,e=!1){let r=iu();iY(r,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];!e&&i.options.layoutScroll&&i.scroll&&i!==i.root&&iT(r,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),im(i.latestValues)&&iT(r,i.latestValues)}return im(this.latestValues)&&iT(r,this.latestValues),r}removeTransform(t){let e=iu();iY(e,t);for(let t=0;t<this.path.length;t++){let r=this.path[t];if(!r.instance||!im(r.latestValues))continue;ip(r.latestValues)&&r.updateSnapshot();let i=iu();iY(i,r.measurePageBox()),i0(e,r.latestValues,r.snapshot?r.snapshot.layoutBox:void 0,i)}return im(this.latestValues)&&i0(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==tU.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e,r,i,n;let s=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=s.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=s.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=s.isSharedProjectionDirty);let o=!!this.resumingFrom||this!==s;if(!(t||o&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=tU.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iu(),this.relativeTargetOrigin=iu(),it(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),iY(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=iu(),this.targetWithTransforms=iu()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),r=this.target,i=this.relativeTarget,n=this.relativeParent.target,r7(r.x,i.x,n.x),r7(r.y,i.y,n.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):iY(this.target,this.layout.layoutBox),ix(this.target,this.targetDelta)):iY(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iu(),this.relativeTargetOrigin=iu(),it(this.relativeTargetOrigin,this.target,t.target),iY(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}nr.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||ip(this.parent.latestValues)||ig(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),r=!!this.resumingFrom||this!==e,i=!0;if((this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty))&&(i=!1),r&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===tU.timestamp&&(i=!1),i)return;let{layout:n,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||s))return;iY(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;(function(t,e,r,i=!1){let n,s;let o=r.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(n=r[a]).projectionDelta;let o=n.instance;(!o||!o.style||"contents"!==o.style.display)&&(i&&n.options.layoutScroll&&n.scroll&&n!==n.root&&iT(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,ix(t,s)),i&&im(n.latestValues)&&iT(t,n.latestValues))}e.x=ib(e.x),e.y=ib(e.y)}})(this.layoutCorrected,this.treeScale,this.path,r),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox);let{target:l}=e;if(!l){this.projectionTransform&&(this.projectionDelta=ia(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=ia(),this.projectionDeltaWithTransform=ia());let u=this.projectionTransform;r9(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.projectionTransform=i4(this.projectionDelta,this.treeScale),(this.projectionTransform!==u||this.treeScale.x!==o||this.treeScale.y!==a)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),nr.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(t,e=!1){let r;let i=this.snapshot,n=i?i.latestValues:{},s={...this.latestValues},o=ia();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=iu(),l=(i?i.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,d=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(nx));this.animationProgress=0,this.mixTargetDelta=e=>{let i=e/1e3;if(ny(o.x,t.x,i),ny(o.y,t.y,i),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,h,p,m;it(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,nv(p.x,m.x,a.x,i),nv(p.y,m.y,a.y,i),r&&(u=this.relativeTarget,h=r,u.x.min===h.x.min&&u.x.max===h.x.max&&u.y.min===h.y.min&&u.y.max===h.y.max)&&(this.isProjectionDirty=!1),r||(r=iu()),iY(r,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,r,i,n,s){n?(t.opacity=eB(0,void 0!==r.opacity?r.opacity:1,iH(i)),t.opacityExit=eB(void 0!==e.opacity?e.opacity:1,0,iG(i))):s&&(t.opacity=eB(void 0!==e.opacity?e.opacity:1,void 0!==r.opacity?r.opacity:1,i));for(let n=0;n<iz;n++){let s=`border${iU[n]}Radius`,o=i_(e,s),a=i_(r,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||i$(o)===i$(a)?(t[s]=Math.max(eB(iW(o),iW(a),i),0),(ts.test(a)||ts.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||r.rotate)&&(t.rotate=eB(e.rotate||0,r.rotate||0,i))}(s,n,this.latestValues,i,d,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=i},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(tO(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=tF.update(()=>{iR.hasAnimatedSinceResize=!0,this.currentAnimation=function(t,e,r){let i=z(0)?0:rB(0);return i.start(rE("",i,1e3,r)),i.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:r,layout:i,latestValues:n}=t;if(e&&r&&i){if(this!==t&&this.layout&&i&&nA(this.options.animationType,this.layout.layoutBox,i.layoutBox)){r=this.target||iu();let e=r3(this.layout.layoutBox.x);r.x.min=t.target.x.min,r.x.max=r.x.min+e;let i=r3(this.layout.layoutBox.y);r.y.min=t.target.y.min,r.y.max=r.y.min+i}iY(e,r),iT(e,n),r9(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new i6),this.sharedNodes.get(t).add(e);let r=e.options.initialPromotionConfig;e.promote({transition:r?r.transition:void 0,preserveFollowOpacity:r&&r.shouldPreserveFollowOpacity?r.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null===(t=this.getStack())||void 0===t?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null===(t=this.getStack())||void 0===t?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:r}={}){let i=this.getStack();i&&i.promote(this,r),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:r}=t;if((r.rotate||r.rotateX||r.rotateY||r.rotateZ)&&(e=!0),!e)return;let i={};for(let e=0;e<i8.length;e++){let n="rotate"+i8[e];r[n]&&(i[n]=r[n],t.setStaticValue(n,0))}for(let e in t.render(),i)t.setStaticValue(e,i[e]);t.scheduleRender()}getProjectionStyles(t){var e,r;if(!this.instance||this.isSVG)return;if(!this.isVisible)return nt;let i={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,i.opacity="",i.pointerEvents=tL(null==t?void 0:t.pointerEvents)||"",i.transform=n?n(this.latestValues,""):"none",i;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=tL(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!im(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1),e}let o=s.animationValues||s.latestValues;this.applyTransformsToTarget(),i.transform=i4(this.projectionDeltaWithTransform,this.treeScale,o),n&&(i.transform=n(o,i.transform));let{x:a,y:l}=this.projectionDelta;for(let t in i.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,s.animationValues?i.opacity=s===this?null!==(r=null!==(e=o.opacity)&&void 0!==e?e:this.latestValues.opacity)&&void 0!==r?r:1:this.preserveOpacity?this.latestValues.opacity:o.opacityExit:i.opacity=s===this?void 0!==o.opacity?o.opacity:"":void 0!==o.opacityExit?o.opacityExit:0,B){if(void 0===o[t])continue;let{correct:e,applyTo:r}=B[t],n="none"===i.transform?o[t]:e(o[t],s);if(r){let t=r.length;for(let e=0;e<t;e++)i[r[e]]=n}else i[t]=n}return this.options.layoutId&&(i.pointerEvents=s===this?tL(null==t?void 0:t.pointerEvents)||"":"none"),i}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null===(e=t.currentAnimation)||void 0===e?void 0:e.stop()}),this.root.nodes.forEach(nu),this.root.sharedNodes.clear()}}}function nn(t){t.updateLayout()}function ns(t){var e;let r=(null===(e=t.resumeFrom)||void 0===e?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&r&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:i}=t.layout,{animationType:n}=t.options,s=r.source!==t.layout.source;"size"===n?ic(t=>{let i=s?r.measuredBox[t]:r.layoutBox[t],n=r3(i);i.min=e[t].min,i.max=i.min+n}):nA(n,r.layoutBox,e)&&ic(i=>{let n=s?r.measuredBox[i]:r.layoutBox[i],o=r3(e[i]);n.max=n.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[i].max=t.relativeTarget[i].min+o)});let o=ia();r9(o,e,r.layoutBox);let a=ia();s?r9(a,t.applyTransform(i,!0),r.measuredBox):r9(a,e,r.layoutBox);let l=!i5(o),u=!1;if(!t.resumeFrom){let i=t.getClosestProjectingParent();if(i&&!i.resumeFrom){let{snapshot:n,layout:s}=i;if(n&&s){let o=iu();it(o,r.layoutBox,n.layoutBox);let a=iu();it(a,e,s.layoutBox),i2(o,a)||(u=!0),i.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=i)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:r,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function no(t){nr.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function na(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function nl(t){t.clearSnapshot()}function nu(t){t.clearMeasurements()}function nc(t){t.isLayoutDirty=!1}function nd(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function nh(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function np(t){t.resolveTargetDelta()}function nm(t){t.calcProjection()}function nf(t){t.resetRotation()}function ng(t){t.removeLeadSnapshot()}function ny(t,e,r){t.translate=eB(e.translate,0,r),t.scale=eB(e.scale,1,r),t.origin=e.origin,t.originPoint=e.originPoint}function nv(t,e,r,i){t.min=eB(e.min,r.min,i),t.max=eB(e.max,r.max,i)}function nx(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let nb={duration:.45,ease:[.4,0,.1,1]},nw=t=>"undefined"!=typeof navigator&&navigator.userAgent.toLowerCase().includes(t),nP=nw("applewebkit/")&&!nw("chrome/")?Math.round:tN;function nS(t){t.min=nP(t.min),t.max=nP(t.max)}function nA(t,e,r){return"position"===t||"preserve-aspect"===t&&!r6(i3(e),i3(r),.2)}let nT=ni({attachResizeListener:(t,e)=>t_(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),nk={current:void 0},nj=ni({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!nk.current){let t=new nT({});t.mount(window),t.setOptions({layoutScroll:!0}),nk.current=t}return nk.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position}),nE=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function nC(t,e,r=1){tN(r<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[i,n]=function(t){let e=nE.exec(t);if(!e)return[,];let[,r,i]=e;return[r,i]}(t);if(!i)return;let s=window.getComputedStyle(e).getPropertyValue(i);if(s){let t=s.trim();return rV(t)?parseFloat(t):t}return G(n)?nC(n,e,r+1):n}let nV=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),nM=t=>nV.has(t),nD=t=>Object.keys(t).some(nM),nL=t=>t===Y||t===to,nR=(t,e)=>parseFloat(t.split(", ")[e]),nN=(t,e)=>(r,{transform:i})=>{if("none"===i||!i)return 0;let n=i.match(/^matrix3d\((.+)\)$/);if(n)return nR(n[1],e);{let e=i.match(/^matrix\((.+)\)$/);return e?nR(e[1],t):0}},nI=new Set(["x","y","z"]),nB=F.filter(t=>!nI.has(t)),nF={width:({x:t},{paddingLeft:e="0",paddingRight:r="0"})=>t.max-t.min-parseFloat(e)-parseFloat(r),height:({y:t},{paddingTop:e="0",paddingBottom:r="0"})=>t.max-t.min-parseFloat(e)-parseFloat(r),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:nN(4,13),y:nN(5,14)};nF.translateX=nF.x,nF.translateY=nF.y;let nO=(t,e,r)=>{let i=e.measureViewportBox(),n=getComputedStyle(e.current),{display:s}=n,o={};"none"===s&&e.setStaticValue("display",t.display||"block"),r.forEach(t=>{o[t]=nF[t](i,n)}),e.render();let a=e.measureViewportBox();return r.forEach(r=>{let i=e.getValue(r);i&&i.jump(o[r]),t[r]=nF[r](a,n)}),t},nU=(t,e,r={},i={})=>{e={...e},i={...i};let n=Object.keys(e).filter(nM),s=[],o=!1,a=[];if(n.forEach(n=>{let l;let u=t.getValue(n);if(!t.hasValue(n))return;let c=r[n],d=rU(c),h=e[n];if(tV(h)){let t=h.length,e=null===h[0]?1:0;d=rU(c=h[e]);for(let r=e;r<t&&null!==h[r];r++)l?tN(rU(h[r])===l,"All keyframes must be of the same type"):tN((l=rU(h[r]))===d||nL(d)&&nL(l),"Keyframes must be of the same dimension as the current value")}else l=rU(h);if(d!==l){if(nL(d)&&nL(l)){let t=u.get();"string"==typeof t&&u.set(parseFloat(t)),"string"==typeof h?e[n]=parseFloat(h):Array.isArray(h)&&l===to&&(e[n]=h.map(parseFloat))}else(null==d?void 0:d.transform)&&(null==l?void 0:l.transform)&&(0===c||0===h)?0===c?u.set(l.transform(c)):e[n]=d.transform(h):(o||(s=function(t){let e=[];return nB.forEach(r=>{let i=t.getValue(r);void 0!==i&&(e.push([r,i.get()]),i.set(r.startsWith("scale")?1:0))}),e.length&&t.render(),e}(t),o=!0),a.push(n),i[n]=void 0!==i[n]?i[n]:e[n],u.jump(h))}}),!a.length)return{target:e,transitionEnd:i};{let r=a.indexOf("height")>=0?window.pageYOffset:null,n=nO(e,t,a);return s.length&&s.forEach(([e,r])=>{t.getValue(e).set(r)}),t.render(),y&&null!==r&&window.scrollTo({top:r}),{target:n,transitionEnd:i}}},nz=(t,e,r,i)=>{let n=function(t,{...e},r){let i=t.current;if(!(i instanceof Element))return{target:e,transitionEnd:r};for(let n in r&&(r={...r}),t.values.forEach(t=>{let e=t.get();if(!G(e))return;let r=nC(e,i);r&&t.set(r)}),e){let t=e[n];if(!G(t))continue;let s=nC(t,i);s&&(e[n]=s,r||(r={}),void 0===r[n]&&(r[n]=t))}return{target:e,transitionEnd:r}}(t,e,i);return function(t,e,r,i){return nD(e)?nU(t,e,r,i):{target:e,transitionEnd:i}}(t,e=n.target,r,i=n.transitionEnd)},nW={current:null},n$={current:!1},n_=new WeakMap,nH=Object.keys(M),nG=nH.length,nq=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],nX=k.length;class nY{constructor({parent:t,props:e,presenceContext:r,reducedMotionConfig:i,visualState:n},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>tF.render(this.render,!1,!0);let{latestValues:o,renderState:a}=n;this.latestValues=o,this.baseTarget={...o},this.initialValues=e.initial?{...o}:{},this.renderState=a,this.parent=t,this.props=e,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=s,this.isControllingVariants=j(e),this.isVariantNode=E(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:l,...u}=this.scrapeMotionValuesFromProps(e,{});for(let t in u){let e=u[t];void 0!==o[t]&&z(e)&&(e.set(o[t],!1),rC(l)&&l.add(t))}}scrapeMotionValuesFromProps(t,e){return{}}mount(t){this.current=t,n_.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),n$.current||function(){if(n$.current=!0,y){if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>nW.current=t.matches;t.addListener(e),e()}else nW.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||nW.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in n_.delete(this.current),this.projection&&this.projection.unmount(),tO(this.notifyUpdate),tO(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,e){let r=O.has(t),i=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&tF.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),n=e.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{i(),n()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}loadFeatures({children:t,...e},r,i,n){let s,o;for(let t=0;t<nG;t++){let r=nH[t],{isEnabled:i,Feature:n,ProjectionNode:a,MeasureLayout:l}=M[r];a&&(s=a),i(e)&&(!this.features[r]&&n&&(this.features[r]=new n(this)),l&&(o=l))}if(("html"===this.type||"svg"===this.type)&&!this.projection&&s){this.projection=new s(this.latestValues,this.parent&&this.parent.projection);let{layoutId:t,layout:r,drag:i,dragConstraints:o,layoutScroll:a,layoutRoot:l}=e;this.projection.setOptions({layoutId:t,layout:r,alwaysMeasureLayout:!!i||o&&P(o),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:"string"==typeof r?r:"both",initialPromotionConfig:n,layoutScroll:a,layoutRoot:l})}return o}updateFeatures(){for(let t in this.features){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iu()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}makeTargetAnimatable(t,e=!0){return this.makeTargetAnimatableFromInstance(t,this.props,e)}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<nq.length;e++){let r=nq[e];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);let i=t["on"+r];i&&(this.propEventSubscriptions[r]=this.on(r,i))}this.prevMotionValues=function(t,e,r){let{willChange:i}=e;for(let n in e){let s=e[n],o=r[n];if(z(s))t.addValue(n,s),rC(i)&&i.add(n);else if(z(o))t.addValue(n,rB(s,{owner:t})),rC(i)&&i.remove(n);else if(o!==s){if(t.hasValue(n)){let e=t.getValue(n);e.hasAnimated||e.set(s)}else{let e=t.getStaticValue(n);t.addValue(n,rB(void 0!==e?e:s,{owner:t}))}}}for(let i in r)void 0===e[i]&&t.removeValue(i);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){let t=this.parent&&this.parent.getVariantContext()||{};return void 0!==this.props.initial&&(t.initial=this.props.initial),t}let e={};for(let t=0;t<nX;t++){let r=k[t],i=this.props[r];(S(i)||!1===i)&&(e[r]=i)}return e}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){e!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,e)),this.values.set(t,e),this.latestValues[t]=e.get()}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return void 0===r&&void 0!==e&&(r=rB(e,{owner:this}),this.addValue(t,r)),r}readValue(t){var e;return void 0===this.latestValues[t]&&this.current?null!==(e=this.getBaseTargetFromProps(this.props,t))&&void 0!==e?e:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t]}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;let{initial:r}=this.props,i="string"==typeof r||"object"==typeof r?null===(e=tC(this.props,r))||void 0===e?void 0:e[t]:void 0;if(r&&void 0!==i)return i;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||z(n)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new rL),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class nZ extends nY{sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:r}){delete e[t],delete r[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:e,...r},{transformValues:i},n){let s=function(t,e,r){let i={};for(let n in t){let t=function(t,e){if(e)return(e[t]||e.default||e).from}(n,e);if(void 0!==t)i[n]=t;else{let t=r.getValue(n);t&&(i[n]=t.get())}}return i}(r,t||{},this);if(i&&(e&&(e=i(e)),r&&(r=i(r)),s&&(s=i(s))),n){!function(t,e,r){var i,n;let s=Object.keys(e).filter(e=>!t.hasValue(e)),o=s.length;if(o)for(let a=0;a<o;a++){let o=s[a],l=e[o],u=null;Array.isArray(l)&&(u=l[0]),null===u&&(u=null!==(n=null!==(i=r[o])&&void 0!==i?i:t.readValue(o))&&void 0!==n?n:e[o]),null!=u&&("string"==typeof u&&(rV(u)||rT(u))?u=parseFloat(u):!rW(u)&&eQ.test(l)&&(u=rA(o,l)),t.addValue(o,rB(u,{owner:t})),void 0===r[o]&&(r[o]=u),null!==u&&t.setBaseTarget(o,u))}}(this,r,s);let t=nz(this,r,s,e);e=t.transitionEnd,r=t.target}return{transition:t,transitionEnd:e,...r}}}class nK extends nZ{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,e){if(O.has(e)){let t=rS(e);return t&&t.default||0}{let r=window.getComputedStyle(t),i=(H(e)?r.getPropertyValue(e):r[e])||0;return"string"==typeof i?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:e}){return ik(t,e)}build(t,e,r,i){th(t,e,r,i.transformTemplate)}scrapeMotionValuesFromProps(t,e){return tj(t,e)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;z(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}renderInstance(t,e,r,i){tA(t,e,r,i)}}class nQ extends nZ{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(O.has(e)){let t=rS(e);return t&&t.default||0}return e=tT.has(e)?e:b(e),t.getAttribute(e)}measureInstanceViewportBox(){return iu()}scrapeMotionValuesFromProps(t,e){return tE(t,e)}build(t,e,r,i){tw(t,e,r,this.isSVGTag,i.transformTemplate)}renderInstance(t,e,r,i){tk(t,e,r,i)}mount(t){this.isSVGTag=tS(t.tagName),super.mount(t)}}let nJ=(t,e)=>I(t)?new nQ(e,{enableHardwareAcceleration:!1}):new nK(e,{enableHardwareAcceleration:!0}),n0={animation:{Feature:rY},exit:{Feature:rK},inView:{Feature:en},tap:{Feature:t7},focus:{Feature:t6},hover:{Feature:t3},pan:{Feature:iL},drag:{Feature:iM,ProjectionNode:nj,MeasureLayout:iF},layout:{ProjectionNode:nj,MeasureLayout:iF}},n1=function(t){function e(e,r={}){return function({preloadedFeatures:t,createVisualElement:e,useRender:r,useVisualState:i,Component:n}){t&&function(t){for(let e in t)M[e]={...M[e],...t[e]}}(t);let o=(0,s.forwardRef)(function(o,a){var l;let u;let c={...(0,s.useContext)(m),...o,layoutId:function({layoutId:t}){let e=(0,s.useContext)(D).id;return e&&void 0!==t?e+"-"+t:t}(o)},{isStatic:d}=c,h=function(t){let{initial:e,animate:r}=function(t,e){if(j(t)){let{initial:e,animate:r}=t;return{initial:!1===e||S(e)?e:void 0,animate:S(r)?r:void 0}}return!1!==t.inherit?e:{}}(t,(0,s.useContext)(f));return(0,s.useMemo)(()=>({initial:e,animate:r}),[C(e),C(r)])}(o),p=i(o,d);if(!d&&y){h.visualElement=function(t,e,r,i){let{visualElement:n}=(0,s.useContext)(f),o=(0,s.useContext)(x),a=(0,s.useContext)(g),l=(0,s.useContext)(m).reducedMotion,u=(0,s.useRef)();i=i||o.renderer,!u.current&&i&&(u.current=i(t,{visualState:e,parent:n,props:r,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));let c=u.current;(0,s.useInsertionEffect)(()=>{c&&c.update(r,a)});let d=(0,s.useRef)(!!(r[w]&&!window.HandoffComplete));return v(()=>{c&&(c.render(),d.current&&c.animationState&&c.animationState.animateChanges())}),(0,s.useEffect)(()=>{c&&(c.updateFeatures(),!d.current&&c.animationState&&c.animationState.animateChanges(),d.current&&(d.current=!1,window.HandoffComplete=!0))}),c}(n,p,c,e);let r=(0,s.useContext)(L),i=(0,s.useContext)(x).strict;h.visualElement&&(u=h.visualElement.loadFeatures(c,i,t,r))}return s.createElement(f.Provider,{value:h},u&&h.visualElement?s.createElement(u,{visualElement:h.visualElement,...c}):null,r(n,o,(l=h.visualElement,(0,s.useCallback)(t=>{t&&p.mount&&p.mount(t),l&&(t?l.mount(t):l.unmount()),a&&("function"==typeof a?a(t):P(a)&&(a.current=t))},[l])),p,d,h.visualElement))});return o[R]=n,o}(t(e,r))}if("undefined"==typeof Proxy)return e;let r=new Map;return new Proxy(e,{get:(t,i)=>(r.has(i)||r.set(i,e(i)),r.get(i))})}((t,e)=>(function(t,{forwardMotionProps:e=!1},r,i){return{...I(t)?tW:t$,preloadedFeatures:r,useRender:function(t=!1){return(e,r,i,{latestValues:n},o)=>{let a=(I(e)?function(t,e,r,i){let n=(0,s.useMemo)(()=>{let r=tP();return tw(r,e,{enableHardwareAcceleration:!1},tS(i),t.transformTemplate),{...r.attrs,style:{...r.style}}},[e]);if(t.style){let e={};tm(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e,r){let i={},n=function(t,e,r){let i=t.style||{},n={};return tm(n,i,t),Object.assign(n,function({transformTemplate:t},e,r){return(0,s.useMemo)(()=>{let i=tp();return th(i,e,{enableHardwareAcceleration:!r},t),Object.assign({},i.vars,i.style)},[e])}(t,e,r)),t.transformValues?t.transformValues(n):n}(t,e,r);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(r,n,o,e),l={...function(t,e,r){let i={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(ty(n)||!0===r&&tg(n)||!e&&!tg(n)||t.draggable&&n.startsWith("onDrag"))&&(i[n]=t[n]);return i}(r,"string"==typeof e,t),...a,ref:i},{children:u}=r,c=(0,s.useMemo)(()=>z(u)?u.get():u,[u]);return(0,s.createElement)(e,{...l,children:c})}}(e),createVisualElement:i,Component:t}})(t,e,n0,nJ)),n5=[{name:"Security Scanning",description:"Detect malware, vulnerabilities, and suspicious patterns using advanced AI algorithms.",icon:l},{name:"Compliance Checking",description:"Ensure GDPR, HIPAA, PCI-DSS compliance with automated PII detection.",icon:u},{name:"Anomaly Detection",description:"Identify unusual patterns and behaviors using machine learning models.",icon:c},{name:"Cloud-Native",description:"Built on AWS with serverless architecture for maximum scalability.",icon:d},{name:"AI-Powered",description:"Leverages Amazon Comprehend, Rekognition, and SageMaker for intelligent analysis.",icon:h},{name:"Enterprise Security",description:"End-to-end encryption, IAM controls, and audit logging for enterprise needs.",icon:p}],n2=[{name:"Files Scanned",value:"10,000+"},{name:"Threats Detected",value:"500+"},{name:"Compliance Issues Found",value:"1,200+"},{name:"Average Scan Time",value:"< 2 min"}];function n3(){let[t,e]=(0,s.useState)(!1);return t?(0,i.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[i.jsx("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:(0,i.jsxs)("div",{className:"text-center",children:[i.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-2",children:"AI Security Scanner"}),i.jsx("p",{className:"text-gray-600",children:"Sign in to your account or create a new one"})]})}),(0,i.jsxs)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:[i.jsx("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:i.jsx(a._,{hideSignUp:!1,components:{Header:()=>null}})}),i.jsx("div",{className:"mt-6 text-center",children:i.jsx("button",{onClick:()=>e(!1),className:"text-primary-600 hover:text-primary-500 text-sm font-medium",children:"← Back to home"})})]})]}):(0,i.jsxs)("div",{className:"bg-white",children:[i.jsx("header",{className:"absolute inset-x-0 top-0 z-50",children:(0,i.jsxs)("nav",{className:"flex items-center justify-between p-6 lg:px-8","aria-label":"Global",children:[i.jsx("div",{className:"flex lg:flex-1",children:i.jsx("span",{className:"text-xl font-bold text-gray-900",children:"\uD83D\uDEE1️ AI Security Scanner"})}),i.jsx("div",{className:"flex lg:flex-1 lg:justify-end",children:i.jsx("button",{onClick:()=>e(!0),className:"btn-primary",children:"Sign In"})})]})}),(0,i.jsxs)("div",{className:"relative isolate px-6 pt-14 lg:px-8",children:[i.jsx("div",{className:"absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80",children:i.jsx("div",{className:"relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-primary-400 to-secondary-400 opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"})}),i.jsx("div",{className:"mx-auto max-w-2xl py-32 sm:py-48 lg:py-56",children:(0,i.jsxs)("div",{className:"text-center",children:[i.jsx(n1.h1,{className:"text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:"AI-Powered Security & Compliance Scanner"}),i.jsx(n1.p,{className:"mt-6 text-lg leading-8 text-gray-600",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},children:"Analyze files for security threats, compliance violations, and anomalies using advanced AI and machine learning. Built on AWS cloud infrastructure for enterprise-grade security."}),(0,i.jsxs)(n1.div,{className:"mt-10 flex items-center justify-center gap-x-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},children:[i.jsx("button",{onClick:()=>e(!0),className:"btn-primary btn-lg",children:"Get Started"}),(0,i.jsxs)("a",{href:"#features",className:"text-sm font-semibold leading-6 text-gray-900",children:["Learn more ",i.jsx("span",{"aria-hidden":"true",children:"→"})]})]})]})}),i.jsx("div",{className:"absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]",children:i.jsx("div",{className:"relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-primary-400 to-secondary-400 opacity-30 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]"})})]}),i.jsx("div",{className:"bg-gray-900 py-24 sm:py-32",children:i.jsx("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:i.jsx("dl",{className:"grid grid-cols-1 gap-x-8 gap-y-16 text-center lg:grid-cols-4",children:n2.map((t,e)=>(0,i.jsxs)(n1.div,{className:"mx-auto flex max-w-xs flex-col gap-y-4",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*e},viewport:{once:!0},children:[i.jsx("dt",{className:"text-base leading-7 text-gray-300",children:t.name}),i.jsx("dd",{className:"order-first text-3xl font-semibold tracking-tight text-white sm:text-5xl",children:t.value})]},t.name))})})}),i.jsx("div",{id:"features",className:"py-24 sm:py-32",children:(0,i.jsxs)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:[(0,i.jsxs)("div",{className:"mx-auto max-w-2xl lg:text-center",children:[i.jsx("h2",{className:"text-base font-semibold leading-7 text-primary-600",children:"Comprehensive Analysis"}),i.jsx("p",{className:"mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:"Everything you need to secure your data"}),i.jsx("p",{className:"mt-6 text-lg leading-8 text-gray-600",children:"Our AI-powered platform provides comprehensive security analysis, compliance checking, and anomaly detection to keep your data safe and compliant."})]}),i.jsx("div",{className:"mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-4xl",children:i.jsx("dl",{className:"grid max-w-xl grid-cols-1 gap-x-8 gap-y-10 lg:max-w-none lg:grid-cols-2 lg:gap-y-16",children:n5.map((t,e)=>(0,i.jsxs)(n1.div,{className:"relative pl-16",initial:{opacity:0,x:-20},whileInView:{opacity:1,x:0},transition:{duration:.6,delay:.1*e},viewport:{once:!0},children:[(0,i.jsxs)("dt",{className:"text-base font-semibold leading-7 text-gray-900",children:[i.jsx("div",{className:"absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-primary-600",children:i.jsx(t.icon,{className:"h-6 w-6 text-white","aria-hidden":"true"})}),t.name]}),i.jsx("dd",{className:"mt-2 text-base leading-7 text-gray-600",children:t.description})]},t.name))})})]})}),i.jsx("div",{className:"bg-primary-600",children:i.jsx("div",{className:"px-6 py-24 sm:px-6 sm:py-32 lg:px-8",children:(0,i.jsxs)("div",{className:"mx-auto max-w-2xl text-center",children:[i.jsx("h2",{className:"text-3xl font-bold tracking-tight text-white sm:text-4xl",children:"Ready to secure your data?"}),i.jsx("p",{className:"mx-auto mt-6 max-w-xl text-lg leading-8 text-primary-100",children:"Start scanning your files today with our AI-powered security and compliance platform."}),i.jsx("div",{className:"mt-10 flex items-center justify-center gap-x-6",children:i.jsx("button",{onClick:()=>e(!0),className:"btn bg-white text-primary-600 hover:bg-gray-50 focus:ring-white",children:"Get Started Now"})})]})})}),i.jsx("footer",{className:"bg-gray-900",children:(0,i.jsxs)("div",{className:"mx-auto max-w-7xl px-6 py-12 md:flex md:items-center md:justify-between lg:px-8",children:[i.jsx("div",{className:"flex justify-center space-x-6 md:order-2",children:i.jsx("span",{className:"text-gray-400 text-sm",children:"Built with AWS cloud services"})}),i.jsx("div",{className:"mt-8 md:order-1 md:mt-0",children:i.jsx("p",{className:"text-center text-xs leading-5 text-gray-400",children:"\xa9 2024 AI Security Scanner. All rights reserved."})})]})})]})}let n6=t=>{let e=n8(t),{conflictingClassGroups:r,conflictingClassGroupModifiers:i}=t;return{getClassGroupId:t=>{let r=t.split("-");return""===r[0]&&1!==r.length&&r.shift(),n4(r,e)||n7(t)},getConflictingClassGroupIds:(t,e)=>{let n=r[t]||[];return e&&i[t]?[...n,...i[t]]:n}}},n4=(t,e)=>{if(0===t.length)return e.classGroupId;let r=t[0],i=e.nextPart.get(r),n=i?n4(t.slice(1),i):void 0;if(n)return n;if(0===e.validators.length)return;let s=t.join("-");return e.validators.find(({validator:t})=>t(s))?.classGroupId},n9=/^\[(.+)\]$/,n7=t=>{if(n9.test(t)){let e=n9.exec(t)[1],r=e?.substring(0,e.indexOf(":"));if(r)return"arbitrary.."+r}},n8=t=>{let{theme:e,prefix:r}=t,i={nextPart:new Map,validators:[]};return si(Object.entries(t.classGroups),r).forEach(([t,r])=>{st(r,i,t,e)}),i},st=(t,e,r,i)=>{t.forEach(t=>{if("string"==typeof t){(""===t?e:se(e,t)).classGroupId=r;return}if("function"==typeof t){if(sr(t)){st(t(i),e,r,i);return}e.validators.push({validator:t,classGroupId:r});return}Object.entries(t).forEach(([t,n])=>{st(n,se(e,t),r,i)})})},se=(t,e)=>{let r=t;return e.split("-").forEach(t=>{r.nextPart.has(t)||r.nextPart.set(t,{nextPart:new Map,validators:[]}),r=r.nextPart.get(t)}),r},sr=t=>t.isThemeGetter,si=(t,e)=>e?t.map(([t,r])=>[t,r.map(t=>"string"==typeof t?e+t:"object"==typeof t?Object.fromEntries(Object.entries(t).map(([t,r])=>[e+t,r])):t)]):t,sn=t=>{if(t<1)return{get:()=>void 0,set:()=>{}};let e=0,r=new Map,i=new Map,n=(n,s)=>{r.set(n,s),++e>t&&(e=0,i=r,r=new Map)};return{get(t){let e=r.get(t);return void 0!==e?e:void 0!==(e=i.get(t))?(n(t,e),e):void 0},set(t,e){r.has(t)?r.set(t,e):n(t,e)}}},ss=t=>{let{separator:e,experimentalParseClassName:r}=t,i=1===e.length,n=e[0],s=e.length,o=t=>{let r;let o=[],a=0,l=0;for(let u=0;u<t.length;u++){let c=t[u];if(0===a){if(c===n&&(i||t.slice(u,u+s)===e)){o.push(t.slice(l,u)),l=u+s;continue}if("/"===c){r=u;continue}}"["===c?a++:"]"===c&&a--}let u=0===o.length?t:t.substring(l),c=u.startsWith("!"),d=c?u.substring(1):u;return{modifiers:o,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:r&&r>l?r-l:void 0}};return r?t=>r({className:t,parseClassName:o}):o},so=t=>{if(t.length<=1)return t;let e=[],r=[];return t.forEach(t=>{"["===t[0]?(e.push(...r.sort(),t),r=[]):r.push(t)}),e.push(...r.sort()),e},sa=t=>({cache:sn(t.cacheSize),parseClassName:ss(t),...n6(t)}),sl=/\s+/,su=(t,e)=>{let{parseClassName:r,getClassGroupId:i,getConflictingClassGroupIds:n}=e,s=[],o=t.trim().split(sl),a="";for(let t=o.length-1;t>=0;t-=1){let e=o[t],{modifiers:l,hasImportantModifier:u,baseClassName:c,maybePostfixModifierPosition:d}=r(e),h=!!d,p=i(h?c.substring(0,d):c);if(!p){if(!h||!(p=i(c))){a=e+(a.length>0?" "+a:a);continue}h=!1}let m=so(l).join(":"),f=u?m+"!":m,g=f+p;if(s.includes(g))continue;s.push(g);let y=n(p,h);for(let t=0;t<y.length;++t){let e=y[t];s.push(f+e)}a=e+(a.length>0?" "+a:a)}return a};function sc(){let t,e,r=0,i="";for(;r<arguments.length;)(t=arguments[r++])&&(e=sd(t))&&(i&&(i+=" "),i+=e);return i}let sd=t=>{let e;if("string"==typeof t)return t;let r="";for(let i=0;i<t.length;i++)t[i]&&(e=sd(t[i]))&&(r&&(r+=" "),r+=e);return r},sh=t=>{let e=e=>e[t]||[];return e.isThemeGetter=!0,e},sp=/^\[(?:([a-z-]+):)?(.+)\]$/i,sm=/^\d+\/\d+$/,sf=new Set(["px","full","screen"]),sg=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,sy=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,sv=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,sx=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,sb=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,sw=t=>sS(t)||sf.has(t)||sm.test(t),sP=t=>sI(t,"length",sB),sS=t=>!!t&&!Number.isNaN(Number(t)),sA=t=>sI(t,"number",sS),sT=t=>!!t&&Number.isInteger(Number(t)),sk=t=>t.endsWith("%")&&sS(t.slice(0,-1)),sj=t=>sp.test(t),sE=t=>sg.test(t),sC=new Set(["length","size","percentage"]),sV=t=>sI(t,sC,sF),sM=t=>sI(t,"position",sF),sD=new Set(["image","url"]),sL=t=>sI(t,sD,sU),sR=t=>sI(t,"",sO),sN=()=>!0,sI=(t,e,r)=>{let i=sp.exec(t);return!!i&&(i[1]?"string"==typeof e?i[1]===e:e.has(i[1]):r(i[2]))},sB=t=>sy.test(t)&&!sv.test(t),sF=()=>!1,sO=t=>sx.test(t),sU=t=>sb.test(t);Symbol.toStringTag;let sz=function(t,...e){let r,i,n;let s=function(a){return i=(r=sa(e.reduce((t,e)=>e(t),t()))).cache.get,n=r.cache.set,s=o,o(a)};function o(t){let e=i(t);if(e)return e;let s=su(t,r);return n(t,s),s}return function(){return s(sc.apply(null,arguments))}}(()=>{let t=sh("colors"),e=sh("spacing"),r=sh("blur"),i=sh("brightness"),n=sh("borderColor"),s=sh("borderRadius"),o=sh("borderSpacing"),a=sh("borderWidth"),l=sh("contrast"),u=sh("grayscale"),c=sh("hueRotate"),d=sh("invert"),h=sh("gap"),p=sh("gradientColorStops"),m=sh("gradientColorStopPositions"),f=sh("inset"),g=sh("margin"),y=sh("opacity"),v=sh("padding"),x=sh("saturate"),b=sh("scale"),w=sh("sepia"),P=sh("skew"),S=sh("space"),A=sh("translate"),T=()=>["auto","contain","none"],k=()=>["auto","hidden","clip","visible","scroll"],j=()=>["auto",sj,e],E=()=>[sj,e],C=()=>["",sw,sP],V=()=>["auto",sS,sj],M=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],D=()=>["solid","dashed","dotted","double","none"],L=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],R=()=>["start","end","center","between","around","evenly","stretch"],N=()=>["","0",sj],I=()=>["auto","avoid","all","avoid-page","page","left","right","column"],B=()=>[sS,sj];return{cacheSize:500,separator:":",theme:{colors:[sN],spacing:[sw,sP],blur:["none","",sE,sj],brightness:B(),borderColor:[t],borderRadius:["none","","full",sE,sj],borderSpacing:E(),borderWidth:C(),contrast:B(),grayscale:N(),hueRotate:B(),invert:N(),gap:E(),gradientColorStops:[t],gradientColorStopPositions:[sk,sP],inset:j(),margin:j(),opacity:B(),padding:E(),saturate:B(),scale:B(),sepia:N(),skew:B(),space:E(),translate:E()},classGroups:{aspect:[{aspect:["auto","square","video",sj]}],container:["container"],columns:[{columns:[sE]}],"break-after":[{"break-after":I()}],"break-before":[{"break-before":I()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...M(),sj]}],overflow:[{overflow:k()}],"overflow-x":[{"overflow-x":k()}],"overflow-y":[{"overflow-y":k()}],overscroll:[{overscroll:T()}],"overscroll-x":[{"overscroll-x":T()}],"overscroll-y":[{"overscroll-y":T()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[f]}],"inset-x":[{"inset-x":[f]}],"inset-y":[{"inset-y":[f]}],start:[{start:[f]}],end:[{end:[f]}],top:[{top:[f]}],right:[{right:[f]}],bottom:[{bottom:[f]}],left:[{left:[f]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",sT,sj]}],basis:[{basis:j()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",sj]}],grow:[{grow:N()}],shrink:[{shrink:N()}],order:[{order:["first","last","none",sT,sj]}],"grid-cols":[{"grid-cols":[sN]}],"col-start-end":[{col:["auto",{span:["full",sT,sj]},sj]}],"col-start":[{"col-start":V()}],"col-end":[{"col-end":V()}],"grid-rows":[{"grid-rows":[sN]}],"row-start-end":[{row:["auto",{span:[sT,sj]},sj]}],"row-start":[{"row-start":V()}],"row-end":[{"row-end":V()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",sj]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",sj]}],gap:[{gap:[h]}],"gap-x":[{"gap-x":[h]}],"gap-y":[{"gap-y":[h]}],"justify-content":[{justify:["normal",...R()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...R(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...R(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[v]}],px:[{px:[v]}],py:[{py:[v]}],ps:[{ps:[v]}],pe:[{pe:[v]}],pt:[{pt:[v]}],pr:[{pr:[v]}],pb:[{pb:[v]}],pl:[{pl:[v]}],m:[{m:[g]}],mx:[{mx:[g]}],my:[{my:[g]}],ms:[{ms:[g]}],me:[{me:[g]}],mt:[{mt:[g]}],mr:[{mr:[g]}],mb:[{mb:[g]}],ml:[{ml:[g]}],"space-x":[{"space-x":[S]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[S]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",sj,e]}],"min-w":[{"min-w":[sj,e,"min","max","fit"]}],"max-w":[{"max-w":[sj,e,"none","full","min","max","fit","prose",{screen:[sE]},sE]}],h:[{h:[sj,e,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[sj,e,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[sj,e,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[sj,e,"auto","min","max","fit"]}],"font-size":[{text:["base",sE,sP]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",sA]}],"font-family":[{font:[sN]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",sj]}],"line-clamp":[{"line-clamp":["none",sS,sA]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",sw,sj]}],"list-image":[{"list-image":["none",sj]}],"list-style-type":[{list:["none","disc","decimal",sj]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[t]}],"placeholder-opacity":[{"placeholder-opacity":[y]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[t]}],"text-opacity":[{"text-opacity":[y]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...D(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",sw,sP]}],"underline-offset":[{"underline-offset":["auto",sw,sj]}],"text-decoration-color":[{decoration:[t]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:E()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",sj]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",sj]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[y]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...M(),sM]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",sV]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},sL]}],"bg-color":[{bg:[t]}],"gradient-from-pos":[{from:[m]}],"gradient-via-pos":[{via:[m]}],"gradient-to-pos":[{to:[m]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[s]}],"rounded-s":[{"rounded-s":[s]}],"rounded-e":[{"rounded-e":[s]}],"rounded-t":[{"rounded-t":[s]}],"rounded-r":[{"rounded-r":[s]}],"rounded-b":[{"rounded-b":[s]}],"rounded-l":[{"rounded-l":[s]}],"rounded-ss":[{"rounded-ss":[s]}],"rounded-se":[{"rounded-se":[s]}],"rounded-ee":[{"rounded-ee":[s]}],"rounded-es":[{"rounded-es":[s]}],"rounded-tl":[{"rounded-tl":[s]}],"rounded-tr":[{"rounded-tr":[s]}],"rounded-br":[{"rounded-br":[s]}],"rounded-bl":[{"rounded-bl":[s]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[y]}],"border-style":[{border:[...D(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[y]}],"divide-style":[{divide:D()}],"border-color":[{border:[n]}],"border-color-x":[{"border-x":[n]}],"border-color-y":[{"border-y":[n]}],"border-color-s":[{"border-s":[n]}],"border-color-e":[{"border-e":[n]}],"border-color-t":[{"border-t":[n]}],"border-color-r":[{"border-r":[n]}],"border-color-b":[{"border-b":[n]}],"border-color-l":[{"border-l":[n]}],"divide-color":[{divide:[n]}],"outline-style":[{outline:["",...D()]}],"outline-offset":[{"outline-offset":[sw,sj]}],"outline-w":[{outline:[sw,sP]}],"outline-color":[{outline:[t]}],"ring-w":[{ring:C()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[t]}],"ring-opacity":[{"ring-opacity":[y]}],"ring-offset-w":[{"ring-offset":[sw,sP]}],"ring-offset-color":[{"ring-offset":[t]}],shadow:[{shadow:["","inner","none",sE,sR]}],"shadow-color":[{shadow:[sN]}],opacity:[{opacity:[y]}],"mix-blend":[{"mix-blend":[...L(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":L()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[i]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",sE,sj]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[d]}],saturate:[{saturate:[x]}],sepia:[{sepia:[w]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[i]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[y]}],"backdrop-saturate":[{"backdrop-saturate":[x]}],"backdrop-sepia":[{"backdrop-sepia":[w]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[o]}],"border-spacing-x":[{"border-spacing-x":[o]}],"border-spacing-y":[{"border-spacing-y":[o]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",sj]}],duration:[{duration:B()}],ease:[{ease:["linear","in","out","in-out",sj]}],delay:[{delay:B()}],animate:[{animate:["none","spin","ping","pulse","bounce",sj]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[b]}],"scale-x":[{"scale-x":[b]}],"scale-y":[{"scale-y":[b]}],rotate:[{rotate:[sT,sj]}],"translate-x":[{"translate-x":[A]}],"translate-y":[{"translate-y":[A]}],"skew-x":[{"skew-x":[P]}],"skew-y":[{"skew-y":[P]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",sj]}],accent:[{accent:["auto",t]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",sj]}],"caret-color":[{caret:[t]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":E()}],"scroll-mx":[{"scroll-mx":E()}],"scroll-my":[{"scroll-my":E()}],"scroll-ms":[{"scroll-ms":E()}],"scroll-me":[{"scroll-me":E()}],"scroll-mt":[{"scroll-mt":E()}],"scroll-mr":[{"scroll-mr":E()}],"scroll-mb":[{"scroll-mb":E()}],"scroll-ml":[{"scroll-ml":E()}],"scroll-p":[{"scroll-p":E()}],"scroll-px":[{"scroll-px":E()}],"scroll-py":[{"scroll-py":E()}],"scroll-ps":[{"scroll-ps":E()}],"scroll-pe":[{"scroll-pe":E()}],"scroll-pt":[{"scroll-pt":E()}],"scroll-pr":[{"scroll-pr":E()}],"scroll-pb":[{"scroll-pb":E()}],"scroll-pl":[{"scroll-pl":E()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",sj]}],fill:[{fill:[t,"none"]}],"stroke-w":[{stroke:[sw,sP,sA]}],stroke:[{stroke:[t,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}}),sW={sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8",xl:"h-12 w-12"},s$={primary:"border-primary-600",secondary:"border-secondary-600",white:"border-white"};function s_({size:t="md",className:e,color:r="primary"}){return i.jsx("div",{className:function(...t){return sz(function(){for(var t,e,r=0,i="",n=arguments.length;r<n;r++)(t=arguments[r])&&(e=function t(e){var r,i,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e){if(Array.isArray(e)){var s=e.length;for(r=0;r<s;r++)e[r]&&(i=t(e[r]))&&(n&&(n+=" "),n+=i)}else for(i in e)e[i]&&(n&&(n+=" "),n+=i)}return n}(t))&&(i&&(i+=" "),i+=e);return i}(t))}("spinner",sW[t],s$[r],e),role:"status","aria-label":"Loading",children:i.jsx("span",{className:"sr-only",children:"Loading..."})})}function sH(){let{authStatus:t,user:e}=(0,n.Z)(t=>[t.authStatus,t.user]);return((0,o.useRouter)(),"configuring"===t)?i.jsx("div",{className:"min-h-screen flex items-center justify-center",children:i.jsx(s_,{size:"lg"})}):"authenticated"===t?(0,i.jsxs)("div",{className:"min-h-screen flex items-center justify-center",children:[i.jsx(s_,{size:"lg"}),i.jsx("p",{className:"ml-3 text-gray-600",children:"Redirecting to dashboard..."})]}):i.jsx(n3,{})}},2395:(t,e,r)=>{"use strict";r.d(e,{Providers:()=>h});var i=r(326),n=r(3999),s=r(2735),o=r(381),a=r(7577);r(7082);var l=r(1070);let u={api:{baseUrl:process.env.NEXT_PUBLIC_API_URL||"http://localhost:3001",timeout:3e4},aws:{region:process.env.NEXT_PUBLIC_REGION||"us-east-1",userPoolId:process.env.NEXT_PUBLIC_USER_POOL_ID||"",userPoolClientId:process.env.NEXT_PUBLIC_USER_POOL_CLIENT_ID||"",identityPoolId:process.env.NEXT_PUBLIC_IDENTITY_POOL_ID||""},app:{name:"AI Security Scanner",version:"1.0.0",environment:process.env.NEXT_PUBLIC_ENVIRONMENT||"development",isDevelopment:"development"===process.env.NEXT_PUBLIC_ENVIRONMENT,isProduction:"production"===process.env.NEXT_PUBLIC_ENVIRONMENT},upload:{maxFileSize:52428800,allowedTypes:["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/msword","text/plain","text/csv","application/json","application/xml","text/xml","application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","image/jpeg","image/png","image/gif","image/bmp","image/tiff"],allowedExtensions:[".pdf",".docx",".doc",".txt",".csv",".json",".xml",".xls",".xlsx",".jpg",".jpeg",".png",".gif",".bmp",".tiff"]},scan:{types:[{value:"security",label:"Security Only",description:"Scan for security threats and vulnerabilities"},{value:"compliance",label:"Compliance Only",description:"Check for PII and compliance violations"},{value:"anomaly",label:"Anomaly Only",description:"Detect unusual patterns and behaviors"},{value:"full",label:"Full Scan",description:"Complete security, compliance, and anomaly analysis"}],statusColors:{pending_upload:"gray",uploaded:"blue",processing:"yellow",completed:"green",failed:"red",cancelled:"gray"},riskColors:{MINIMAL:"green",LOW:"green",MEDIUM:"yellow",HIGH:"orange",CRITICAL:"red"}},ui:{pageSize:20,debounceDelay:300,toastDuration:5e3,animationDuration:300},features:{enableAnalytics:!1,enableNotifications:!0,enableDarkMode:!1,enableExport:!0,enableSharing:!1}},c={Auth:{Cognito:{userPoolId:u.aws.userPoolId,userPoolClientId:u.aws.userPoolClientId,identityPoolId:u.aws.identityPoolId,loginWith:{email:!0},signUpVerificationMethod:"code",userAttributes:{email:{required:!0},given_name:{required:!0},family_name:{required:!0}},allowGuestAccess:!1,passwordFormat:{minLength:8,requireLowercase:!0,requireUppercase:!0,requireNumbers:!0,requireSpecialCharacters:!0}}},API:{REST:{"ai-scanner-api":{endpoint:u.api.baseUrl,region:u.aws.region}}},Storage:{S3:{region:u.aws.region,bucket:"ai-scanner-uploads"}}};l.V.configure(c);let d=()=>new n.QueryClient({defaultOptions:{queries:{retry:3,retryDelay:t=>Math.min(1e3*2**t,3e4),staleTime:3e5,cacheTime:6e5,refetchOnWindowFocus:!1},mutations:{retry:1}}});function h({children:t}){let[e]=(0,a.useState)(()=>d());return i.jsx(n.QueryClientProvider,{client:e,children:(0,i.jsxs)(s._.Provider,{children:[t,i.jsx(o.x7,{position:"top-right",toastOptions:{duration:5e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#10B981",secondary:"#fff"}},error:{duration:5e3,iconTheme:{primary:"#EF4444",secondary:"#fff"}}}})]})})}},5495:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>l,metadata:()=>a});var i=r(9510),n=r(5384),s=r.n(n);r(5023);let o=(0,r(8570).createProxy)(String.raw`C:\Users\<USER>\Desktop\AWS Task\frontend\src\app\providers.tsx#Providers`),a={title:"AI Security Scanner",description:"AI-powered security and compliance scanning platform",keywords:["security","compliance","AI","scanning","AWS"],authors:[{name:"AWS AI Security Scanner Team"}],viewport:"width=device-width, initial-scale=1",robots:"index, follow",openGraph:{title:"AI Security Scanner",description:"AI-powered security and compliance scanning platform",type:"website",locale:"en_US"},twitter:{card:"summary_large_image",title:"AI Security Scanner",description:"AI-powered security and compliance scanning platform"},icons:{icon:"/favicon.ico",apple:"/apple-touch-icon.png"}};function l({children:t}){return i.jsx("html",{lang:"en",className:"h-full",children:i.jsx("body",{className:`${s().className} h-full`,children:i.jsx(o,{children:t})})})}},5480:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>i});let i=(0,r(8570).createProxy)(String.raw`C:\Users\<USER>\Desktop\AWS Task\frontend\src\app\page.tsx#default`)},5023:()=>{}};var e=require("../webpack-runtime.js");e.C(t);var r=t=>e(e.s=t),i=e.X(0,[200],()=>r(6782));module.exports=i})();