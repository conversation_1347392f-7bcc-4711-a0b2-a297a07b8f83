#!/usr/bin/env node
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
require("source-map-support/register");
const cdk = __importStar(require("aws-cdk-lib"));
const ai_security_scanner_stack_1 = require("../lib/ai-security-scanner-stack");
const frontend_stack_1 = require("../lib/frontend-stack");
const monitoring_stack_1 = require("../lib/monitoring-stack");
const app = new cdk.App();
// Get environment from context
const environment = app.node.tryGetContext('environment') || 'dev';
const account = process.env.CDK_DEFAULT_ACCOUNT;
const region = process.env.CDK_DEFAULT_REGION || 'us-east-1';
// Environment configuration
const envConfig = {
    dev: {
        domainName: undefined, // Use CloudFront default domain
        certificateArn: undefined,
        enableWaf: false,
        retentionDays: 7,
        logLevel: 'DEBUG'
    },
    prod: {
        domainName: 'ai-security-scanner.com',
        certificateArn: process.env.CERTIFICATE_ARN,
        enableWaf: true,
        retentionDays: 30,
        logLevel: 'INFO'
    }
};
const config = envConfig[environment];
// Stack naming convention
const stackPrefix = `AiSecurityScanner-${environment}`;
// Main backend stack
const backendStack = new ai_security_scanner_stack_1.AiSecurityScannerStack(app, `${stackPrefix}-Backend`, {
    env: { account, region },
    environment,
    config,
    description: `AI Security Scanner Backend Stack - ${environment}`,
    tags: {
        Environment: environment,
        Project: 'AiSecurityScanner',
        Owner: 'DevOps',
        CostCenter: 'Engineering'
    }
});
// Frontend stack (depends on backend)
const frontendStack = new frontend_stack_1.FrontendStack(app, `${stackPrefix}-Frontend`, {
    env: { account, region },
    environment,
    config,
    apiGatewayUrl: backendStack.apiGatewayUrl,
    userPoolId: backendStack.userPoolId,
    userPoolClientId: backendStack.userPoolClientId,
    identityPoolId: backendStack.identityPoolId,
    description: `AI Security Scanner Frontend Stack - ${environment}`,
    tags: {
        Environment: environment,
        Project: 'AiSecurityScanner',
        Owner: 'DevOps',
        CostCenter: 'Engineering'
    }
});
// Monitoring stack (depends on backend)
const monitoringStack = new monitoring_stack_1.MonitoringStack(app, `${stackPrefix}-Monitoring`, {
    env: { account, region },
    environment,
    config,
    apiGateway: backendStack.apiGateway,
    lambdaFunctions: backendStack.lambdaFunctions,
    stepFunction: backendStack.stepFunction,
    description: `AI Security Scanner Monitoring Stack - ${environment}`,
    tags: {
        Environment: environment,
        Project: 'AiSecurityScanner',
        Owner: 'DevOps',
        CostCenter: 'Engineering'
    }
});
// Add dependencies
frontendStack.addDependency(backendStack);
monitoringStack.addDependency(backendStack);
// Output important information
new cdk.CfnOutput(backendStack, 'DeploymentInfo', {
    value: JSON.stringify({
        environment,
        region,
        timestamp: new Date().toISOString(),
        stacks: [
            `${stackPrefix}-Backend`,
            `${stackPrefix}-Frontend`,
            `${stackPrefix}-Monitoring`
        ]
    }),
    description: 'Deployment information'
});
app.synth();
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiYXBwLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vYmluL2FwcC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDQSx1Q0FBcUM7QUFDckMsaURBQW1DO0FBQ25DLGdGQUEwRTtBQUMxRSwwREFBc0Q7QUFDdEQsOERBQTBEO0FBRTFELE1BQU0sR0FBRyxHQUFHLElBQUksR0FBRyxDQUFDLEdBQUcsRUFBRSxDQUFDO0FBRTFCLCtCQUErQjtBQUMvQixNQUFNLFdBQVcsR0FBRyxHQUFHLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxhQUFhLENBQUMsSUFBSSxLQUFLLENBQUM7QUFDbkUsTUFBTSxPQUFPLEdBQUcsT0FBTyxDQUFDLEdBQUcsQ0FBQyxtQkFBbUIsQ0FBQztBQUNoRCxNQUFNLE1BQU0sR0FBRyxPQUFPLENBQUMsR0FBRyxDQUFDLGtCQUFrQixJQUFJLFdBQVcsQ0FBQztBQUU3RCw0QkFBNEI7QUFDNUIsTUFBTSxTQUFTLEdBQUc7SUFDaEIsR0FBRyxFQUFFO1FBQ0gsVUFBVSxFQUFFLFNBQVMsRUFBRSxnQ0FBZ0M7UUFDdkQsY0FBYyxFQUFFLFNBQVM7UUFDekIsU0FBUyxFQUFFLEtBQUs7UUFDaEIsYUFBYSxFQUFFLENBQUM7UUFDaEIsUUFBUSxFQUFFLE9BQU87S0FDbEI7SUFDRCxJQUFJLEVBQUU7UUFDSixVQUFVLEVBQUUseUJBQXlCO1FBQ3JDLGNBQWMsRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLGVBQWU7UUFDM0MsU0FBUyxFQUFFLElBQUk7UUFDZixhQUFhLEVBQUUsRUFBRTtRQUNqQixRQUFRLEVBQUUsTUFBTTtLQUNqQjtDQUNGLENBQUM7QUFFRixNQUFNLE1BQU0sR0FBRyxTQUFTLENBQUMsV0FBcUMsQ0FBQyxDQUFDO0FBRWhFLDBCQUEwQjtBQUMxQixNQUFNLFdBQVcsR0FBRyxxQkFBcUIsV0FBVyxFQUFFLENBQUM7QUFFdkQscUJBQXFCO0FBQ3JCLE1BQU0sWUFBWSxHQUFHLElBQUksa0RBQXNCLENBQUMsR0FBRyxFQUFFLEdBQUcsV0FBVyxVQUFVLEVBQUU7SUFDN0UsR0FBRyxFQUFFLEVBQUUsT0FBTyxFQUFFLE1BQU0sRUFBRTtJQUN4QixXQUFXO0lBQ1gsTUFBTTtJQUNOLFdBQVcsRUFBRSx1Q0FBdUMsV0FBVyxFQUFFO0lBQ2pFLElBQUksRUFBRTtRQUNKLFdBQVcsRUFBRSxXQUFXO1FBQ3hCLE9BQU8sRUFBRSxtQkFBbUI7UUFDNUIsS0FBSyxFQUFFLFFBQVE7UUFDZixVQUFVLEVBQUUsYUFBYTtLQUMxQjtDQUNGLENBQUMsQ0FBQztBQUVILHNDQUFzQztBQUN0QyxNQUFNLGFBQWEsR0FBRyxJQUFJLDhCQUFhLENBQUMsR0FBRyxFQUFFLEdBQUcsV0FBVyxXQUFXLEVBQUU7SUFDdEUsR0FBRyxFQUFFLEVBQUUsT0FBTyxFQUFFLE1BQU0sRUFBRTtJQUN4QixXQUFXO0lBQ1gsTUFBTTtJQUNOLGFBQWEsRUFBRSxZQUFZLENBQUMsYUFBYTtJQUN6QyxVQUFVLEVBQUUsWUFBWSxDQUFDLFVBQVU7SUFDbkMsZ0JBQWdCLEVBQUUsWUFBWSxDQUFDLGdCQUFnQjtJQUMvQyxjQUFjLEVBQUUsWUFBWSxDQUFDLGNBQWM7SUFDM0MsV0FBVyxFQUFFLHdDQUF3QyxXQUFXLEVBQUU7SUFDbEUsSUFBSSxFQUFFO1FBQ0osV0FBVyxFQUFFLFdBQVc7UUFDeEIsT0FBTyxFQUFFLG1CQUFtQjtRQUM1QixLQUFLLEVBQUUsUUFBUTtRQUNmLFVBQVUsRUFBRSxhQUFhO0tBQzFCO0NBQ0YsQ0FBQyxDQUFDO0FBRUgsd0NBQXdDO0FBQ3hDLE1BQU0sZUFBZSxHQUFHLElBQUksa0NBQWUsQ0FBQyxHQUFHLEVBQUUsR0FBRyxXQUFXLGFBQWEsRUFBRTtJQUM1RSxHQUFHLEVBQUUsRUFBRSxPQUFPLEVBQUUsTUFBTSxFQUFFO0lBQ3hCLFdBQVc7SUFDWCxNQUFNO0lBQ04sVUFBVSxFQUFFLFlBQVksQ0FBQyxVQUFVO0lBQ25DLGVBQWUsRUFBRSxZQUFZLENBQUMsZUFBZTtJQUM3QyxZQUFZLEVBQUUsWUFBWSxDQUFDLFlBQVk7SUFDdkMsV0FBVyxFQUFFLDBDQUEwQyxXQUFXLEVBQUU7SUFDcEUsSUFBSSxFQUFFO1FBQ0osV0FBVyxFQUFFLFdBQVc7UUFDeEIsT0FBTyxFQUFFLG1CQUFtQjtRQUM1QixLQUFLLEVBQUUsUUFBUTtRQUNmLFVBQVUsRUFBRSxhQUFhO0tBQzFCO0NBQ0YsQ0FBQyxDQUFDO0FBRUgsbUJBQW1CO0FBQ25CLGFBQWEsQ0FBQyxhQUFhLENBQUMsWUFBWSxDQUFDLENBQUM7QUFDMUMsZUFBZSxDQUFDLGFBQWEsQ0FBQyxZQUFZLENBQUMsQ0FBQztBQUU1QywrQkFBK0I7QUFDL0IsSUFBSSxHQUFHLENBQUMsU0FBUyxDQUFDLFlBQVksRUFBRSxnQkFBZ0IsRUFBRTtJQUNoRCxLQUFLLEVBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQztRQUNwQixXQUFXO1FBQ1gsTUFBTTtRQUNOLFNBQVMsRUFBRSxJQUFJLElBQUksRUFBRSxDQUFDLFdBQVcsRUFBRTtRQUNuQyxNQUFNLEVBQUU7WUFDTixHQUFHLFdBQVcsVUFBVTtZQUN4QixHQUFHLFdBQVcsV0FBVztZQUN6QixHQUFHLFdBQVcsYUFBYTtTQUM1QjtLQUNGLENBQUM7SUFDRixXQUFXLEVBQUUsd0JBQXdCO0NBQ3RDLENBQUMsQ0FBQztBQUVILEdBQUcsQ0FBQyxLQUFLLEVBQUUsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIiMhL3Vzci9iaW4vZW52IG5vZGVcbmltcG9ydCAnc291cmNlLW1hcC1zdXBwb3J0L3JlZ2lzdGVyJztcbmltcG9ydCAqIGFzIGNkayBmcm9tICdhd3MtY2RrLWxpYic7XG5pbXBvcnQgeyBBaVNlY3VyaXR5U2Nhbm5lclN0YWNrIH0gZnJvbSAnLi4vbGliL2FpLXNlY3VyaXR5LXNjYW5uZXItc3RhY2snO1xuaW1wb3J0IHsgRnJvbnRlbmRTdGFjayB9IGZyb20gJy4uL2xpYi9mcm9udGVuZC1zdGFjayc7XG5pbXBvcnQgeyBNb25pdG9yaW5nU3RhY2sgfSBmcm9tICcuLi9saWIvbW9uaXRvcmluZy1zdGFjayc7XG5cbmNvbnN0IGFwcCA9IG5ldyBjZGsuQXBwKCk7XG5cbi8vIEdldCBlbnZpcm9ubWVudCBmcm9tIGNvbnRleHRcbmNvbnN0IGVudmlyb25tZW50ID0gYXBwLm5vZGUudHJ5R2V0Q29udGV4dCgnZW52aXJvbm1lbnQnKSB8fCAnZGV2JztcbmNvbnN0IGFjY291bnQgPSBwcm9jZXNzLmVudi5DREtfREVGQVVMVF9BQ0NPVU5UO1xuY29uc3QgcmVnaW9uID0gcHJvY2Vzcy5lbnYuQ0RLX0RFRkFVTFRfUkVHSU9OIHx8ICd1cy1lYXN0LTEnO1xuXG4vLyBFbnZpcm9ubWVudCBjb25maWd1cmF0aW9uXG5jb25zdCBlbnZDb25maWcgPSB7XG4gIGRldjoge1xuICAgIGRvbWFpbk5hbWU6IHVuZGVmaW5lZCwgLy8gVXNlIENsb3VkRnJvbnQgZGVmYXVsdCBkb21haW5cbiAgICBjZXJ0aWZpY2F0ZUFybjogdW5kZWZpbmVkLFxuICAgIGVuYWJsZVdhZjogZmFsc2UsXG4gICAgcmV0ZW50aW9uRGF5czogNyxcbiAgICBsb2dMZXZlbDogJ0RFQlVHJ1xuICB9LFxuICBwcm9kOiB7XG4gICAgZG9tYWluTmFtZTogJ2FpLXNlY3VyaXR5LXNjYW5uZXIuY29tJyxcbiAgICBjZXJ0aWZpY2F0ZUFybjogcHJvY2Vzcy5lbnYuQ0VSVElGSUNBVEVfQVJOLFxuICAgIGVuYWJsZVdhZjogdHJ1ZSxcbiAgICByZXRlbnRpb25EYXlzOiAzMCxcbiAgICBsb2dMZXZlbDogJ0lORk8nXG4gIH1cbn07XG5cbmNvbnN0IGNvbmZpZyA9IGVudkNvbmZpZ1tlbnZpcm9ubWVudCBhcyBrZXlvZiB0eXBlb2YgZW52Q29uZmlnXTtcblxuLy8gU3RhY2sgbmFtaW5nIGNvbnZlbnRpb25cbmNvbnN0IHN0YWNrUHJlZml4ID0gYEFpU2VjdXJpdHlTY2FubmVyLSR7ZW52aXJvbm1lbnR9YDtcblxuLy8gTWFpbiBiYWNrZW5kIHN0YWNrXG5jb25zdCBiYWNrZW5kU3RhY2sgPSBuZXcgQWlTZWN1cml0eVNjYW5uZXJTdGFjayhhcHAsIGAke3N0YWNrUHJlZml4fS1CYWNrZW5kYCwge1xuICBlbnY6IHsgYWNjb3VudCwgcmVnaW9uIH0sXG4gIGVudmlyb25tZW50LFxuICBjb25maWcsXG4gIGRlc2NyaXB0aW9uOiBgQUkgU2VjdXJpdHkgU2Nhbm5lciBCYWNrZW5kIFN0YWNrIC0gJHtlbnZpcm9ubWVudH1gLFxuICB0YWdzOiB7XG4gICAgRW52aXJvbm1lbnQ6IGVudmlyb25tZW50LFxuICAgIFByb2plY3Q6ICdBaVNlY3VyaXR5U2Nhbm5lcicsXG4gICAgT3duZXI6ICdEZXZPcHMnLFxuICAgIENvc3RDZW50ZXI6ICdFbmdpbmVlcmluZydcbiAgfVxufSk7XG5cbi8vIEZyb250ZW5kIHN0YWNrIChkZXBlbmRzIG9uIGJhY2tlbmQpXG5jb25zdCBmcm9udGVuZFN0YWNrID0gbmV3IEZyb250ZW5kU3RhY2soYXBwLCBgJHtzdGFja1ByZWZpeH0tRnJvbnRlbmRgLCB7XG4gIGVudjogeyBhY2NvdW50LCByZWdpb24gfSxcbiAgZW52aXJvbm1lbnQsXG4gIGNvbmZpZyxcbiAgYXBpR2F0ZXdheVVybDogYmFja2VuZFN0YWNrLmFwaUdhdGV3YXlVcmwsXG4gIHVzZXJQb29sSWQ6IGJhY2tlbmRTdGFjay51c2VyUG9vbElkLFxuICB1c2VyUG9vbENsaWVudElkOiBiYWNrZW5kU3RhY2sudXNlclBvb2xDbGllbnRJZCxcbiAgaWRlbnRpdHlQb29sSWQ6IGJhY2tlbmRTdGFjay5pZGVudGl0eVBvb2xJZCxcbiAgZGVzY3JpcHRpb246IGBBSSBTZWN1cml0eSBTY2FubmVyIEZyb250ZW5kIFN0YWNrIC0gJHtlbnZpcm9ubWVudH1gLFxuICB0YWdzOiB7XG4gICAgRW52aXJvbm1lbnQ6IGVudmlyb25tZW50LFxuICAgIFByb2plY3Q6ICdBaVNlY3VyaXR5U2Nhbm5lcicsXG4gICAgT3duZXI6ICdEZXZPcHMnLFxuICAgIENvc3RDZW50ZXI6ICdFbmdpbmVlcmluZydcbiAgfVxufSk7XG5cbi8vIE1vbml0b3Jpbmcgc3RhY2sgKGRlcGVuZHMgb24gYmFja2VuZClcbmNvbnN0IG1vbml0b3JpbmdTdGFjayA9IG5ldyBNb25pdG9yaW5nU3RhY2soYXBwLCBgJHtzdGFja1ByZWZpeH0tTW9uaXRvcmluZ2AsIHtcbiAgZW52OiB7IGFjY291bnQsIHJlZ2lvbiB9LFxuICBlbnZpcm9ubWVudCxcbiAgY29uZmlnLFxuICBhcGlHYXRld2F5OiBiYWNrZW5kU3RhY2suYXBpR2F0ZXdheSxcbiAgbGFtYmRhRnVuY3Rpb25zOiBiYWNrZW5kU3RhY2subGFtYmRhRnVuY3Rpb25zLFxuICBzdGVwRnVuY3Rpb246IGJhY2tlbmRTdGFjay5zdGVwRnVuY3Rpb24sXG4gIGRlc2NyaXB0aW9uOiBgQUkgU2VjdXJpdHkgU2Nhbm5lciBNb25pdG9yaW5nIFN0YWNrIC0gJHtlbnZpcm9ubWVudH1gLFxuICB0YWdzOiB7XG4gICAgRW52aXJvbm1lbnQ6IGVudmlyb25tZW50LFxuICAgIFByb2plY3Q6ICdBaVNlY3VyaXR5U2Nhbm5lcicsXG4gICAgT3duZXI6ICdEZXZPcHMnLFxuICAgIENvc3RDZW50ZXI6ICdFbmdpbmVlcmluZydcbiAgfVxufSk7XG5cbi8vIEFkZCBkZXBlbmRlbmNpZXNcbmZyb250ZW5kU3RhY2suYWRkRGVwZW5kZW5jeShiYWNrZW5kU3RhY2spO1xubW9uaXRvcmluZ1N0YWNrLmFkZERlcGVuZGVuY3koYmFja2VuZFN0YWNrKTtcblxuLy8gT3V0cHV0IGltcG9ydGFudCBpbmZvcm1hdGlvblxubmV3IGNkay5DZm5PdXRwdXQoYmFja2VuZFN0YWNrLCAnRGVwbG95bWVudEluZm8nLCB7XG4gIHZhbHVlOiBKU09OLnN0cmluZ2lmeSh7XG4gICAgZW52aXJvbm1lbnQsXG4gICAgcmVnaW9uLFxuICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgIHN0YWNrczogW1xuICAgICAgYCR7c3RhY2tQcmVmaXh9LUJhY2tlbmRgLFxuICAgICAgYCR7c3RhY2tQcmVmaXh9LUZyb250ZW5kYCxcbiAgICAgIGAke3N0YWNrUHJlZml4fS1Nb25pdG9yaW5nYFxuICAgIF1cbiAgfSksXG4gIGRlc2NyaXB0aW9uOiAnRGVwbG95bWVudCBpbmZvcm1hdGlvbidcbn0pO1xuXG5hcHAuc3ludGgoKTtcbiJdfQ==