import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from 'aws-lambda';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, PutCommand } from '@aws-sdk/lib-dynamodb';
import * as Jo<PERSON> from 'joi';
import { v4 as uuidv4 } from 'uuid';
import * as mime from 'mime-types';

// Initialize AWS clients
const s3Client = new S3Client({ region: process.env.AWS_REGION });
const dynamoClient = DynamoDBDocumentClient.from(new DynamoDBClient({ region: process.env.AWS_REGION }));

// Environment variables
const UPLOADS_BUCKET = process.env.UPLOADS_BUCKET!;
const SCANS_TABLE = process.env.SCANS_TABLE!;

// Allowed file types and sizes
const ALLOWED_MIME_TYPES = [
  'application/pdf',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/msword',
  'text/plain',
  'text/csv',
  'application/json',
  'application/xml',
  'text/xml',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/bmp',
  'image/tiff'
];

const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
const PRESIGNED_URL_EXPIRY = 300; // 5 minutes

// Validation schemas
const uploadRequestSchema = Joi.object({
  fileName: Joi.string().min(1).max(255).required(),
  fileSize: Joi.number().min(1).max(MAX_FILE_SIZE).required(),
  mimeType: Joi.string().valid(...ALLOWED_MIME_TYPES).required(),
  scanType: Joi.string().valid('security', 'compliance', 'anomaly', 'full').default('full'),
  description: Joi.string().max(500).optional()
});

// Response helper
const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
  statusCode,
  headers: {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type,Authorization',
    'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
  },
  body: JSON.stringify(body)
});

// Error handler
const handleError = (error: any): APIGatewayProxyResult => {
  console.error('Error:', error);
  
  if (error.name === 'ValidationError') {
    return createResponse(400, {
      error: 'Validation Error',
      message: error.details[0].message
    });
  }
  
  return createResponse(500, {
    error: 'Internal Server Error',
    message: 'An unexpected error occurred'
  });
};

// Extract user ID from JWT token
const extractUserIdFromToken = (event: APIGatewayProxyEvent): string => {
  const authHeader = event.headers.Authorization || event.headers.authorization;
  if (!authHeader) {
    throw new Error('Authorization header missing');
  }
  
  // In a real implementation, you would decode and validate the JWT
  // For now, we'll extract from the context (set by API Gateway authorizer)
  const userId = event.requestContext.authorizer?.claims?.sub;
  if (!userId) {
    throw new Error('User ID not found in token');
  }
  
  return userId;
};

// Generate presigned URL for upload
const generatePresignedUrl = async (
  scanId: string,
  fileName: string,
  mimeType: string,
  fileSize: number
): Promise<string> => {
  const key = `uploads/${scanId}/${fileName}`;
  
  const command = new PutObjectCommand({
    Bucket: UPLOADS_BUCKET,
    Key: key,
    ContentType: mimeType,
    ContentLength: fileSize,
    Metadata: {
      scanId,
      originalFileName: fileName,
      uploadedAt: new Date().toISOString()
    },
    ServerSideEncryption: 'aws:kms'
  });
  
  return await getSignedUrl(s3Client, command, { expiresIn: PRESIGNED_URL_EXPIRY });
};

// Create scan record
const createScanRecord = async (
  scanId: string,
  userId: string,
  fileName: string,
  fileSize: number,
  mimeType: string,
  scanType: string,
  description?: string
): Promise<void> => {
  const scanRecord = {
    scanId,
    userId,
    timestamp: Date.now(),
    fileName,
    fileSize,
    mimeType,
    scanType,
    description: description || '',
    status: 'pending_upload',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    s3Key: `uploads/${scanId}/${fileName}`,
    results: {},
    reportUrl: null,
    completedAt: null
  };
  
  await dynamoClient.send(new PutCommand({
    TableName: SCANS_TABLE,
    Item: scanRecord
  }));
};

// Handle upload request
const handleUploadRequest = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const body = event.body ? JSON.parse(event.body) : {};
    
    // Validate request
    const { error, value } = uploadRequestSchema.validate(body);
    if (error) throw error;
    
    const { fileName, fileSize, mimeType, scanType, description } = value;
    
    // Extract user ID
    const userId = extractUserIdFromToken(event);
    
    // Generate scan ID
    const scanId = uuidv4();
    
    // Validate file extension matches MIME type
    const expectedMimeType = mime.lookup(fileName);
    if (expectedMimeType && expectedMimeType !== mimeType) {
      return createResponse(400, {
        error: 'File Type Mismatch',
        message: 'File extension does not match the provided MIME type'
      });
    }
    
    // Generate presigned URL
    const uploadUrl = await generatePresignedUrl(scanId, fileName, mimeType, fileSize);
    
    // Create scan record
    await createScanRecord(scanId, userId, fileName, fileSize, mimeType, scanType, description);
    
    return createResponse(200, {
      scanId,
      uploadUrl,
      expiresIn: PRESIGNED_URL_EXPIRY,
      maxFileSize: MAX_FILE_SIZE,
      allowedTypes: ALLOWED_MIME_TYPES,
      message: 'Upload URL generated successfully'
    });
    
  } catch (error) {
    return handleError(error);
  }
};

// Handle upload completion notification
const handleUploadComplete = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const { scanId } = event.pathParameters || {};
    
    if (!scanId) {
      return createResponse(400, {
        error: 'Missing Parameter',
        message: 'Scan ID is required'
      });
    }
    
    // Extract user ID
    const userId = extractUserIdFromToken(event);
    
    // Update scan status to uploaded and trigger processing
    // This would typically trigger a Step Function or send an SQS message
    
    return createResponse(200, {
      message: 'Upload completed successfully',
      scanId,
      status: 'uploaded'
    });
    
  } catch (error) {
    return handleError(error);
  }
};

// Get upload status
const handleGetUploadStatus = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const { scanId } = event.pathParameters || {};
    
    if (!scanId) {
      return createResponse(400, {
        error: 'Missing Parameter',
        message: 'Scan ID is required'
      });
    }
    
    // Extract user ID
    const userId = extractUserIdFromToken(event);
    
    // Get scan record from DynamoDB
    // Implementation would fetch the scan record and return status
    
    return createResponse(200, {
      scanId,
      status: 'pending_upload',
      message: 'Upload status retrieved successfully'
    });
    
  } catch (error) {
    return handleError(error);
  }
};

// Main handler
export const handler = async (event: APIGatewayProxyEvent, context: Context): Promise<APIGatewayProxyResult> => {
  console.log('Event:', JSON.stringify(event, null, 2));
  
  try {
    const { httpMethod, path } = event;
    
    // Handle OPTIONS request for CORS
    if (httpMethod === 'OPTIONS') {
      return createResponse(200, {});
    }
    
    // Route requests
    if (httpMethod === 'POST' && path.endsWith('/upload')) {
      return await handleUploadRequest(event);
    } else if (httpMethod === 'POST' && path.includes('/upload/') && path.endsWith('/complete')) {
      return await handleUploadComplete(event);
    } else if (httpMethod === 'GET' && path.includes('/upload/')) {
      return await handleGetUploadStatus(event);
    }
    
    return createResponse(404, {
      error: 'Not Found',
      message: 'Endpoint not found'
    });
    
  } catch (error) {
    return handleError(error);
  }
};
