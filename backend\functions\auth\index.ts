import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from 'aws-lambda';
import { CognitoIdentityProviderClient, InitiateAuthCommand, SignUpCommand, ConfirmSignUpCommand, ResendConfirmationCodeCommand } from '@aws-sdk/client-cognito-identity-provider';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, PutCommand, GetCommand } from '@aws-sdk/lib-dynamodb';
import * as jwt from 'jsonwebtoken';
import * as Joi from 'joi';
import { v4 as uuidv4 } from 'uuid';

// Initialize AWS clients
const cognitoClient = new CognitoIdentityProviderClient({ region: process.env.AWS_REGION });
const dynamoClient = DynamoDBDocumentClient.from(new DynamoDBClient({ region: process.env.AWS_REGION }));

// Environment variables
const USER_POOL_CLIENT_ID = process.env.USER_POOL_CLIENT_ID!;
const USERS_TABLE = process.env.USERS_TABLE!;

// Validation schemas
const signUpSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(8).required(),
  firstName: Joi.string().min(1).max(50).required(),
  lastName: Joi.string().min(1).max(50).required()
});

const signInSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().required()
});

const confirmSignUpSchema = Joi.object({
  email: Joi.string().email().required(),
  confirmationCode: Joi.string().required()
});

// Response helper
const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
  statusCode,
  headers: {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type,Authorization',
    'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
  },
  body: JSON.stringify(body)
});

// Error handler
const handleError = (error: any): APIGatewayProxyResult => {
  console.error('Error:', error);
  
  if (error.name === 'ValidationError') {
    return createResponse(400, {
      error: 'Validation Error',
      message: error.details[0].message
    });
  }
  
  if (error.name === 'UserNotFoundException') {
    return createResponse(404, {
      error: 'User Not Found',
      message: 'User does not exist'
    });
  }
  
  if (error.name === 'NotAuthorizedException') {
    return createResponse(401, {
      error: 'Authentication Failed',
      message: 'Invalid credentials'
    });
  }
  
  if (error.name === 'UsernameExistsException') {
    return createResponse(409, {
      error: 'User Already Exists',
      message: 'A user with this email already exists'
    });
  }
  
  return createResponse(500, {
    error: 'Internal Server Error',
    message: 'An unexpected error occurred'
  });
};

// Sign up handler
const handleSignUp = async (body: any): Promise<APIGatewayProxyResult> => {
  const { error, value } = signUpSchema.validate(body);
  if (error) throw error;
  
  const { email, password, firstName, lastName } = value;
  
  try {
    // Create user in Cognito
    const signUpCommand = new SignUpCommand({
      ClientId: USER_POOL_CLIENT_ID,
      Username: email,
      Password: password,
      UserAttributes: [
        { Name: 'email', Value: email },
        { Name: 'given_name', Value: firstName },
        { Name: 'family_name', Value: lastName }
      ]
    });
    
    const cognitoResponse = await cognitoClient.send(signUpCommand);
    
    // Store user profile in DynamoDB
    const userId = uuidv4();
    const userProfile = {
      userId,
      email,
      firstName,
      lastName,
      cognitoSub: cognitoResponse.UserSub,
      createdAt: new Date().toISOString(),
      isEmailVerified: false,
      scanCount: 0,
      lastLoginAt: null
    };
    
    await dynamoClient.send(new PutCommand({
      TableName: USERS_TABLE,
      Item: userProfile
    }));
    
    return createResponse(201, {
      message: 'User created successfully. Please check your email for verification code.',
      userId,
      userSub: cognitoResponse.UserSub
    });
    
  } catch (error) {
    throw error;
  }
};

// Sign in handler
const handleSignIn = async (body: any): Promise<APIGatewayProxyResult> => {
  const { error, value } = signInSchema.validate(body);
  if (error) throw error;
  
  const { email, password } = value;
  
  try {
    const authCommand = new InitiateAuthCommand({
      ClientId: USER_POOL_CLIENT_ID,
      AuthFlow: 'USER_PASSWORD_AUTH',
      AuthParameters: {
        USERNAME: email,
        PASSWORD: password
      }
    });
    
    const authResponse = await cognitoClient.send(authCommand);
    
    if (authResponse.AuthenticationResult) {
      // Update last login time
      const userResponse = await dynamoClient.send(new GetCommand({
        TableName: USERS_TABLE,
        Key: { email }
      }));
      
      if (userResponse.Item) {
        await dynamoClient.send(new PutCommand({
          TableName: USERS_TABLE,
          Item: {
            ...userResponse.Item,
            lastLoginAt: new Date().toISOString()
          }
        }));
      }
      
      return createResponse(200, {
        message: 'Authentication successful',
        tokens: {
          accessToken: authResponse.AuthenticationResult.AccessToken,
          idToken: authResponse.AuthenticationResult.IdToken,
          refreshToken: authResponse.AuthenticationResult.RefreshToken
        },
        expiresIn: authResponse.AuthenticationResult.ExpiresIn
      });
    } else {
      return createResponse(401, {
        error: 'Authentication Failed',
        message: 'Invalid credentials'
      });
    }
    
  } catch (error) {
    throw error;
  }
};

// Confirm sign up handler
const handleConfirmSignUp = async (body: any): Promise<APIGatewayProxyResult> => {
  const { error, value } = confirmSignUpSchema.validate(body);
  if (error) throw error;
  
  const { email, confirmationCode } = value;
  
  try {
    const confirmCommand = new ConfirmSignUpCommand({
      ClientId: USER_POOL_CLIENT_ID,
      Username: email,
      ConfirmationCode: confirmationCode
    });
    
    await cognitoClient.send(confirmCommand);
    
    // Update user profile
    const userResponse = await dynamoClient.send(new GetCommand({
      TableName: USERS_TABLE,
      Key: { email }
    }));
    
    if (userResponse.Item) {
      await dynamoClient.send(new PutCommand({
        TableName: USERS_TABLE,
        Item: {
          ...userResponse.Item,
          isEmailVerified: true,
          verifiedAt: new Date().toISOString()
        }
      }));
    }
    
    return createResponse(200, {
      message: 'Email verified successfully. You can now sign in.'
    });
    
  } catch (error) {
    throw error;
  }
};

// Resend confirmation code handler
const handleResendConfirmation = async (body: any): Promise<APIGatewayProxyResult> => {
  const { email } = body;
  
  if (!email) {
    return createResponse(400, {
      error: 'Validation Error',
      message: 'Email is required'
    });
  }
  
  try {
    const resendCommand = new ResendConfirmationCodeCommand({
      ClientId: USER_POOL_CLIENT_ID,
      Username: email
    });
    
    await cognitoClient.send(resendCommand);
    
    return createResponse(200, {
      message: 'Confirmation code sent successfully'
    });
    
  } catch (error) {
    throw error;
  }
};

// Main handler
export const handler = async (event: APIGatewayProxyEvent, context: Context): Promise<APIGatewayProxyResult> => {
  console.log('Event:', JSON.stringify(event, null, 2));
  
  try {
    const { httpMethod, path } = event;
    const body = event.body ? JSON.parse(event.body) : {};
    
    // Handle OPTIONS request for CORS
    if (httpMethod === 'OPTIONS') {
      return createResponse(200, {});
    }
    
    // Route requests
    if (httpMethod === 'POST') {
      if (path.endsWith('/signup')) {
        return await handleSignUp(body);
      } else if (path.endsWith('/signin')) {
        return await handleSignIn(body);
      } else if (path.endsWith('/confirm')) {
        return await handleConfirmSignUp(body);
      } else if (path.endsWith('/resend')) {
        return await handleResendConfirmation(body);
      }
    }
    
    return createResponse(404, {
      error: 'Not Found',
      message: 'Endpoint not found'
    });
    
  } catch (error) {
    return handleError(error);
  }
};
