import json
import boto3
import os
import tempfile
from datetime import datetime
from typing import Dict, Any, List, Optional
import structlog
from jinja2 import Template
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT

# Configure logging
logger = structlog.get_logger()

# Initialize AWS clients
s3 = boto3.client('s3')
bedrock_runtime = boto3.client('bedrock-runtime')
dynamodb = boto3.resource('dynamodb')

# Environment variables
REPORTS_BUCKET = os.environ['REPORTS_BUCKET']
RESULTS_TABLE = os.environ['RESULTS_TABLE']
SCANS_TABLE = os.environ['SCANS_TABLE']

# Report templates
EXECUTIVE_SUMMARY_TEMPLATE = """
Based on the comprehensive security and compliance analysis of the uploaded file, here are the key findings:

**Security Assessment:**
{% if security_results %}
- Risk Level: {{ security_results.riskLevel }}
- Risk Score: {{ security_results.riskScore }}/100
- Total Security Findings: {{ security_results.summary.totalFindings }}
{% if security_results.summary.hasCredentials %}
- ⚠️ CRITICAL: Exposed credentials detected
{% endif %}
{% if security_results.summary.hasMalware %}
- ⚠️ WARNING: Potential malware signatures found
{% endif %}
{% else %}
- Security scan not completed or failed
{% endif %}

**Compliance Assessment:**
{% if pii_results %}
- PII Risk Level: {{ pii_results.riskLevel }}
- Total PII Instances: {{ pii_results.summary.totalPiiInstances }}
- Compliance Frameworks Violated: {{ pii_results.summary.complianceFrameworksViolated | length }}
{% for framework in pii_results.complianceViolations %}
- {{ framework }}: {{ pii_results.complianceViolations[framework].violation_level }} violation
{% endfor %}
{% else %}
- PII scan not completed or failed
{% endif %}

**Anomaly Detection:**
{% if anomaly_results %}
- Anomaly Risk Level: {{ anomaly_results.riskLevel }}
- Total Anomalies Detected: {{ anomaly_results.summary.totalAnomalies }}
{% if anomaly_results.summary.hasStatisticalAnomalies %}
- Statistical anomalies detected
{% endif %}
{% if anomaly_results.summary.hasPatternAnomalies %}
- Pattern anomalies detected
{% endif %}
{% else %}
- Anomaly detection not completed or failed
{% endif %}

**Recommendations:**
{% if recommendations %}
{% for rec in recommendations %}
- {{ rec }}
{% endfor %}
{% else %}
- No specific recommendations at this time
{% endif %}

**Overall Assessment:**
The file has been analyzed for security threats, compliance violations, and anomalous patterns. 
{% if overall_risk_level == 'CRITICAL' %}
IMMEDIATE ACTION REQUIRED: Critical security or compliance issues detected.
{% elif overall_risk_level == 'HIGH' %}
HIGH PRIORITY: Significant issues require prompt attention.
{% elif overall_risk_level == 'MEDIUM' %}
MODERATE RISK: Issues should be addressed in a timely manner.
{% else %}
LOW RISK: Minor or no significant issues detected.
{% endif %}
"""

def get_scan_results(scan_id: str) -> Dict[str, Any]:
    """Get all scan results for a scan ID"""
    try:
        table = dynamodb.Table(RESULTS_TABLE)
        
        # Query all results for this scan
        response = table.query(
            KeyConditionExpression='scanId = :scan_id',
            ExpressionAttributeValues={':scan_id': scan_id}
        )
        
        results = {}
        for item in response.get('Items', []):
            result_type = item.get('resultType')
            if result_type:
                results[result_type] = item.get('results', {})
        
        return results
    except Exception as e:
        logger.error("Failed to get scan results", scan_id=scan_id, error=str(e))
        return {}

def get_scan_metadata(scan_id: str) -> Dict[str, Any]:
    """Get scan metadata from scans table"""
    try:
        table = dynamodb.Table(SCANS_TABLE)
        
        response = table.get_item(Key={'scanId': scan_id})
        return response.get('Item', {})
    except Exception as e:
        logger.error("Failed to get scan metadata", scan_id=scan_id, error=str(e))
        return {}

def generate_executive_summary_with_bedrock(scan_results: Dict[str, Any], scan_metadata: Dict[str, Any]) -> str:
    """Generate executive summary using Amazon Bedrock"""
    try:
        # Prepare context for Bedrock
        context = {
            'fileName': scan_metadata.get('fileName', 'Unknown'),
            'scanType': scan_metadata.get('scanType', 'full'),
            'fileSize': scan_metadata.get('fileSize', 0),
            'security_results': scan_results.get('security', {}),
            'pii_results': scan_results.get('pii', {}),
            'anomaly_results': scan_results.get('anomaly', {}),
            'recommendations': generate_recommendations(scan_results),
            'overall_risk_level': calculate_overall_risk_level(scan_results)
        }
        
        # Render template
        template = Template(EXECUTIVE_SUMMARY_TEMPLATE)
        summary = template.render(**context)
        
        # Use Bedrock to enhance the summary
        prompt = f"""
        Please review and enhance the following security scan executive summary. 
        Make it more professional, clear, and actionable while maintaining all the key information:

        {summary}

        Please provide an enhanced version that is:
        1. Professional and executive-friendly
        2. Clear and concise
        3. Actionable with specific next steps
        4. Properly formatted
        """
        
        try:
            # Call Bedrock Claude model
            body = {
                "prompt": f"\n\nHuman: {prompt}\n\nAssistant:",
                "max_tokens_to_sample": 2000,
                "temperature": 0.3,
                "top_p": 0.9
            }
            
            response = bedrock_runtime.invoke_model(
                modelId="anthropic.claude-v2",
                contentType="application/json",
                accept="application/json",
                body=json.dumps(body)
            )
            
            response_body = json.loads(response['body'].read())
            enhanced_summary = response_body.get('completion', summary)
            
            return enhanced_summary.strip()
            
        except Exception as e:
            logger.warning("Failed to enhance summary with Bedrock, using template", error=str(e))
            return summary
            
    except Exception as e:
        logger.error("Failed to generate executive summary", error=str(e))
        return "Executive summary generation failed."

def generate_recommendations(scan_results: Dict[str, Any]) -> List[str]:
    """Generate recommendations based on scan results"""
    recommendations = []
    
    try:
        # Security recommendations
        security_results = scan_results.get('security', {})
        if security_results.get('summary', {}).get('hasCredentials'):
            recommendations.append("URGENT: Remove or rotate all exposed credentials immediately")
        
        if security_results.get('summary', {}).get('hasMalware'):
            recommendations.append("Quarantine file and perform thorough malware analysis")
        
        if security_results.get('riskLevel') in ['HIGH', 'CRITICAL']:
            recommendations.append("Conduct immediate security review of file contents")
        
        # PII recommendations
        pii_results = scan_results.get('pii', {})
        compliance_violations = pii_results.get('complianceViolations', {})
        
        if 'GDPR' in compliance_violations:
            recommendations.append("Ensure GDPR compliance measures are in place for personal data")
        
        if 'HIPAA' in compliance_violations:
            recommendations.append("Implement HIPAA safeguards for protected health information")
        
        if 'PCI_DSS' in compliance_violations:
            recommendations.append("Apply PCI DSS security controls for payment card data")
        
        if pii_results.get('riskLevel') in ['HIGH', 'CRITICAL']:
            recommendations.append("Review data handling procedures for personal information")
        
        # Anomaly recommendations
        anomaly_results = scan_results.get('anomaly', {})
        if anomaly_results.get('summary', {}).get('hasPatternAnomalies'):
            recommendations.append("Investigate unusual patterns that may indicate data corruption or encoding issues")
        
        if anomaly_results.get('riskLevel') in ['HIGH', 'CRITICAL']:
            recommendations.append("Perform detailed analysis of detected anomalies")
        
        # General recommendations
        if not recommendations:
            recommendations.append("Continue monitoring and maintain current security practices")
        
        return recommendations
    except Exception as e:
        logger.error("Failed to generate recommendations", error=str(e))
        return ["Review scan results and consult security team"]

def calculate_overall_risk_level(scan_results: Dict[str, Any]) -> str:
    """Calculate overall risk level from all scan results"""
    try:
        risk_levels = []
        
        for result_type, results in scan_results.items():
            risk_level = results.get('riskLevel')
            if risk_level:
                risk_levels.append(risk_level)
        
        if not risk_levels:
            return 'UNKNOWN'
        
        # Priority order for risk levels
        priority = {'CRITICAL': 4, 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1, 'MINIMAL': 0}
        
        # Return the highest risk level
        highest_risk = max(risk_levels, key=lambda x: priority.get(x, 0))
        return highest_risk
    except Exception as e:
        logger.error("Failed to calculate overall risk level", error=str(e))
        return 'UNKNOWN'

def create_pdf_report(scan_id: str, scan_metadata: Dict[str, Any], scan_results: Dict[str, Any], executive_summary: str) -> str:
    """Create PDF report using ReportLab"""
    try:
        # Create temporary file for PDF
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_file.close()
        
        # Create PDF document
        doc = SimpleDocTemplate(temp_file.name, pagesize=A4)
        story = []
        
        # Get styles
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        )
        
        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=12,
            textColor=colors.darkblue
        )
        
        # Title page
        story.append(Paragraph("AI Security & Compliance Scan Report", title_style))
        story.append(Spacer(1, 0.5*inch))
        
        # Scan information table
        scan_info_data = [
            ['Scan ID:', scan_id],
            ['File Name:', scan_metadata.get('fileName', 'Unknown')],
            ['File Size:', f"{scan_metadata.get('fileSize', 0):,} bytes"],
            ['Scan Type:', scan_metadata.get('scanType', 'full').upper()],
            ['Scan Date:', scan_metadata.get('createdAt', 'Unknown')],
            ['Overall Risk:', calculate_overall_risk_level(scan_results)]
        ]
        
        scan_info_table = Table(scan_info_data, colWidths=[2*inch, 4*inch])
        scan_info_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('BACKGROUND', (1, -1), (1, -1), colors.yellow if calculate_overall_risk_level(scan_results) in ['HIGH', 'CRITICAL'] else colors.lightgreen),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(scan_info_table)
        story.append(Spacer(1, 0.5*inch))
        
        # Executive Summary
        story.append(Paragraph("Executive Summary", heading_style))
        for paragraph in executive_summary.split('\n\n'):
            if paragraph.strip():
                story.append(Paragraph(paragraph.strip(), styles['Normal']))
                story.append(Spacer(1, 12))
        
        story.append(PageBreak())
        
        # Detailed Results
        story.append(Paragraph("Detailed Scan Results", heading_style))
        
        # Security Results
        if 'security' in scan_results:
            story.append(Paragraph("Security Analysis", styles['Heading3']))
            security = scan_results['security']
            
            security_data = [
                ['Risk Level:', security.get('riskLevel', 'Unknown')],
                ['Risk Score:', f"{security.get('riskScore', 0)}/100"],
                ['Total Findings:', security.get('summary', {}).get('totalFindings', 0)],
                ['Categories Found:', ', '.join(security.get('summary', {}).get('categoriesFound', []))]
            ]
            
            security_table = Table(security_data, colWidths=[2*inch, 4*inch])
            security_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
            ]))
            
            story.append(security_table)
            story.append(Spacer(1, 20))
        
        # PII Results
        if 'pii' in scan_results:
            story.append(Paragraph("PII & Compliance Analysis", styles['Heading3']))
            pii = scan_results['pii']
            
            pii_data = [
                ['Risk Level:', pii.get('riskLevel', 'Unknown')],
                ['Total PII Instances:', pii.get('summary', {}).get('totalPiiInstances', 0)],
                ['PII Types Found:', ', '.join(pii.get('summary', {}).get('piiTypesFound', []))],
                ['Compliance Violations:', ', '.join(pii.get('summary', {}).get('complianceFrameworksViolated', []))]
            ]
            
            pii_table = Table(pii_data, colWidths=[2*inch, 4*inch])
            pii_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
            ]))
            
            story.append(pii_table)
            story.append(Spacer(1, 20))
        
        # Anomaly Results
        if 'anomaly' in scan_results:
            story.append(Paragraph("Anomaly Detection Analysis", styles['Heading3']))
            anomaly = scan_results['anomaly']
            
            anomaly_data = [
                ['Risk Level:', anomaly.get('riskLevel', 'Unknown')],
                ['Total Anomalies:', anomaly.get('summary', {}).get('totalAnomalies', 0)],
                ['Anomaly Types:', ', '.join(anomaly.get('summary', {}).get('anomalyTypes', []))],
                ['Highest Severity:', anomaly.get('summary', {}).get('highestSeverity', 'None')]
            ]
            
            anomaly_table = Table(anomaly_data, colWidths=[2*inch, 4*inch])
            anomaly_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
            ]))
            
            story.append(anomaly_table)
        
        # Build PDF
        doc.build(story)
        
        return temp_file.name
    except Exception as e:
        logger.error("Failed to create PDF report", error=str(e))
        raise

def upload_report_to_s3(local_file_path: str, scan_id: str) -> str:
    """Upload report to S3 and return URL"""
    try:
        s3_key = f"reports/{scan_id}/security-compliance-report.pdf"
        
        s3.upload_file(
            local_file_path,
            REPORTS_BUCKET,
            s3_key,
            ExtraArgs={
                'ContentType': 'application/pdf',
                'ServerSideEncryption': 'aws:kms'
            }
        )
        
        # Generate presigned URL for download (valid for 7 days)
        report_url = s3.generate_presigned_url(
            'get_object',
            Params={'Bucket': REPORTS_BUCKET, 'Key': s3_key},
            ExpiresIn=7*24*60*60  # 7 days
        )
        
        return report_url
    except Exception as e:
        logger.error("Failed to upload report to S3", error=str(e))
        raise

def update_scan_with_report(scan_id: str, report_url: str) -> None:
    """Update scan record with report URL"""
    try:
        table = dynamodb.Table(SCANS_TABLE)
        
        table.update_item(
            Key={'scanId': scan_id},
            UpdateExpression="SET reportUrl = :report_url, #status = :status, updatedAt = :updated_at",
            ExpressionAttributeNames={'#status': 'status'},
            ExpressionAttributeValues={
                ':report_url': report_url,
                ':status': 'completed',
                ':updated_at': datetime.utcnow().isoformat()
            }
        )
        
        logger.info("Scan updated with report URL", scan_id=scan_id)
    except Exception as e:
        logger.error("Failed to update scan with report", scan_id=scan_id, error=str(e))
        raise

def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """Main Lambda handler for report generation"""
    logger.info("Report generation started", event=event)
    
    try:
        # Extract parameters from event
        scan_id = event.get('scanId')
        
        if not scan_id:
            raise ValueError("Missing required parameter: scanId")
        
        # Get scan metadata and results
        scan_metadata = get_scan_metadata(scan_id)
        scan_results = get_scan_results(scan_id)
        
        if not scan_results:
            raise ValueError("No scan results found for scan ID")
        
        # Generate executive summary
        executive_summary = generate_executive_summary_with_bedrock(scan_results, scan_metadata)
        
        # Create PDF report
        pdf_file_path = create_pdf_report(scan_id, scan_metadata, scan_results, executive_summary)
        
        try:
            # Upload to S3
            report_url = upload_report_to_s3(pdf_file_path, scan_id)
            
            # Update scan record
            update_scan_with_report(scan_id, report_url)
            
            # Prepare results
            results = {
                'scanType': 'report',
                'status': 'completed',
                'reportUrl': report_url,
                'executiveSummary': executive_summary,
                'overallRiskLevel': calculate_overall_risk_level(scan_results),
                'recommendations': generate_recommendations(scan_results),
                'completedAt': datetime.utcnow().isoformat()
            }
            
            logger.info("Report generation completed", 
                       scan_id=scan_id, 
                       overall_risk=results['overallRiskLevel'])
            
            return results
            
        finally:
            # Clean up temporary file
            try:
                os.unlink(pdf_file_path)
            except Exception:
                pass
    
    except Exception as e:
        logger.error("Report generation failed", scan_id=scan_id, error=str(e))
        
        error_results = {
            'scanType': 'report',
            'status': 'failed',
            'error': str(e),
            'completedAt': datetime.utcnow().isoformat()
        }
        
        raise
