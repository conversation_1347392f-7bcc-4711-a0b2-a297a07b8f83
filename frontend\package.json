{"name": "ai-security-scanner-frontend", "version": "1.0.0", "description": "React frontend for AI Security Scanner", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "clean": "rimraf .next out dist node_modules/.cache"}, "dependencies": {"next": "^14.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "@aws-amplify/ui-react": "^6.0.7", "aws-amplify": "^6.0.7", "@heroicons/react": "^2.0.18", "react-query": "^3.39.3", "react-hook-form": "^7.47.0", "react-dropzone": "^14.2.3", "recharts": "^2.8.0", "date-fns": "^2.30.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "lucide-react": "^0.294.0", "framer-motion": "^10.16.5", "react-hot-toast": "^2.4.1", "zustand": "^4.4.7"}, "devDependencies": {"@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-config-next": "^14.0.3", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.31", "prettier": "^3.1.0", "rimraf": "^5.0.5", "tailwindcss": "^3.3.5", "typescript": "^5.2.2"}, "keywords": ["react", "nextjs", "aws", "security", "compliance", "ai", "scanner"], "author": "AWS AI Security Scanner Team", "license": "MIT"}