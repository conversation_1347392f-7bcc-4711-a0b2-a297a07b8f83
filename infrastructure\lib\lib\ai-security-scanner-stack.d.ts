import * as cdk from 'aws-cdk-lib';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import * as stepfunctions from 'aws-cdk-lib/aws-stepfunctions';
import { Construct } from 'constructs';
export interface AiSecurityScannerStackProps extends cdk.StackProps {
    environment: string;
    config: any;
}
export declare class AiSecurityScannerStack extends cdk.Stack {
    readonly apiGateway: apigateway.RestApi;
    readonly apiGatewayUrl: string;
    readonly userPoolId: string;
    readonly userPoolClientId: string;
    readonly identityPoolId: string;
    readonly lambdaFunctions: {
        [key: string]: lambda.Function;
    };
    readonly stepFunction: stepfunctions.StateMachine;
    constructor(scope: Construct, id: string, props: AiSecurityScannerStackProps);
    private createLambdaFunctions;
    private createStepFunction;
    private createApiGateway;
}
