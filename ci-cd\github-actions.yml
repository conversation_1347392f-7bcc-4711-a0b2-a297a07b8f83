name: AI Security Scanner CI/CD

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  AWS_REGION: us-east-1
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.9'

jobs:
  # Security and Quality Checks
  security-scan:
    name: <PERSON> Scan
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: |
          npm ci
          cd infrastructure && npm ci
          cd ../frontend && npm ci

      - name: Run Snyk Security Scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install Python dependencies
        run: |
          pip install bandit safety
          find backend/functions -name requirements.txt -exec pip install -r {} \;

      - name: Run Bandit Security Scan
        run: |
          bandit -r backend/ -f json -o bandit-report.json || true
          bandit -r backend/ -f txt

      - name: Run Safety Check
        run: safety check --json --output safety-report.json || true

      - name: Upload security reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: security-reports
          path: |
            bandit-report.json
            safety-report.json

  # Code Quality and Linting
  code-quality:
    name: Code Quality
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: |
          npm ci
          cd infrastructure && npm ci
          cd ../frontend && npm ci

      - name: Lint Infrastructure
        run: cd infrastructure && npm run lint

      - name: Lint Frontend
        run: cd frontend && npm run lint

      - name: Type Check Frontend
        run: cd frontend && npm run type-check

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install Python linting tools
        run: |
          pip install flake8 black isort mypy
          find backend/functions -name requirements.txt -exec pip install -r {} \;

      - name: Lint Python code
        run: |
          flake8 backend/ --max-line-length=100 --ignore=E203,W503
          black --check backend/
          isort --check-only backend/

  # Unit Tests
  test:
    name: Unit Tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: |
          npm ci
          cd infrastructure && npm ci
          cd ../frontend && npm ci

      - name: Test Infrastructure
        run: cd infrastructure && npm test

      - name: Test Frontend
        run: cd frontend && npm run test:coverage

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install Python test dependencies
        run: |
          pip install pytest pytest-cov moto
          find backend/functions -name requirements.txt -exec pip install -r {} \;

      - name: Test Python functions
        run: |
          cd backend
          python -m pytest tests/ --cov=functions --cov-report=xml --cov-report=html

      - name: Upload test coverage
        uses: codecov/codecov-action@v3
        with:
          files: ./backend/coverage.xml,./frontend/coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

  # Build and Package
  build:
    name: Build and Package
    runs-on: ubuntu-latest
    needs: [security-scan, code-quality, test]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: |
          npm ci
          cd infrastructure && npm ci
          cd ../frontend && npm ci

      - name: Build Infrastructure
        run: cd infrastructure && npm run build

      - name: Build Frontend
        run: cd frontend && npm run build

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Package Lambda functions
        run: |
          cd backend
          for dir in functions/*/; do
            if [ -f "$dir/requirements.txt" ]; then
              echo "Packaging $dir"
              cd "$dir"
              pip install -r requirements.txt -t .
              zip -r "../$(basename "$dir").zip" . -x "*.pyc" "__pycache__/*" "tests/*"
              cd ../..
            fi
          done

      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-artifacts
          path: |
            infrastructure/lib/
            frontend/out/
            backend/functions/*.zip

  # Deploy to Development
  deploy-dev:
    name: Deploy to Development
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/develop'
    environment: development
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-artifacts

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install CDK
        run: npm install -g aws-cdk

      - name: Deploy Infrastructure
        run: |
          cd infrastructure
          npm ci
          cdk deploy --all --require-approval never --context environment=dev
        env:
          ALERT_EMAIL: ${{ secrets.ALERT_EMAIL }}

      - name: Deploy Frontend
        run: |
          aws s3 sync frontend/out/ s3://ai-scanner-frontend-dev-${{ secrets.AWS_ACCOUNT_ID }}/ --delete
          aws cloudfront create-invalidation --distribution-id ${{ secrets.DEV_CLOUDFRONT_DISTRIBUTION_ID }} --paths "/*"

  # Deploy to Production
  deploy-prod:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main'
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-artifacts

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.PROD_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.PROD_AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install CDK
        run: npm install -g aws-cdk

      - name: Deploy Infrastructure
        run: |
          cd infrastructure
          npm ci
          cdk deploy --all --require-approval never --context environment=prod
        env:
          ALERT_EMAIL: ${{ secrets.PROD_ALERT_EMAIL }}
          CERTIFICATE_ARN: ${{ secrets.PROD_CERTIFICATE_ARN }}

      - name: Deploy Frontend
        run: |
          aws s3 sync frontend/out/ s3://ai-scanner-frontend-prod-${{ secrets.AWS_ACCOUNT_ID }}/ --delete
          aws cloudfront create-invalidation --distribution-id ${{ secrets.PROD_CLOUDFRONT_DISTRIBUTION_ID }} --paths "/*"

      - name: Run smoke tests
        run: |
          curl -f https://ai-security-scanner.com/health || exit 1
          echo "Production deployment successful"

  # Post-deployment tests
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: [deploy-dev]
    if: github.ref == 'refs/heads/develop'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install test dependencies
        run: |
          npm install -g newman
          pip install requests pytest

      - name: Run API tests
        run: |
          newman run tests/postman/ai-scanner-api.json \
            --environment tests/postman/dev-environment.json \
            --reporters cli,json \
            --reporter-json-export newman-results.json

      - name: Run end-to-end tests
        run: |
          cd tests/e2e
          python -m pytest test_upload_flow.py test_scan_flow.py -v

      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: integration-test-results
          path: |
            newman-results.json
            tests/e2e/reports/
