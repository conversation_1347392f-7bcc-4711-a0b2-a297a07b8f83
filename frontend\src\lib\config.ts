// Environment configuration
export const config = {
  api: {
    baseUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001',
    timeout: 30000,
  },
  aws: {
    region: process.env.NEXT_PUBLIC_REGION || 'us-east-1',
    userPoolId: process.env.NEXT_PUBLIC_USER_POOL_ID || '',
    userPoolClientId: process.env.NEXT_PUBLIC_USER_POOL_CLIENT_ID || '',
    identityPoolId: process.env.NEXT_PUBLIC_IDENTITY_POOL_ID || '',
  },
  app: {
    name: 'AI Security Scanner',
    version: '1.0.0',
    environment: process.env.NEXT_PUBLIC_ENVIRONMENT || 'development',
    isDevelopment: process.env.NEXT_PUBLIC_ENVIRONMENT === 'development',
    isProduction: process.env.NEXT_PUBLIC_ENVIRONMENT === 'production',
  },
  upload: {
    maxFileSize: 50 * 1024 * 1024, // 50MB
    allowedTypes: [
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword',
      'text/plain',
      'text/csv',
      'application/json',
      'application/xml',
      'text/xml',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/bmp',
      'image/tiff',
    ],
    allowedExtensions: [
      '.pdf',
      '.docx',
      '.doc',
      '.txt',
      '.csv',
      '.json',
      '.xml',
      '.xls',
      '.xlsx',
      '.jpg',
      '.jpeg',
      '.png',
      '.gif',
      '.bmp',
      '.tiff',
    ],
  },
  scan: {
    types: [
      { value: 'security', label: 'Security Only', description: 'Scan for security threats and vulnerabilities' },
      { value: 'compliance', label: 'Compliance Only', description: 'Check for PII and compliance violations' },
      { value: 'anomaly', label: 'Anomaly Only', description: 'Detect unusual patterns and behaviors' },
      { value: 'full', label: 'Full Scan', description: 'Complete security, compliance, and anomaly analysis' },
    ],
    statusColors: {
      pending_upload: 'gray',
      uploaded: 'blue',
      processing: 'yellow',
      completed: 'green',
      failed: 'red',
      cancelled: 'gray',
    },
    riskColors: {
      MINIMAL: 'green',
      LOW: 'green',
      MEDIUM: 'yellow',
      HIGH: 'orange',
      CRITICAL: 'red',
    },
  },
  ui: {
    pageSize: 20,
    debounceDelay: 300,
    toastDuration: 5000,
    animationDuration: 300,
  },
  features: {
    enableAnalytics: false,
    enableNotifications: true,
    enableDarkMode: false,
    enableExport: true,
    enableSharing: false,
  },
};

// Validation functions
export const validateConfig = () => {
  const required = [
    'NEXT_PUBLIC_API_URL',
    'NEXT_PUBLIC_USER_POOL_ID',
    'NEXT_PUBLIC_USER_POOL_CLIENT_ID',
    'NEXT_PUBLIC_IDENTITY_POOL_ID',
    'NEXT_PUBLIC_REGION',
  ];

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    console.warn('Missing required environment variables:', missing);
    if (config.app.isProduction) {
      throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
    }
  }
};

// Helper functions
export const getApiUrl = (path: string) => {
  const baseUrl = config.api.baseUrl.replace(/\/$/, '');
  const cleanPath = path.replace(/^\//, '');
  return `${baseUrl}/${cleanPath}`;
};

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const isFileTypeAllowed = (file: File): boolean => {
  return config.upload.allowedTypes.includes(file.type) ||
         config.upload.allowedExtensions.some(ext => file.name.toLowerCase().endsWith(ext));
};

export const isFileSizeAllowed = (file: File): boolean => {
  return file.size <= config.upload.maxFileSize;
};

export const getRiskLevelColor = (riskLevel: string): string => {
  return config.scan.riskColors[riskLevel as keyof typeof config.scan.riskColors] || 'gray';
};

export const getStatusColor = (status: string): string => {
  return config.scan.statusColors[status as keyof typeof config.scan.statusColors] || 'gray';
};

// Initialize configuration validation
if (typeof window !== 'undefined') {
  validateConfig();
}
