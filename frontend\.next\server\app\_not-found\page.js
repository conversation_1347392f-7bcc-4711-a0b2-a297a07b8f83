(()=>{var e={};e.id=409,e.ids=[409],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7790:e=>{"use strict";e.exports=require("assert")},8893:e=>{"use strict";e.exports=require("buffer")},4770:e=>{"use strict";e.exports=require("crypto")},2048:e=>{"use strict";e.exports=require("fs")},6162:e=>{"use strict";e.exports=require("stream")},1764:e=>{"use strict";e.exports=require("util")},1568:e=>{"use strict";e.exports=require("zlib")},6760:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>u,routeModule:()=>f,tree:()=>c}),r(7352),r(5866),r(5495);var n=r(3191),o=r(8716),i=r(7922),s=r.n(i),a=r(5231),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let c=["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,5866,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,5495)),"C:\\Users\\<USER>\\Desktop\\AWS Task\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,5866,23)),"next/dist/client/components/not-found-error"]}],u=[],d="/_not-found/page",p={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},1167:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2994,23)),Promise.resolve().then(r.t.bind(r,6114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,9671,23)),Promise.resolve().then(r.t.bind(r,1868,23)),Promise.resolve().then(r.t.bind(r,4759,23))},6390:(e,t,r)=>{Promise.resolve().then(r.bind(r,2395))},2395:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>p});var n=r(326),o=r(3999),i=r(2735),s=r(381),a=r(7577);r(7082);var l=r(1070);let c={api:{baseUrl:process.env.NEXT_PUBLIC_API_URL||"http://localhost:3001",timeout:3e4},aws:{region:process.env.NEXT_PUBLIC_REGION||"us-east-1",userPoolId:process.env.NEXT_PUBLIC_USER_POOL_ID||"",userPoolClientId:process.env.NEXT_PUBLIC_USER_POOL_CLIENT_ID||"",identityPoolId:process.env.NEXT_PUBLIC_IDENTITY_POOL_ID||""},app:{name:"AI Security Scanner",version:"1.0.0",environment:process.env.NEXT_PUBLIC_ENVIRONMENT||"development",isDevelopment:"development"===process.env.NEXT_PUBLIC_ENVIRONMENT,isProduction:"production"===process.env.NEXT_PUBLIC_ENVIRONMENT},upload:{maxFileSize:52428800,allowedTypes:["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/msword","text/plain","text/csv","application/json","application/xml","text/xml","application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","image/jpeg","image/png","image/gif","image/bmp","image/tiff"],allowedExtensions:[".pdf",".docx",".doc",".txt",".csv",".json",".xml",".xls",".xlsx",".jpg",".jpeg",".png",".gif",".bmp",".tiff"]},scan:{types:[{value:"security",label:"Security Only",description:"Scan for security threats and vulnerabilities"},{value:"compliance",label:"Compliance Only",description:"Check for PII and compliance violations"},{value:"anomaly",label:"Anomaly Only",description:"Detect unusual patterns and behaviors"},{value:"full",label:"Full Scan",description:"Complete security, compliance, and anomaly analysis"}],statusColors:{pending_upload:"gray",uploaded:"blue",processing:"yellow",completed:"green",failed:"red",cancelled:"gray"},riskColors:{MINIMAL:"green",LOW:"green",MEDIUM:"yellow",HIGH:"orange",CRITICAL:"red"}},ui:{pageSize:20,debounceDelay:300,toastDuration:5e3,animationDuration:300},features:{enableAnalytics:!1,enableNotifications:!0,enableDarkMode:!1,enableExport:!0,enableSharing:!1}},u={Auth:{Cognito:{userPoolId:c.aws.userPoolId,userPoolClientId:c.aws.userPoolClientId,identityPoolId:c.aws.identityPoolId,loginWith:{email:!0},signUpVerificationMethod:"code",userAttributes:{email:{required:!0},given_name:{required:!0},family_name:{required:!0}},allowGuestAccess:!1,passwordFormat:{minLength:8,requireLowercase:!0,requireUppercase:!0,requireNumbers:!0,requireSpecialCharacters:!0}}},API:{REST:{"ai-scanner-api":{endpoint:c.api.baseUrl,region:c.aws.region}}},Storage:{S3:{region:c.aws.region,bucket:"ai-scanner-uploads"}}};l.V.configure(u);let d=()=>new o.QueryClient({defaultOptions:{queries:{retry:3,retryDelay:e=>Math.min(1e3*2**e,3e4),staleTime:3e5,cacheTime:6e5,refetchOnWindowFocus:!1},mutations:{retry:1}}});function p({children:e}){let[t]=(0,a.useState)(()=>d());return n.jsx(o.QueryClientProvider,{client:t,children:(0,n.jsxs)(i._.Provider,{children:[e,n.jsx(s.x7,{position:"top-right",toastOptions:{duration:5e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#10B981",secondary:"#fff"}},error:{duration:5e3,iconTheme:{primary:"#EF4444",secondary:"#fff"}}}})]})})}},6399:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return o},notFound:function(){return n}});let r="NEXT_NOT_FOUND";function n(){let e=Error(r);throw e.digest=r,e}function o(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7352:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{PARALLEL_ROUTE_DEFAULT_PATH:function(){return o},default:function(){return i}});let n=r(6399),o="next/dist/client/components/parallel-route-default.js";function i(){(0,n.notFound)()}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5495:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>a});var n=r(9510),o=r(5384),i=r.n(o);r(5023);let s=(0,r(8570).createProxy)(String.raw`C:\Users\<USER>\Desktop\AWS Task\frontend\src\app\providers.tsx#Providers`),a={title:"AI Security Scanner",description:"AI-powered security and compliance scanning platform",keywords:["security","compliance","AI","scanning","AWS"],authors:[{name:"AWS AI Security Scanner Team"}],viewport:"width=device-width, initial-scale=1",robots:"index, follow",openGraph:{title:"AI Security Scanner",description:"AI-powered security and compliance scanning platform",type:"website",locale:"en_US"},twitter:{card:"summary_large_image",title:"AI Security Scanner",description:"AI-powered security and compliance scanning platform"},icons:{icon:"/favicon.ico",apple:"/apple-touch-icon.png"}};function l({children:e}){return n.jsx("html",{lang:"en",className:"h-full",children:n.jsx("body",{className:`${i().className} h-full`,children:n.jsx(s,{children:e})})})}},5023:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[200],()=>r(6760));module.exports=n})();