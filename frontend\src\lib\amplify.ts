import { Amplify } from 'aws-amplify';
import { config } from './config';

// Amplify configuration
const amplifyConfig = {
  Auth: {
    Cognito: {
      userPoolId: config.aws.userPoolId,
      userPoolClientId: config.aws.userPoolClientId,
      identityPoolId: config.aws.identityPoolId,
      loginWith: {
        email: true,
      },
      signUpVerificationMethod: 'code',
      userAttributes: {
        email: {
          required: true,
        },
        given_name: {
          required: true,
        },
        family_name: {
          required: true,
        },
      },
      allowGuestAccess: false,
      passwordFormat: {
        minLength: 8,
        requireLowercase: true,
        requireUppercase: true,
        requireNumbers: true,
        requireSpecialCharacters: true,
      },
    },
  },
  API: {
    REST: {
      'ai-scanner-api': {
        endpoint: config.api.baseUrl,
        region: config.aws.region,
      },
    },
  },
  Storage: {
    S3: {
      region: config.aws.region,
      bucket: 'ai-scanner-uploads', // This will be dynamically set
    },
  },
};

// Configure Amplify
Amplify.configure(amplifyConfig);

export { amplifyConfig };
export default Amplify;
