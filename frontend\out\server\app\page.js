/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgrady%5CDesktop%5CAWS%20Task%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgrady%5CDesktop%5CAWS%20Task%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgrady%5CDesktop%5CAWS%20Task%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgrady%5CDesktop%5CAWS%20Task%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZwYWdlJnBhZ2U9JTJGcGFnZSZhcHBQYXRocz0lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGcGFnZS50c3gmYXBwRGlyPUMlM0ElNUNVc2VycyU1Q2dyYWR5JTVDRGVza3RvcCU1Q0FXUyUyMFRhc2slNUNmcm9udGVuZCU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9QyUzQSU1Q1VzZXJzJTVDZ3JhZHklNUNEZXNrdG9wJTVDQVdTJTIwVGFzayU1Q2Zyb250ZW5kJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PWV4cG9ydCZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsYUFBYSxzQkFBc0I7QUFDaUU7QUFDckM7QUFDL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDLHVCQUF1QixnSkFBc0c7QUFDN0g7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0EseUJBQXlCLG9KQUF3RztBQUNqSSxvQkFBb0IsME5BQWdGO0FBQ3BHO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUN1QjtBQUM2RDtBQUNwRiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDQTtBQUNQO0FBQ0E7QUFDQTtBQUN1RDtBQUN2RDtBQUNPLHdCQUF3Qiw4R0FBa0I7QUFDakQ7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktc2VjdXJpdHktc2Nhbm5lci1mcm9udGVuZC8/NWE4ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJcIlRVUkJPUEFDSyB7IHRyYW5zaXRpb246IG5leHQtc3NyIH1cIjtcbmltcG9ydCB7IEFwcFBhZ2VSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL2FwcC1wYWdlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbi8vIFdlIGluamVjdCB0aGUgdHJlZSBhbmQgcGFnZXMgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IHRyZWUgPSB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICcnLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbJ19fUEFHRV9fJywge30sIHtcbiAgICAgICAgICBwYWdlOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxncmFkeVxcXFxEZXNrdG9wXFxcXEFXUyBUYXNrXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIiksIFwiQzpcXFxcVXNlcnNcXFxcZ3JhZHlcXFxcRGVza3RvcFxcXFxBV1MgVGFza1xcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCJdLFxuICAgICAgICAgIFxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcZ3JhZHlcXFxcRGVza3RvcFxcXFxBV1MgVGFza1xcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGxheW91dC50c3hcIiksIFwiQzpcXFxcVXNlcnNcXFxcZ3JhZHlcXFxcRGVza3RvcFxcXFxBV1MgVGFza1xcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGxheW91dC50c3hcIl0sXG4nbm90LWZvdW5kJzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiKSwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCJdLFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0uY2hpbGRyZW47XG5jb25zdCBwYWdlcyA9IFtcIkM6XFxcXFVzZXJzXFxcXGdyYWR5XFxcXERlc2t0b3BcXFxcQVdTIFRhc2tcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiXTtcbmV4cG9ydCB7IHRyZWUsIHBhZ2VzIH07XG5leHBvcnQgeyBkZWZhdWx0IGFzIEdsb2JhbEVycm9yIH0gZnJvbSBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeVwiO1xuY29uc3QgX19uZXh0X2FwcF9yZXF1aXJlX18gPSBfX3dlYnBhY2tfcmVxdWlyZV9fXG5jb25zdCBfX25leHRfYXBwX2xvYWRfY2h1bmtfXyA9ICgpID0+IFByb21pc2UucmVzb2x2ZSgpXG5leHBvcnQgY29uc3Qgb3JpZ2luYWxQYXRobmFtZSA9IFwiL3BhZ2VcIjtcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUGFnZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgcGFnZTogXCIvcGFnZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvXCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogXCJcIixcbiAgICAgICAgZmlsZW5hbWU6IFwiXCIsXG4gICAgICAgIGFwcFBhdGhzOiBbXVxuICAgIH0sXG4gICAgdXNlcmxhbmQ6IHtcbiAgICAgICAgbG9hZGVyVHJlZTogdHJlZVxuICAgIH1cbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcGFnZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgrady%5CDesktop%5CAWS%20Task%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgrady%5CDesktop%5CAWS%20Task%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(ssr)/./src/app/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2dyYWR5JTVDJTVDRGVza3RvcCU1QyU1Q0FXUyUyMFRhc2slNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNncmFkeSU1QyU1Q0Rlc2t0b3AlNUMlNUNBV1MlMjBUYXNrJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNncmFkeSU1QyU1Q0Rlc2t0b3AlNUMlNUNBV1MlMjBUYXNrJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwcm92aWRlcnMudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyUHJvdmlkZXJzJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwSkFBMEkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1zZWN1cml0eS1zY2FubmVyLWZyb250ZW5kLz9lMjFmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiUHJvdmlkZXJzXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcZ3JhZHlcXFxcRGVza3RvcFxcXFxBV1MgVGFza1xcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXHByb3ZpZGVycy50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2dyYWR5JTVDJTVDRGVza3RvcCU1QyU1Q0FXUyUyMFRhc2slNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBc0ciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1zZWN1cml0eS1zY2FubmVyLWZyb250ZW5kLz9iMjE5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcZ3JhZHlcXFxcRGVza3RvcFxcXFxBV1MgVGFza1xcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgrady%5C%5CDesktop%5C%5CAWS%20Task%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _aws_amplify_ui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @aws-amplify/ui-react */ \"(ssr)/./node_modules/@aws-amplify/ui-react-core/dist/esm/Authenticator/hooks/useAuthenticator/useAuthenticator.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_LandingPage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/LandingPage */ \"(ssr)/./src/components/LandingPage.tsx\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(ssr)/./src/components/ui/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction HomePage() {\n    const { authStatus, user } = (0,_aws_amplify_ui_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"])((context)=>[\n            context.authStatus,\n            context.user\n        ]);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (authStatus === \"authenticated\" && user) {\n            router.push(\"/dashboard\");\n        }\n    }, [\n        authStatus,\n        user,\n        router\n    ]);\n    if (authStatus === \"configuring\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__.LoadingSpinner, {\n                size: \"lg\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this);\n    }\n    if (authStatus === \"authenticated\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__.LoadingSpinner, {\n                    size: \"lg\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"ml-3 text-gray-600\",\n                    children: \"Redirecting to dashboard...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LandingPage__WEBPACK_IMPORTED_MODULE_3__.LandingPage, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 36,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-query */ \"(ssr)/./node_modules/react-query/es/index.js\");\n/* harmony import */ var _aws_amplify_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @aws-amplify/ui-react */ \"(ssr)/./node_modules/@aws-amplify/ui-react/dist/esm/components/Authenticator/Authenticator.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _aws_amplify_ui_react_styles_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @aws-amplify/ui-react/styles.css */ \"(ssr)/./node_modules/@aws-amplify/ui-react/dist/styles.css\");\n/* harmony import */ var _lib_amplify__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/amplify */ \"(ssr)/./src/lib/amplify.ts\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n\n\n\n// Create a client\nconst createQueryClient = ()=>new react_query__WEBPACK_IMPORTED_MODULE_1__.QueryClient({\n        defaultOptions: {\n            queries: {\n                retry: 3,\n                retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),\n                staleTime: 5 * 60 * 1000,\n                cacheTime: 10 * 60 * 1000,\n                refetchOnWindowFocus: false\n            },\n            mutations: {\n                retry: 1\n            }\n        }\n    });\nfunction Providers({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(()=>createQueryClient());\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_query__WEBPACK_IMPORTED_MODULE_1__.QueryClientProvider, {\n        client: queryClient,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_aws_amplify_ui_react__WEBPACK_IMPORTED_MODULE_6__.Authenticator.Provider, {\n            children: [\n                children,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                    position: \"top-right\",\n                    toastOptions: {\n                        duration: 5000,\n                        style: {\n                            background: \"#363636\",\n                            color: \"#fff\"\n                        },\n                        success: {\n                            duration: 3000,\n                            iconTheme: {\n                                primary: \"#10B981\",\n                                secondary: \"#fff\"\n                            }\n                        },\n                        error: {\n                            duration: 5000,\n                            iconTheme: {\n                                primary: \"#EF4444\",\n                                secondary: \"#fff\"\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\app\\\\providers.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\app\\\\providers.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\app\\\\providers.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/LandingPage.tsx":
/*!****************************************!*\
  !*** ./src/components/LandingPage.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LandingPage: () => (/* binding */ LandingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _aws_amplify_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @aws-amplify/ui-react */ \"(ssr)/./node_modules/@aws-amplify/ui-react/dist/esm/components/Authenticator/Authenticator.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CloudArrowUpIcon_CpuChipIcon_DocumentMagnifyingGlassIcon_LockClosedIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CloudArrowUpIcon,CpuChipIcon,DocumentMagnifyingGlassIcon,LockClosedIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CloudArrowUpIcon_CpuChipIcon_DocumentMagnifyingGlassIcon_LockClosedIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CloudArrowUpIcon,CpuChipIcon,DocumentMagnifyingGlassIcon,LockClosedIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentMagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CloudArrowUpIcon_CpuChipIcon_DocumentMagnifyingGlassIcon_LockClosedIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CloudArrowUpIcon,CpuChipIcon,DocumentMagnifyingGlassIcon,LockClosedIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CloudArrowUpIcon_CpuChipIcon_DocumentMagnifyingGlassIcon_LockClosedIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CloudArrowUpIcon,CpuChipIcon,DocumentMagnifyingGlassIcon,LockClosedIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CloudArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CloudArrowUpIcon_CpuChipIcon_DocumentMagnifyingGlassIcon_LockClosedIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CloudArrowUpIcon,CpuChipIcon,DocumentMagnifyingGlassIcon,LockClosedIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CloudArrowUpIcon_CpuChipIcon_DocumentMagnifyingGlassIcon_LockClosedIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CloudArrowUpIcon,CpuChipIcon,DocumentMagnifyingGlassIcon,LockClosedIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/LockClosedIcon.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ LandingPage auto */ \n\n\n\n\nconst features = [\n    {\n        name: \"Security Scanning\",\n        description: \"Detect malware, vulnerabilities, and suspicious patterns using advanced AI algorithms.\",\n        icon: _barrel_optimize_names_ChartBarIcon_CloudArrowUpIcon_CpuChipIcon_DocumentMagnifyingGlassIcon_LockClosedIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    },\n    {\n        name: \"Compliance Checking\",\n        description: \"Ensure GDPR, HIPAA, PCI-DSS compliance with automated PII detection.\",\n        icon: _barrel_optimize_names_ChartBarIcon_CloudArrowUpIcon_CpuChipIcon_DocumentMagnifyingGlassIcon_LockClosedIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    {\n        name: \"Anomaly Detection\",\n        description: \"Identify unusual patterns and behaviors using machine learning models.\",\n        icon: _barrel_optimize_names_ChartBarIcon_CloudArrowUpIcon_CpuChipIcon_DocumentMagnifyingGlassIcon_LockClosedIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        name: \"Cloud-Native\",\n        description: \"Built on AWS with serverless architecture for maximum scalability.\",\n        icon: _barrel_optimize_names_ChartBarIcon_CloudArrowUpIcon_CpuChipIcon_DocumentMagnifyingGlassIcon_LockClosedIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: \"AI-Powered\",\n        description: \"Leverages Amazon Comprehend, Rekognition, and SageMaker for intelligent analysis.\",\n        icon: _barrel_optimize_names_ChartBarIcon_CloudArrowUpIcon_CpuChipIcon_DocumentMagnifyingGlassIcon_LockClosedIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: \"Enterprise Security\",\n        description: \"End-to-end encryption, IAM controls, and audit logging for enterprise needs.\",\n        icon: _barrel_optimize_names_ChartBarIcon_CloudArrowUpIcon_CpuChipIcon_DocumentMagnifyingGlassIcon_LockClosedIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    }\n];\nconst stats = [\n    {\n        name: \"Files Scanned\",\n        value: \"10,000+\"\n    },\n    {\n        name: \"Threats Detected\",\n        value: \"500+\"\n    },\n    {\n        name: \"Compliance Issues Found\",\n        value: \"1,200+\"\n    },\n    {\n        name: \"Average Scan Time\",\n        value: \"< 2 min\"\n    }\n];\nfunction LandingPage() {\n    const [showAuth, setShowAuth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    if (showAuth) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"sm:mx-auto sm:w-full sm:max-w-md\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                children: \"AI Security Scanner\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Sign in to your account or create a new one\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 sm:mx-auto sm:w-full sm:max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_aws_amplify_ui_react__WEBPACK_IMPORTED_MODULE_8__.Authenticator, {\n                                hideSignUp: false,\n                                components: {\n                                    Header () {\n                                        return null;\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAuth(false),\n                                className: \"text-primary-600 hover:text-primary-500 text-sm font-medium\",\n                                children: \"← Back to home\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n            lineNumber: 60,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"absolute inset-x-0 top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex items-center justify-between p-6 lg:px-8\",\n                    \"aria-label\": \"Global\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex lg:flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xl font-bold text-gray-900\",\n                                children: \"\\uD83D\\uDEE1️ AI Security Scanner\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex lg:flex-1 lg:justify-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAuth(true),\n                                className: \"btn-primary\",\n                                children: \"Sign In\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative isolate px-6 pt-14 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-primary-400 to-secondary-400 opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto max-w-2xl py-32 sm:py-48 lg:py-56\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.h1, {\n                                    className: \"text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8\n                                    },\n                                    children: \"AI-Powered Security & Compliance Scanner\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.p, {\n                                    className: \"mt-6 text-lg leading-8 text-gray-600\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.2\n                                    },\n                                    children: \"Analyze files for security threats, compliance violations, and anomalies using advanced AI and machine learning. Built on AWS cloud infrastructure for enterprise-grade security.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                    className: \"mt-10 flex items-center justify-center gap-x-6\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.4\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowAuth(true),\n                                            className: \"btn-primary btn-lg\",\n                                            children: \"Get Started\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#features\",\n                                            className: \"text-sm font-semibold leading-6 text-gray-900\",\n                                            children: [\n                                                \"Learn more \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    \"aria-hidden\": \"true\",\n                                                    children: \"→\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 28\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-primary-400 to-secondary-400 opacity-30 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-900 py-24 sm:py-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                        className: \"grid grid-cols-1 gap-x-8 gap-y-16 text-center lg:grid-cols-4\",\n                        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                className: \"mx-auto flex max-w-xs flex-col gap-y-4\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: index * 0.1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                        className: \"text-base leading-7 text-gray-300\",\n                                        children: stat.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                        className: \"order-first text-3xl font-semibold tracking-tight text-white sm:text-5xl\",\n                                        children: stat.value\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, stat.name, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"features\",\n                className: \"py-24 sm:py-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-2xl lg:text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-base font-semibold leading-7 text-primary-600\",\n                                    children: \"Comprehensive Analysis\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\",\n                                    children: \"Everything you need to secure your data\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-6 text-lg leading-8 text-gray-600\",\n                                    children: \"Our AI-powered platform provides comprehensive security analysis, compliance checking, and anomaly detection to keep your data safe and compliant.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-4xl\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                className: \"grid max-w-xl grid-cols-1 gap-x-8 gap-y-10 lg:max-w-none lg:grid-cols-2 lg:gap-y-16\",\n                                children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                        className: \"relative pl-16\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: -20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: index * 0.1\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                className: \"text-base font-semibold leading-7 text-gray-900\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-primary-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                            className: \"h-6 w-6 text-white\",\n                                                            \"aria-hidden\": \"true\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    feature.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                className: \"mt-2 text-base leading-7 text-gray-600\",\n                                                children: feature.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, feature.name, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-primary-600\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-24 sm:px-6 sm:py-32 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto max-w-2xl text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold tracking-tight text-white sm:text-4xl\",\n                                children: \"Ready to secure your data?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mx-auto mt-6 max-w-xl text-lg leading-8 text-primary-100\",\n                                children: \"Start scanning your files today with our AI-powered security and compliance platform.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-10 flex items-center justify-center gap-x-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowAuth(true),\n                                    className: \"btn bg-white text-primary-600 hover:bg-gray-50 focus:ring-white\",\n                                    children: \"Get Started Now\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 236,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gray-900\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-6 py-12 md:flex md:items-center md:justify-between lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center space-x-6 md:order-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"Built with AWS cloud services\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 md:order-1 md:mt-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-center text-xs leading-5 text-gray-400\",\n                                children: \"\\xa9 2024 AI Security Scanner. All rights reserved.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n                lineNumber: 258,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\LandingPage.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LandingPage.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/LoadingSpinner.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/LoadingSpinner.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingSpinner: () => (/* binding */ LoadingSpinner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_cn__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/cn */ \"(ssr)/./src/utils/cn.ts\");\n\n\nconst sizeClasses = {\n    sm: \"h-4 w-4\",\n    md: \"h-6 w-6\",\n    lg: \"h-8 w-8\",\n    xl: \"h-12 w-12\"\n};\nconst colorClasses = {\n    primary: \"border-primary-600\",\n    secondary: \"border-secondary-600\",\n    white: \"border-white\"\n};\nfunction LoadingSpinner({ size = \"md\", className, color = \"primary\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_1__.cn)(\"spinner\", sizeClasses[size], colorClasses[color], className),\n        role: \"status\",\n        \"aria-label\": \"Loading\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"sr-only\",\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/LoadingSpinner.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/amplify.ts":
/*!****************************!*\
  !*** ./src/lib/amplify.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   amplifyConfig: () => (/* binding */ amplifyConfig),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var aws_amplify__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! aws-amplify */ \"(ssr)/./node_modules/aws-amplify/dist/esm/initSingleton.mjs\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./config */ \"(ssr)/./src/lib/config.ts\");\n\n\n// Amplify configuration\nconst amplifyConfig = {\n    Auth: {\n        Cognito: {\n            userPoolId: _config__WEBPACK_IMPORTED_MODULE_0__.config.aws.userPoolId,\n            userPoolClientId: _config__WEBPACK_IMPORTED_MODULE_0__.config.aws.userPoolClientId,\n            identityPoolId: _config__WEBPACK_IMPORTED_MODULE_0__.config.aws.identityPoolId,\n            loginWith: {\n                email: true\n            },\n            signUpVerificationMethod: \"code\",\n            userAttributes: {\n                email: {\n                    required: true\n                },\n                given_name: {\n                    required: true\n                },\n                family_name: {\n                    required: true\n                }\n            },\n            allowGuestAccess: false,\n            passwordFormat: {\n                minLength: 8,\n                requireLowercase: true,\n                requireUppercase: true,\n                requireNumbers: true,\n                requireSpecialCharacters: true\n            }\n        }\n    },\n    API: {\n        REST: {\n            \"ai-scanner-api\": {\n                endpoint: _config__WEBPACK_IMPORTED_MODULE_0__.config.api.baseUrl,\n                region: _config__WEBPACK_IMPORTED_MODULE_0__.config.aws.region\n            }\n        }\n    },\n    Storage: {\n        S3: {\n            region: _config__WEBPACK_IMPORTED_MODULE_0__.config.aws.region,\n            bucket: \"ai-scanner-uploads\"\n        }\n    }\n};\n// Configure Amplify\naws_amplify__WEBPACK_IMPORTED_MODULE_1__.DefaultAmplify.configure(amplifyConfig);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (aws_amplify__WEBPACK_IMPORTED_MODULE_1__.DefaultAmplify);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/amplify.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   getApiUrl: () => (/* binding */ getApiUrl),\n/* harmony export */   getRiskLevelColor: () => (/* binding */ getRiskLevelColor),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   isFileSizeAllowed: () => (/* binding */ isFileSizeAllowed),\n/* harmony export */   isFileTypeAllowed: () => (/* binding */ isFileTypeAllowed),\n/* harmony export */   validateConfig: () => (/* binding */ validateConfig)\n/* harmony export */ });\n// Environment configuration\nconst config = {\n    api: {\n        baseUrl: process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3001\",\n        timeout: 30000\n    },\n    aws: {\n        region: process.env.NEXT_PUBLIC_REGION || \"us-east-1\",\n        userPoolId: process.env.NEXT_PUBLIC_USER_POOL_ID || \"\",\n        userPoolClientId: process.env.NEXT_PUBLIC_USER_POOL_CLIENT_ID || \"\",\n        identityPoolId: process.env.NEXT_PUBLIC_IDENTITY_POOL_ID || \"\"\n    },\n    app: {\n        name: \"AI Security Scanner\",\n        version: \"1.0.0\",\n        environment: process.env.NEXT_PUBLIC_ENVIRONMENT || \"development\",\n        isDevelopment: process.env.NEXT_PUBLIC_ENVIRONMENT === \"development\",\n        isProduction: process.env.NEXT_PUBLIC_ENVIRONMENT === \"production\"\n    },\n    upload: {\n        maxFileSize: 50 * 1024 * 1024,\n        allowedTypes: [\n            \"application/pdf\",\n            \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n            \"application/msword\",\n            \"text/plain\",\n            \"text/csv\",\n            \"application/json\",\n            \"application/xml\",\n            \"text/xml\",\n            \"application/vnd.ms-excel\",\n            \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n            \"image/jpeg\",\n            \"image/png\",\n            \"image/gif\",\n            \"image/bmp\",\n            \"image/tiff\"\n        ],\n        allowedExtensions: [\n            \".pdf\",\n            \".docx\",\n            \".doc\",\n            \".txt\",\n            \".csv\",\n            \".json\",\n            \".xml\",\n            \".xls\",\n            \".xlsx\",\n            \".jpg\",\n            \".jpeg\",\n            \".png\",\n            \".gif\",\n            \".bmp\",\n            \".tiff\"\n        ]\n    },\n    scan: {\n        types: [\n            {\n                value: \"security\",\n                label: \"Security Only\",\n                description: \"Scan for security threats and vulnerabilities\"\n            },\n            {\n                value: \"compliance\",\n                label: \"Compliance Only\",\n                description: \"Check for PII and compliance violations\"\n            },\n            {\n                value: \"anomaly\",\n                label: \"Anomaly Only\",\n                description: \"Detect unusual patterns and behaviors\"\n            },\n            {\n                value: \"full\",\n                label: \"Full Scan\",\n                description: \"Complete security, compliance, and anomaly analysis\"\n            }\n        ],\n        statusColors: {\n            pending_upload: \"gray\",\n            uploaded: \"blue\",\n            processing: \"yellow\",\n            completed: \"green\",\n            failed: \"red\",\n            cancelled: \"gray\"\n        },\n        riskColors: {\n            MINIMAL: \"green\",\n            LOW: \"green\",\n            MEDIUM: \"yellow\",\n            HIGH: \"orange\",\n            CRITICAL: \"red\"\n        }\n    },\n    ui: {\n        pageSize: 20,\n        debounceDelay: 300,\n        toastDuration: 5000,\n        animationDuration: 300\n    },\n    features: {\n        enableAnalytics: false,\n        enableNotifications: true,\n        enableDarkMode: false,\n        enableExport: true,\n        enableSharing: false\n    }\n};\n// Validation functions\nconst validateConfig = ()=>{\n    const required = [\n        \"NEXT_PUBLIC_API_URL\",\n        \"NEXT_PUBLIC_USER_POOL_ID\",\n        \"NEXT_PUBLIC_USER_POOL_CLIENT_ID\",\n        \"NEXT_PUBLIC_IDENTITY_POOL_ID\",\n        \"NEXT_PUBLIC_REGION\"\n    ];\n    const missing = required.filter((key)=>!process.env[key]);\n    if (missing.length > 0) {\n        console.warn(\"Missing required environment variables:\", missing);\n        if (config.app.isProduction) {\n            throw new Error(`Missing required environment variables: ${missing.join(\", \")}`);\n        }\n    }\n};\n// Helper functions\nconst getApiUrl = (path)=>{\n    const baseUrl = config.api.baseUrl.replace(/\\/$/, \"\");\n    const cleanPath = path.replace(/^\\//, \"\");\n    return `${baseUrl}/${cleanPath}`;\n};\nconst formatFileSize = (bytes)=>{\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\"\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n};\nconst isFileTypeAllowed = (file)=>{\n    return config.upload.allowedTypes.includes(file.type) || config.upload.allowedExtensions.some((ext)=>file.name.toLowerCase().endsWith(ext));\n};\nconst isFileSizeAllowed = (file)=>{\n    return file.size <= config.upload.maxFileSize;\n};\nconst getRiskLevelColor = (riskLevel)=>{\n    return config.scan.riskColors[riskLevel] || \"gray\";\n};\nconst getStatusColor = (status)=>{\n    return config.scan.statusColors[status] || \"gray\";\n};\n// Initialize configuration validation\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/config.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/cn.ts":
/*!*************************!*\
  !*** ./src/utils/cn.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdXRpbHMvY24udHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTZDO0FBQ0o7QUFFbEMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktc2VjdXJpdHktc2Nhbm5lci1mcm9udGVuZC8uL3NyYy91dGlscy9jbi50cz8xMDBiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gJ2Nsc3gnO1xuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gJ3RhaWx3aW5kLW1lcmdlJztcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSk7XG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/cn.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9e14caf61359\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktc2VjdXJpdHktc2Nhbm5lci1mcm9udGVuZC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/YjA4NiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjllMTRjYWY2MTM1OVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(rsc)/./src/app/providers.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"AI Security Scanner\",\n    description: \"AI-powered security and compliance scanning platform\",\n    keywords: [\n        \"security\",\n        \"compliance\",\n        \"AI\",\n        \"scanning\",\n        \"AWS\"\n    ],\n    authors: [\n        {\n            name: \"AWS AI Security Scanner Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    robots: \"index, follow\",\n    openGraph: {\n        title: \"AI Security Scanner\",\n        description: \"AI-powered security and compliance scanning platform\",\n        type: \"website\",\n        locale: \"en_US\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"AI Security Scanner\",\n        description: \"AI-powered security and compliance scanning platform\"\n    },\n    icons: {\n        icon: \"/favicon.ico\",\n        apple: \"/apple-touch-icon.png\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} h-full`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 40,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AWS Task\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\AWS Task\frontend\src\app\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\AWS Task\frontend\src\app\providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@aws-amplify","vendor-chunks/framer-motion","vendor-chunks/next","vendor-chunks/lodash","vendor-chunks/react-query","vendor-chunks/qrcode","vendor-chunks/xstate","vendor-chunks/pngjs","vendor-chunks/@aws-crypto","vendor-chunks/@smithy","vendor-chunks/uuid","vendor-chunks/@swc","vendor-chunks/@heroicons","vendor-chunks/@xstate","vendor-chunks/use-sync-external-store","vendor-chunks/@babel","vendor-chunks/tslib","vendor-chunks/tailwind-merge","vendor-chunks/react-hot-toast","vendor-chunks/js-cookie","vendor-chunks/goober","vendor-chunks/clsx","vendor-chunks/aws-amplify","vendor-chunks/use-isomorphic-layout-effect","vendor-chunks/encode-utf8","vendor-chunks/dijkstrajs"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgrady%5CDesktop%5CAWS%20Task%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgrady%5CDesktop%5CAWS%20Task%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();