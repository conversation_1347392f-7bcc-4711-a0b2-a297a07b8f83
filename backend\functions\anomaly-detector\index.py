import json
import boto3
import os
import tempfile
import re
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
import structlog
import pandas as pd
import numpy as np
from sklearn.ensemble import IsolationForest
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.preprocessing import StandardScaler
from textblob import TextBlob
import chardet
from PyPDF2 import PdfReader
from docx import Document
import openpyxl

# Configure logging
logger = structlog.get_logger()

# Initialize AWS clients
s3 = boto3.client('s3')
sagemaker_runtime = boto3.client('sagemaker-runtime')
dynamodb = boto3.resource('dynamodb')

# Environment variables
UPLOADS_BUCKET = os.environ['UPLOADS_BUCKET']
RESULTS_TABLE = os.environ['RESULTS_TABLE']
SAGEMAKER_ENDPOINT = os.environ.get('SAGEMAKER_ENDPOINT')

# Anomaly detection thresholds
ANOMALY_THRESHOLDS = {
    'text_length': {'min': 10, 'max': 1000000},
    'line_length': {'min': 1, 'max': 1000},
    'word_frequency': {'threshold': 0.95},
    'character_frequency': {'threshold': 0.95},
    'entropy': {'min': 1.0, 'max': 8.0},
    'sentiment_score': {'min': -1.0, 'max': 1.0}
}

def download_file_from_s3(s3_key: str) -> str:
    """Download file from S3 to temporary location"""
    try:
        temp_file = tempfile.NamedTemporaryFile(delete=False)
        s3.download_fileobj(UPLOADS_BUCKET, s3_key, temp_file)
        temp_file.close()
        return temp_file.name
    except Exception as e:
        logger.error("Failed to download file from S3", s3_key=s3_key, error=str(e))
        raise

def extract_text_from_file(file_path: str, mime_type: str) -> str:
    """Extract text content from various file types"""
    try:
        if mime_type == 'application/pdf':
            return extract_text_from_pdf(file_path)
        elif mime_type in ['application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/msword']:
            return extract_text_from_docx(file_path)
        elif mime_type in ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']:
            return extract_text_from_excel(file_path)
        elif mime_type.startswith('text/'):
            return extract_text_from_text_file(file_path)
        else:
            return extract_text_from_text_file(file_path)
    except Exception as e:
        logger.error("Failed to extract text from file", file_path=file_path, mime_type=mime_type, error=str(e))
        return ""

def extract_text_from_pdf(file_path: str) -> str:
    """Extract text from PDF file"""
    try:
        text = ""
        with open(file_path, 'rb') as file:
            pdf_reader = PdfReader(file)
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
        return text
    except Exception as e:
        logger.error("Failed to extract text from PDF", error=str(e))
        return ""

def extract_text_from_docx(file_path: str) -> str:
    """Extract text from DOCX file"""
    try:
        doc = Document(file_path)
        text = ""
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
        return text
    except Exception as e:
        logger.error("Failed to extract text from DOCX", error=str(e))
        return ""

def extract_text_from_excel(file_path: str) -> str:
    """Extract text from Excel file"""
    try:
        workbook = openpyxl.load_workbook(file_path, data_only=True)
        text = ""
        for sheet_name in workbook.sheetnames:
            sheet = workbook[sheet_name]
            for row in sheet.iter_rows(values_only=True):
                for cell in row:
                    if cell is not None:
                        text += str(cell) + " "
                text += "\n"
        return text
    except Exception as e:
        logger.error("Failed to extract text from Excel", error=str(e))
        return ""

def extract_text_from_text_file(file_path: str) -> str:
    """Extract text from text file"""
    try:
        with open(file_path, 'rb') as f:
            raw_data = f.read(10000)
            encoding = chardet.detect(raw_data).get('encoding', 'utf-8')
        
        with open(file_path, 'r', encoding=encoding, errors='ignore') as file:
            return file.read()
    except Exception as e:
        logger.error("Failed to extract text from text file", error=str(e))
        return ""

def calculate_text_entropy(text: str) -> float:
    """Calculate Shannon entropy of text"""
    try:
        if not text:
            return 0.0
        
        # Count character frequencies
        char_counts = {}
        for char in text:
            char_counts[char] = char_counts.get(char, 0) + 1
        
        # Calculate entropy
        text_length = len(text)
        entropy = 0.0
        for count in char_counts.values():
            probability = count / text_length
            if probability > 0:
                entropy -= probability * np.log2(probability)
        
        return entropy
    except Exception as e:
        logger.error("Failed to calculate entropy", error=str(e))
        return 0.0

def analyze_text_statistics(text: str) -> Dict[str, Any]:
    """Analyze basic text statistics"""
    try:
        lines = text.split('\n')
        words = text.split()
        
        # Basic statistics
        stats = {
            'total_characters': len(text),
            'total_lines': len(lines),
            'total_words': len(words),
            'avg_line_length': np.mean([len(line) for line in lines]) if lines else 0,
            'avg_word_length': np.mean([len(word) for word in words]) if words else 0,
            'entropy': calculate_text_entropy(text)
        }
        
        # Character frequency analysis
        char_freq = {}
        for char in text:
            char_freq[char] = char_freq.get(char, 0) + 1
        
        # Most common characters
        sorted_chars = sorted(char_freq.items(), key=lambda x: x[1], reverse=True)
        stats['most_common_chars'] = sorted_chars[:10]
        
        # Word frequency analysis
        word_freq = {}
        for word in words:
            word_lower = word.lower().strip('.,!?;:"()[]{}')
            word_freq[word_lower] = word_freq.get(word_lower, 0) + 1
        
        # Most common words
        sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        stats['most_common_words'] = sorted_words[:20]
        
        return stats
    except Exception as e:
        logger.error("Failed to analyze text statistics", error=str(e))
        return {}

def detect_statistical_anomalies(stats: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Detect statistical anomalies in text"""
    anomalies = []
    
    try:
        # Check text length anomalies
        total_chars = stats.get('total_characters', 0)
        if total_chars < ANOMALY_THRESHOLDS['text_length']['min']:
            anomalies.append({
                'type': 'text_too_short',
                'severity': 'LOW',
                'description': f'Text is unusually short ({total_chars} characters)',
                'value': total_chars,
                'threshold': ANOMALY_THRESHOLDS['text_length']['min']
            })
        elif total_chars > ANOMALY_THRESHOLDS['text_length']['max']:
            anomalies.append({
                'type': 'text_too_long',
                'severity': 'MEDIUM',
                'description': f'Text is unusually long ({total_chars} characters)',
                'value': total_chars,
                'threshold': ANOMALY_THRESHOLDS['text_length']['max']
            })
        
        # Check line length anomalies
        avg_line_length = stats.get('avg_line_length', 0)
        if avg_line_length > ANOMALY_THRESHOLDS['line_length']['max']:
            anomalies.append({
                'type': 'long_lines',
                'severity': 'MEDIUM',
                'description': f'Average line length is unusually long ({avg_line_length:.1f} characters)',
                'value': avg_line_length,
                'threshold': ANOMALY_THRESHOLDS['line_length']['max']
            })
        
        # Check entropy anomalies
        entropy = stats.get('entropy', 0)
        if entropy < ANOMALY_THRESHOLDS['entropy']['min']:
            anomalies.append({
                'type': 'low_entropy',
                'severity': 'HIGH',
                'description': f'Text has unusually low entropy ({entropy:.2f}), possibly encrypted or compressed',
                'value': entropy,
                'threshold': ANOMALY_THRESHOLDS['entropy']['min']
            })
        elif entropy > ANOMALY_THRESHOLDS['entropy']['max']:
            anomalies.append({
                'type': 'high_entropy',
                'severity': 'HIGH',
                'description': f'Text has unusually high entropy ({entropy:.2f}), possibly random or encrypted',
                'value': entropy,
                'threshold': ANOMALY_THRESHOLDS['entropy']['max']
            })
        
        # Check character frequency anomalies
        most_common_chars = stats.get('most_common_chars', [])
        if most_common_chars:
            top_char_freq = most_common_chars[0][1] / stats.get('total_characters', 1)
            if top_char_freq > ANOMALY_THRESHOLDS['character_frequency']['threshold']:
                anomalies.append({
                    'type': 'character_frequency_anomaly',
                    'severity': 'MEDIUM',
                    'description': f'Single character appears unusually frequently ({top_char_freq:.2%})',
                    'value': top_char_freq,
                    'character': most_common_chars[0][0],
                    'threshold': ANOMALY_THRESHOLDS['character_frequency']['threshold']
                })
        
        # Check word frequency anomalies
        most_common_words = stats.get('most_common_words', [])
        if most_common_words:
            top_word_freq = most_common_words[0][1] / stats.get('total_words', 1)
            if top_word_freq > ANOMALY_THRESHOLDS['word_frequency']['threshold']:
                anomalies.append({
                    'type': 'word_frequency_anomaly',
                    'severity': 'MEDIUM',
                    'description': f'Single word appears unusually frequently ({top_word_freq:.2%})',
                    'value': top_word_freq,
                    'word': most_common_words[0][0],
                    'threshold': ANOMALY_THRESHOLDS['word_frequency']['threshold']
                })
        
        return anomalies
    except Exception as e:
        logger.error("Failed to detect statistical anomalies", error=str(e))
        return []

def analyze_sentiment_anomalies(text: str) -> List[Dict[str, Any]]:
    """Analyze sentiment anomalies using TextBlob"""
    anomalies = []
    
    try:
        # Split text into chunks for analysis
        chunks = [text[i:i+1000] for i in range(0, len(text), 1000)]
        sentiments = []
        
        for chunk in chunks:
            if chunk.strip():
                blob = TextBlob(chunk)
                sentiments.append({
                    'polarity': blob.sentiment.polarity,
                    'subjectivity': blob.sentiment.subjectivity
                })
        
        if not sentiments:
            return anomalies
        
        # Calculate average sentiment
        avg_polarity = np.mean([s['polarity'] for s in sentiments])
        avg_subjectivity = np.mean([s['subjectivity'] for s in sentiments])
        
        # Check for extreme sentiment
        if avg_polarity < -0.8:
            anomalies.append({
                'type': 'extremely_negative_sentiment',
                'severity': 'MEDIUM',
                'description': f'Text has extremely negative sentiment ({avg_polarity:.2f})',
                'value': avg_polarity
            })
        elif avg_polarity > 0.8:
            anomalies.append({
                'type': 'extremely_positive_sentiment',
                'severity': 'LOW',
                'description': f'Text has extremely positive sentiment ({avg_polarity:.2f})',
                'value': avg_polarity
            })
        
        # Check for high subjectivity
        if avg_subjectivity > 0.9:
            anomalies.append({
                'type': 'highly_subjective',
                'severity': 'LOW',
                'description': f'Text is highly subjective ({avg_subjectivity:.2f})',
                'value': avg_subjectivity
            })
        
        return anomalies
    except Exception as e:
        logger.error("Failed to analyze sentiment anomalies", error=str(e))
        return []

def detect_pattern_anomalies(text: str) -> List[Dict[str, Any]]:
    """Detect pattern-based anomalies"""
    anomalies = []
    
    try:
        # Check for repeated patterns
        repeated_patterns = []
        
        # Look for repeated sequences of 10+ characters
        for i in range(len(text) - 20):
            substring = text[i:i+10]
            if substring in text[i+10:]:
                count = text.count(substring)
                if count > 5:  # Appears more than 5 times
                    repeated_patterns.append({
                        'pattern': substring,
                        'count': count,
                        'length': len(substring)
                    })
        
        if repeated_patterns:
            # Sort by count and take top patterns
            top_patterns = sorted(repeated_patterns, key=lambda x: x['count'], reverse=True)[:5]
            anomalies.append({
                'type': 'repeated_patterns',
                'severity': 'MEDIUM',
                'description': f'Found {len(repeated_patterns)} repeated patterns',
                'patterns': top_patterns
            })
        
        # Check for unusual character sequences
        unusual_sequences = re.findall(r'[^\w\s]{5,}', text)
        if unusual_sequences:
            anomalies.append({
                'type': 'unusual_character_sequences',
                'severity': 'MEDIUM',
                'description': f'Found {len(unusual_sequences)} unusual character sequences',
                'sequences': unusual_sequences[:10]  # Limit to first 10
            })
        
        # Check for base64-like patterns
        base64_patterns = re.findall(r'[A-Za-z0-9+/]{20,}={0,2}', text)
        if base64_patterns:
            anomalies.append({
                'type': 'base64_like_patterns',
                'severity': 'HIGH',
                'description': f'Found {len(base64_patterns)} potential base64 encoded strings',
                'patterns': base64_patterns[:5]  # Limit to first 5
            })
        
        return anomalies
    except Exception as e:
        logger.error("Failed to detect pattern anomalies", error=str(e))
        return []

def use_sagemaker_anomaly_detection(text: str) -> List[Dict[str, Any]]:
    """Use SageMaker endpoint for anomaly detection (if available)"""
    anomalies = []
    
    try:
        if not SAGEMAKER_ENDPOINT:
            logger.info("SageMaker endpoint not configured, skipping ML-based anomaly detection")
            return anomalies
        
        # Prepare input for SageMaker
        # This would depend on your specific model
        input_data = {
            'text': text[:5000],  # Limit text length
            'features': {
                'length': len(text),
                'entropy': calculate_text_entropy(text)
            }
        }
        
        response = sagemaker_runtime.invoke_endpoint(
            EndpointName=SAGEMAKER_ENDPOINT,
            ContentType='application/json',
            Body=json.dumps(input_data)
        )
        
        result = json.loads(response['Body'].read().decode())
        
        # Process SageMaker response
        if result.get('anomaly_score', 0) > 0.7:
            anomalies.append({
                'type': 'ml_detected_anomaly',
                'severity': 'HIGH',
                'description': f'ML model detected anomaly (score: {result["anomaly_score"]:.2f})',
                'score': result['anomaly_score'],
                'details': result.get('details', {})
            })
        
        return anomalies
    except Exception as e:
        logger.warning("Failed to use SageMaker for anomaly detection", error=str(e))
        return []

def calculate_anomaly_risk_score(anomalies: List[Dict[str, Any]]) -> Tuple[int, str]:
    """Calculate overall risk score based on anomalies"""
    try:
        severity_weights = {
            'LOW': 1,
            'MEDIUM': 3,
            'HIGH': 5,
            'CRITICAL': 8
        }
        
        total_score = 0
        for anomaly in anomalies:
            severity = anomaly.get('severity', 'LOW')
            weight = severity_weights.get(severity, 1)
            total_score += weight
        
        # Normalize to 0-100 scale
        risk_score = min(total_score * 5, 100)
        
        # Determine risk level
        if risk_score >= 80:
            risk_level = 'CRITICAL'
        elif risk_score >= 60:
            risk_level = 'HIGH'
        elif risk_score >= 40:
            risk_level = 'MEDIUM'
        elif risk_score >= 20:
            risk_level = 'LOW'
        else:
            risk_level = 'MINIMAL'
        
        return risk_score, risk_level
    except Exception as e:
        logger.error("Failed to calculate risk score", error=str(e))
        return 0, 'MINIMAL'

def save_results(scan_id: str, results: Dict[str, Any]) -> None:
    """Save scan results to DynamoDB"""
    try:
        table = dynamodb.Table(RESULTS_TABLE)
        
        item = {
            'scanId': scan_id,
            'resultType': 'anomaly',
            'results': results,
            'timestamp': datetime.utcnow().isoformat(),
            'ttl': int(datetime.utcnow().timestamp()) + (30 * 24 * 60 * 60)  # 30 days TTL
        }
        
        table.put_item(Item=item)
        logger.info("Anomaly detection results saved", scan_id=scan_id)
        
    except Exception as e:
        logger.error("Failed to save results", scan_id=scan_id, error=str(e))
        raise

def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """Main Lambda handler for anomaly detection"""
    logger.info("Anomaly detection started", event=event)
    
    try:
        # Extract parameters from event
        scan_id = event.get('scanId')
        s3_key = event.get('s3Key')
        
        if not scan_id or not s3_key:
            raise ValueError("Missing required parameters: scanId and s3Key")
        
        # Download file from S3
        local_file_path = download_file_from_s3(s3_key)
        
        try:
            # Determine MIME type from S3 metadata
            s3_response = s3.head_object(Bucket=UPLOADS_BUCKET, Key=s3_key)
            mime_type = s3_response.get('ContentType', 'application/octet-stream')
            
            # Extract text content
            text_content = extract_text_from_file(local_file_path, mime_type)
            
            if not text_content.strip():
                logger.warning("No text content extracted from file", scan_id=scan_id)
                text_content = ""
            
            # Analyze text statistics
            text_stats = analyze_text_statistics(text_content)
            
            # Detect various types of anomalies
            statistical_anomalies = detect_statistical_anomalies(text_stats)
            sentiment_anomalies = analyze_sentiment_anomalies(text_content)
            pattern_anomalies = detect_pattern_anomalies(text_content)
            ml_anomalies = use_sagemaker_anomaly_detection(text_content)
            
            # Combine all anomalies
            all_anomalies = statistical_anomalies + sentiment_anomalies + pattern_anomalies + ml_anomalies
            
            # Calculate risk score
            risk_score, risk_level = calculate_anomaly_risk_score(all_anomalies)
            
            # Prepare results
            results = {
                'scanType': 'anomaly',
                'status': 'completed',
                'riskScore': risk_score,
                'riskLevel': risk_level,
                'textStatistics': text_stats,
                'anomalies': {
                    'statistical': statistical_anomalies,
                    'sentiment': sentiment_anomalies,
                    'pattern': pattern_anomalies,
                    'ml_detected': ml_anomalies
                },
                'summary': {
                    'totalAnomalies': len(all_anomalies),
                    'anomalyTypes': list(set(a['type'] for a in all_anomalies)),
                    'highestSeverity': max([a.get('severity', 'LOW') for a in all_anomalies], default='NONE'),
                    'hasStatisticalAnomalies': len(statistical_anomalies) > 0,
                    'hasSentimentAnomalies': len(sentiment_anomalies) > 0,
                    'hasPatternAnomalies': len(pattern_anomalies) > 0,
                    'hasMlAnomalies': len(ml_anomalies) > 0
                },
                'completedAt': datetime.utcnow().isoformat()
            }
            
            # Save results
            save_results(scan_id, results)
            
            logger.info("Anomaly detection completed", 
                       scan_id=scan_id, 
                       risk_score=risk_score, 
                       risk_level=risk_level,
                       total_anomalies=len(all_anomalies))
            
            return results
            
        finally:
            # Clean up temporary file
            try:
                os.unlink(local_file_path)
            except Exception:
                pass
    
    except Exception as e:
        logger.error("Anomaly detection failed", scan_id=scan_id, error=str(e))
        
        error_results = {
            'scanType': 'anomaly',
            'status': 'failed',
            'error': str(e),
            'completedAt': datetime.utcnow().isoformat()
        }
        
        try:
            save_results(scan_id, error_results)
        except Exception:
            pass
        
        raise
